{"name": "background_v4", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve && webpack-dev-server --open", "build:prod": "vue-cli-service build --mode prod", "build:staging": "vue-cli-service build --mode staging", "build:test": "vue-cli-service build --mode test", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "new": "plop", "api": "node shell.config.js", "clean": "node shell.clean.js"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "@packy-tang/vue-tinymce": "^1.1.2", "@riophae/vue-treeselect": "^0.4.0", "@vue-office/docx": "^1.3.0", "@vue/composition-api": "^1.7.2", "axios": "^0.19.2", "clipboard": "^2.0.11", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dayjs": "^1.10.7", "echarts": "^4.9.0", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.13", "file-loader": "^6.2.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "jspdf": "^2.5.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "nprogress": "^0.2.0", "number-precision": "^1.5.1", "qs": "^6.10.1", "save": "^2.9.0", "sortablejs": "^1.14.0", "tinymce": "^5.10.2", "umy-ui": "^1.1.6", "vconsole": "^3.9.1", "vue": "^2.6.11", "vue-barcode": "^1.3.0", "vue-demi": "^0.14.5", "vue-html2pdf": "^1.8.0", "vue-i18n": "^8.26.5", "vue-iframe-print": "^1.0.1", "vue-photo-preview": "^1.1.3", "vue-print-nb": "^1.7.5", "vue-router": "^3.2.0", "vue-virtual-scroll-list": "^2.3.4", "vue-wxlogin": "^1.0.4", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0", "webpack-theme-color-replacer": "^1.4.7", "xlsx": "^0.17.4"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-plugin-unit-jest": "~4.4.0", "@vue/cli-plugin-vuex": "~4.4.0", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^1.0.3", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^4.0.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "js-base64": "^3.7.7", "mockjs": "^1.1.0", "plop": "^2.7.3", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^5.1.1", "prettier": "^2.8.4", "sass": "^1.43.4", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.5", "shelljs": "^0.8.5", "svg-sprite-loader": "^5.0.0", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^3.8.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.7.2", "webpack-merge": "^5.8.0"}}