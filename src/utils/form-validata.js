import { isEmail, isTel, isUserLength5To20, isPassLength8To20, isTelOrPhone, isNum, positiveMoney, money, integer, onePositiveValue, threePositiveValue, oneDecimal } from "./validata"

export const validateEmail = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else {
    if (!isEmail(value)) {
      callback(new Error('邮箱格式错误！'))
    } else {
      callback()
    }
  }
}

export const validateTel = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else {
    if (!isTel(value)) {
      callback(new Error('电话格式错误！'))
    } else {
      callback()
    }
  }
}

export const calidateUser = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else {
    if (!isUserLength5To20(value)) {
      callback(new Error('长度5到20位，只支持数字、大小写英文或下划线组合'))
    } else {
      callback()
    }
  }
}
export const calidatePass = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else {
    if (!isPassLength8To20(value)) {
      callback(new Error('密码长度8~20位，英文加数字'))
    } else {
      callback()
    }
  }
}
/**
 * 校验日期大于今天
 */
export const validateDatePassCurrentDay = (rule, value, callback) => {
  if (!value || value === '长期') {
    return callback()
  } else if (isNum(value)) {
    // 去除一下空格
    var date = value.toString().trim().replace(' ', '')
    // 将他转换成1998/01/01的格式
    if (date.length === 8) {
      date = date.slice(0, 4) + '/' + date.slice(4, 6) + "/" + date.slice(6, date.length)
    } else {
      return callback()
    }
    console.log("validateDatePassCurrentDays", value, date);
    var tempDate = new Date(date).getTime()
    if (isNaN(tempDate)) {
      return callback(new Error('请输入正确的日期'))
    }
    var currentDate = new Date().getTime()
    console.log("validateDatePassCurrentDay", tempDate, currentDate);
    if (tempDate < currentDate) {
      callback(new Error('有效期必须大于当前日期'))
    }
    callback()
  }
  callback(new Error('请输入yyyyMMdd格式的日期'))
}

/**
 * 校验手机跟座机，符合其中一个
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export const validatePhoneAndLandline = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else {
    if (!isTelOrPhone(value)) {
      callback(new Error('电话/座机格式错误！'))
    } else {
      callback()
    }
  }
}

/**
 * @description 校验金额 money 可为任意数字
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export const validataMoney = (rule, value, callback) => {
  if (money(value)) {
    callback()
  } else {
    callback(new Error('金额格式有误'))
  }
}

/**
 * @description 校验金额 money 只能是>=0的数字
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export const validataPositiveMoney = (rule, value, callback) => {
  if (positiveMoney(value)) {
    callback()
  } else {
    callback(new Error('金额格式有误'))
  }
}

/**
 * @description 校验金额 money 只能是>0的数字
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export let validataPositiveNoZeroMoney = (rule, value, callback) => {
  if (value === '') {
    return callback(new Error('请输入金额'))
  }
  if (value) {
    if (positiveMoney(value)) {
      callback()
    } else {
      callback(new Error('金额格式有误'))
    }
  } else {
    callback(new Error('金额格式有误'))
  }
}

/**
 * [description] 月份可非必填
 * @param  {[type]}   rule     [description]
 * @param  {[type]}   value    [description]
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
export const validateMonth = (rule, value, callback) => {
  if (value === '') {
    return callback()
  } else {
    if (!integer(value)) {
      callback(new Error('格式错误'))
    } else {
      if (Number(value) === 0) {
        callback(new Error('月份必须大于0'))
      } else if (Number(value) > 12) {
        callback(new Error('不能大于12月'))
      } else {
        callback()
      }
    }
  }
}

/**
 * @description 校验重量g 只能是>=0的数字，可保护2位小数
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export const validataPositiveWeight = (rule, value, callback) => {
  if (positiveMoney(value)) {
    callback()
  } else {
    callback(new Error('请输入>=0的数字，可包含2位小数'))
  }
}

/**
 * [description]  校验正整数,必须大于0
 * @param  {[type]}   rule     [description]
 * @param  {[type]}   value    [description]
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
export const validateNumber = (rule, value, callback) => {
  if (value === '') {
    return callback(new Error('不能为空'))
  } else {
    if (!integer(value)) {
      callback(new Error('请输入数字'))
    } else {
      if (Number(value) === 0) {
        callback(new Error('请输入大于0的数字'))
      } else {
        callback()
      }
    }
  }
}

/**
 * @description 校验大于0的最多2位数字可保留一位小数
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export const validateNotZeroOnePositive = (rule, value, callback) => {
  if (value) {
    if (Number(value) === 0) {
      callback(new Error('请输入大于0的数字！'))
    } else if (!onePositiveValue(value)) {
      callback(new Error('最多2位数字可保留一位小数!'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入！'))
  }
}

/**
 * @description 校验大于0的最多1位数字可保留3位小数
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export const validateNotZeroThreePositive = (rule, value, callback) => {
  if (value) {
    if (Number(value) === 0) {
      callback(new Error('请输入大于0的数字！'))
    } else if (!threePositiveValue(value)) {
      callback(new Error('最多1位数字可保留3位小数!'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入！'))
  }
}

/**
 * [description]  校验正整数,大于等于0
 * @param  {[type]}   rule     [description]
 * @param  {[type]}   value    [description]
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
export const validateStock = (rule, value, callback) => {
  if (value === '') {
    return callback(new Error('不能为空'))
  } else {
    if (!integer(value)) {
      callback(new Error('请输入数字'))
    } else {
      callback()
    }
  }
}

// >0，最多只能包含一位小数
export const validateOneDecimalCount = (rule, value, callback) => {
  if (value) {
    if (!oneDecimal(value) || Number(value) === 0) {
      callback(new Error('格式错误'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入必填项'))
  }
}

// 只允许输入数字
export const validateNumberPosAndNeg = (rule, value, callback) => {
  if (value) {
    if (!isNum(value)) {
      callback(new Error('请输入数字'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
