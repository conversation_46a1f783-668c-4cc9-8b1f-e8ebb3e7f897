// 兼容 ie
// https://cli.vuejs.org/zh/guide/browser-compatibility.html#browserslist
// https://juejin.cn/post/6969027420217311269
// https://juejin.cn/post/6977643173195710478
// https://www.cnblogs.com/chun321/p/13070553.html
import 'core-js/stable'
import 'regenerator-runtime/runtime'

import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/fonts/iconfont.css'
import '@/assets/fonts/iconfont.js'

// import Cookies from 'js-cookie'
import i18n from './lang' // lang
import './permission'
import apis from '@/api'
import { sleep, encodeQuery, decodeQuery, to } from '@/utils'
import { closeCurrentTab, changehash, backVisitedViewsPath } from '@/utils/layout-tab'
import { computedTotalPageSize } from '@/utils/pagination'
import { needCloseDialog } from '@/utils/responseUtils'

import Message from '@/components/message/index.js'

// global filters
// eslint-disable-next-line no-unused-vars
import filters from './filters'

// 自定义指令
import Directives from './directives'

// 混入
import mixins from './mixins'

// 引入echarts
import echarts from 'echarts'

// import Vconsole from 'vconsole';

import eventBus from './utils/eventBus'

// import Throttle from '../Throttle'
// Vue.component('Throttle', Throttle)

// 自动注册全局组件
import '@/components/componentRegister.js'
import TreeSelect from '@/components/TreeSelect'

// svg
import './icons'

// 注册组件，暂时不用这种方式
// import installPlugin from '@/plugin'
// installPlugin(Vue)

// 富文本Tinymce
import VueTinymce from '@packy-tang/vue-tinymce'

// 错误捕获
// Vue.config.errorHandler = (error, vm, mes) => {
//   let info = {
//     type: 'script',
//     code: 0,
//     mes: error.message,
//     url: window.location.href
//   }
//   console.error(info)
// }
// 主题换肤
import { initThemeColor } from './utils/themeColorClient'
import drag from './utils/drag'
// umy-ui表格
import { UTable, UTableColumn } from 'umy-ui';

import VuePhotoPreview from 'vue-photo-preview'
// 百度统计
import '@/utils/hm_baidu'
import 'vue-photo-preview/dist/skin.css'

initThemeColor();

if (process.env.NODE_ENV === 'development') {
  // new Vconsole();
}

Vue.prototype.$echarts = echarts
Vue.use(ElementUI, {
  // size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})
Vue.use(drag)

Vue.use(filters)
Vue.use(Directives)
Vue.use(mixins)
Vue.use(eventBus)
Vue.use(VueTinymce)
Vue.use(TreeSelect)
Vue.use(VuePhotoPreview /* 插件选项 */)

Vue.config.productionTip = false

Vue.prototype.$apis = apis
Vue.prototype.$sleep = sleep
Vue.prototype.$closeCurrentTab = closeCurrentTab
Vue.prototype.$computedTotalPageSize = computedTotalPageSize
Vue.prototype.$needCloseDialog = needCloseDialog
Vue.prototype.$pmessage = Message
Vue.prototype.$encodeQuery = encodeQuery
Vue.prototype.$decodeQuery = decodeQuery
Vue.prototype.$to = to
Vue.prototype.$changehash = changehash
Vue.prototype.$backVisitedViewsPath = backVisitedViewsPath

Vue.component(UTable.name, UTable);
Vue.component(UTableColumn.name, UTableColumn);

// 错误捕获
// Vue.config.errorHandler = (error, vm, mes) => {
//   let info = {
//     type: 'script',
//     code: 0,
//     mes: error.message,
//     url: window.location.href
//   }
//   console.error(info)
// }
new Vue({
  router,
  store,
  i18n,
  data: {
    eventHub: new Vue()
  },
  render: h => h(App)
}).$mount('#app')
