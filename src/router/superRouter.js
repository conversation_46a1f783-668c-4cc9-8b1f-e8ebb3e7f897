// import Layout from '@/layout'
// 注意layout一定要最后生成再替换成import的方式引入，不然会触发页面重复渲染
let Layout = 'Layout'

const system = [
  {
    path: '/super_employee_management',
    component: Layout,
    redirect: '/super_employee_management/department',
    alwaysShow: true,
    name: 'SuperUserManagement',
    meta: {
      title: 'user_management',
      icon: 'user_1',
      permission: ['admin_user_management']
    },
    children: [
      {
        path: 'department',
        component: () =>
          import(
            /* webpackChunkName: "super_department" */ '@/views/super/employee-admin/Department'
          ),
        name: 'SuperDepartment',
        meta: {
          noCache: true,
          title: 'department_management',
          permission: ['background.admin.department.list']
        }
      },
      {
        path: 'role',
        component: () =>
          import(/* webpackChunkName: "super_role" */ '@/views/super/employee-admin/RoleList'),
        name: 'SuperRoleList',
        meta: {
          noCache: true,
          title: 'role_management',
          permission: ['background.admin.role']
        }
      },
      {
        path: 'role/setting',
        component: () =>
          import(
            /* webpackChunkName: "super_roleSetting" */ '@/views/super/employee-admin/RoleSetting'
          ),
        name: 'SuperRoleSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'role_setting',
          activeMenu: '/super_employee_management/role',
          permission: ['background.admin.role.set_permission']
        }
      },
      {
        path: 'account',
        component: () =>
          import(
            /* webpackChunkName: "super_account" */ '@/views/super/employee-admin/AccountList'
          ),
        name: 'SuperAccountList',
        meta: {
          noCache: true,
          title: 'account_management',
          permission: ['background.admin.account']
        }
      }
    ]
  },
  {
    path: '/super_system',
    component: Layout,
    redirect: '/super_system/loglist',
    alwaysShow: true,
    name: 'SuperSystem',
    meta: {
      title: 'merchant_system',
      icon: 'system',
      permission: ['admin_system']
    },
    children: [
      {
        path: 'loglist',
        component: () =>
          import(/* webpackChunkName: "super_loglist" */ '@/views/public/log/OperatingLogList'),
        name: 'LogList',
        meta: {
          noCache: true,
          title: 'list_log',
          permission: ['background.admin.log.list_log']
        }
      },
      {
        path: 'notice_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_admin" */ '@/views/super/system-settings/NoticeAdmin'
          ),
        name: 'SuperNoticeAdmin',
        meta: {
          noCache: true,
          title: 'notice_admin',
          permission: ['background.admin.messages']
        }
      },
      {
        path: 'update_record',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_admin" */ '@/views/super/system-settings/UpdateRecord'
          ),
        name: 'SuperUpdateRecord',
        meta: {
          noCache: true,
          title: 'update_record',
          no_permission: true,
          permission: ['']
        }
      },
      {
        path: 'notice-:type',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_add" */ '@/views/super/system-settings/NoticeAdd'
          ),
        name: 'SuperNoticeAdd',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice',
          activeMenu: '/super_system/notice_admin'
          // permission: ['admin_merchant_management']
        }
      },
      {
        path: 'notice_detail',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_detail" */ '@/views/super/system-settings/NoticeDetail'
          ),
        name: 'SuperNoticeDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice',
          activeMenu: '/super_system/notice_admin'
          // permission: ['admin_merchant_management']
        }
      },
      // 满意度
      {
        path: 'satisfaction_setting',
        name: 'MerchantSatisfactionSetting',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/merchant/system/satisfaction/SatisfactionSetting'
          ),
        meta: {
          noCache: true,
          title: 'satisfaction_setting',
          // no_permission: true
          permission: ['background.admin.satisfaction.get_config_list']
        }
      },
      {
        path: 'satisfaction_total',
        name: 'MerchantSatisfactionTotal',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/merchant/system/satisfaction/SatisfactionTotal'
          ),
        meta: {
          noCache: true,
          title: 'satisfaction_total',
          // no_permission: true
          permission: ['background.admin.satisfaction.statistics']
        }
      },
      {
        path: 'satisfaction_detail',
        name: 'MerchantSatisfactionDetail',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/merchant/system/satisfaction/SatisfactionDetail'
          ),
        meta: {
          noCache: true,
          title: 'satisfaction_detail',
          // no_permission: true
          permission: ['background.admin.satisfaction.detail_list']
        }
      },
      {
        path: 'scale_recognition_statistics',
        name: 'ScaleRecognitionStatistics',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/super/system-settings/ScaleRecognitionStatistics'
          ),
        meta: {
          noCache: true,
          title: 'scale_recognition_statistics',
          // no_permission: true
          permission: ['background.admin.recognition_rate_statistics']
        }
      }
    ]
  },
  {
    path: '/charge_management',
    redirect: '/charge_management/toll_rule',
    component: Layout,
    name: 'ChargeManagement',
    alwaysShow: true,
    meta: {
      icon: 'system',
      title: 'charge_management',
      permission: ['admin_system']
    },
    children: [
      {
        path: 'toll_rule',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/ChargingRules'
          ),
        name: 'ChargingRules',
        meta: {
          noCache: true,
          title: 'charging_rules',
          permission: ['background.admin.background_toll_rule']
        }
      },
      {
        path: 'toll',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/ChargeTrail'
          ),
        name: 'ChargeTrail',
        meta: {
          noCache: true,
          title: 'charging_trail',
          permission: ['background.admin.background_toll']
        }
      },
      {
        path: 'toll_order',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/ChargeOrder'
          ),
        name: 'ChargeOrder',
        meta: {
          noCache: true,
          title: 'charge_order',
          permission: ['background.admin.background_toll_order.list']
        }
      },
      {
        path: 'version_configuration',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/VersionConfiguration'
          ),
        name: 'VersionConfiguration',
        meta: {
          noCache: true,
          title: 'version_configuration',
          no_permission: true,
          permission: ['background.admin.background_toll_rule']
        }
      }
    ]
  },
  {
    path: '/operations-system',
    component: Layout,
    redirect: '/operations-system/member_center/member_list',
    alwaysShow: true,
    name: 'OperationsManagement',
    meta: {
      title: 'operations_management',
      icon: '',
      permission: ['admin_operation_management']
      // no_permission: true
    },
    children: [
      {
        path: 'member_center',
        redirect: '/operations-system/member_center/member_list',
        name: 'SuperMemberCenter',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'member_center',
          no_permission: true
          // permission: ['background_healthy.admin.label_group']
        },
        children: [
          {
            path: 'member_list',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberList'
              ),
            name: 'SuperMemberList',
            meta: {
              noCache: true,
              title: 'member_list',
              permission: ['background_member.member_user.list']
            }
          },
          {
            path: 'member_detail',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberDetail'
              ),
            name: 'SuperMemberDetail',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_detail',
              activeMenu: '/super_health_system/member_center/member_list',
              permission: ['background_member.member_user.list']
            }
          },
          {
            path: 'member_level',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberLevel'
              ),
            name: 'SuperMemberLevel',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_level',
              permission: ['background_member.member_grade.list']
            }
          },
          {
            path: 'member_level/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberLevel'
              ),
            name: 'SuperAddOrEditMemberLevel',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_level',
              activeMenu: '/super_health_system/member_center/member_level',
              permission: ['background_member.member_grade.list']
            }
          },
          {
            path: 'member_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberLabel'
              ),
            name: 'SuperMemberLabel',
            meta: {
              noCache: true,
              title: 'member_label',
              permission: ['background_member.member_label.list']
            }
          },
          {
            path: 'member_label/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberLabel'
              ),
            name: 'SuperAddOrEditMemberLabel',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_label',
              activeMenu: '/super_health_system/member_center/member_label',
              permission: ['background_member.member_label.list']
            }
          },
          {
            path: 'member_permission',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberPermission'
              ),
            name: 'SuperMemberPermission',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_permission',
              permission: ['background_member.member_permission.list']
            }
          },
          {
            path: 'member_permission/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberPermission'
              ),
            name: 'SuperAddOrEditMemberPermission',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_permission',
              activeMenu: '/super_health_system/member_center/member_permission',
              permission: ['background_member.member_permission.list']
            }
          },
          {
            path: 'member_charge_rule_index',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberChargeRuleIndex'
              ),
            name: 'SuperMemberChargeRule',
            meta: {
              noCache: true,
              title: 'member_charge_rule',
              permission: ['background_member.member_charge_rule.list']
            }
          },
          {
            path: 'member_charge_rule_detail',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberChargeRuleDetail'
              ),
            name: 'SuperMemberChargeRuleDetail',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_charge_rule_detail',
              permission: ['background_member.member_charge_rule.list']
            }
          },
          {
            path: 'member_receive_record',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberReceiveRecord'
              ),
            name: 'SuperMemberReceiveRecord',
            meta: {
              noCache: true,
              title: 'member_receive_record',
              permission: ['background_member.member_receive.list']
            }
          },
          {
            path: 'member_exclusive_rights',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberExclusiveRightsList'
              ),
            name: 'MemberExclusiveRightsList',
            meta: {
              noCache: true,
              title: 'member_exclusive_rights',
              permission: ['background_member.rights_setting.list']
            }
          },
          {
            path: 'member_exclusive_setting',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberExclusiveSetting'
              ),
            name: 'MemberExclusiveSetting',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_exclusive_setting',
              permission: ['background_member.rights_setting.list']
            }
          },
          {
            path: 'member_permission_manager',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberPermissionManager'
              ),
            name: 'MemberPermissionManager',
            meta: {
              noCache: true,
              title: 'member_permission_manager',
              permission: ['background_member.member_permission.list']
            }
          },
          {
            path: 'member_key_manager',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberKeyManager'
              ),
            name: 'MemberKeyManager',
            hidden: true,
            meta: {
              noCache: true,
              title: 'member_key_manager',
              permission: ['background_member.member_permission.list']
            }
          },
          {
            path: 'promotional_picture_setting_list',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/PromotionalPictureSettingList'
              ),
            name: 'PromotionalPictureSettingList',
            meta: {
              noCache: true,
              title: 'promotional_picture_setting_list',
              permission: ['background_member.promotional_picture.list']
            }
          },
          {
            path: 'promotional_picture_setting',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/PromotionalPictureSetting'
              ),
            name: 'PromotionalPictureSetting',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/member_center/promotional_picture_setting_list',
              title: 'promotional_picture_setting',
              permission: ['background_member.promotional_picture.list']
            }
          }
        ]
      },

      {
        path: 'points_admin',
        redirect: '/operations-system/points_admin/points_order',
        name: 'pointsAdmin',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'points_admin',
          no_permission: true
          // permission: ['background_member.sms_push_receive.list']
          // permission: ['background_healthy.admin.label_group']
        },
        children: [
          {
            path: 'points_order',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/points-admin/PointsOrder'
              ),
            name: 'pointsOrder',
            meta: {
              noCache: true,
              title: 'points_order'
              // permission: ['background_member.sms_push_receive.list']
            }
          },
          {
            path: 'points_commodity',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/points-admin/PointsCommodity'
              ),
            name: 'pointsCommodity',
            meta: {
              noCache: true,
              title: 'points_commodity'
              // permission: ['background_member.sms_push_receive.list']
            }
          },
          {
            path: 'points_task',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/points-admin/pointsTask'
              ),
            name: 'pointsTask',
            meta: {
              noCache: true,
              title: 'points_task'
              // permission: ['background_member.sms_push_receive.list']
            }
          }
        ]
      },

      {
        path: 'sms_manager',
        redirect: '/operations-system/sms_manager/sms_call_record',
        name: 'SmsManager',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'sms_manager',
          no_permission: true,
          permission: ['background_member.sms_push_receive.list']
          // permission: ['background_healthy.admin.label_group']
        },
        children: [
          {
            path: 'sms_call_record',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberSmsCallRecord'
              ),
            name: 'smsCallRecord',
            meta: {
              noCache: true,
              title: 'sms_call_record',
              permission: ['background_member.sms_push_receive.list']
            }
          }
        ]
      },
      // 功能迭代，超管不给看评价
      // {
      //   path: 'evaluate_list',
      //   component: () =>
      //     import(/* webpackChunkName: "evaluate_list" */ '@/views/super/operations-management/EvaluateList'),
      //   name: 'SuperOperationsManagementEvaluateList',
      //   meta: {
      //     noCache: true,
      //     title: 'evaluate_list',
      //     permission: ['admin_operation_management']
      //   }
      // },
      {
        path: 'feedback',
        component: () =>
          import(/* webpackChunkName: "feedback" */ '@/views/super/operations-management/feedback'),
        name: 'SuperOperationsManagementFeedback',
        meta: {
          noCache: true,
          title: 'feedback',
          permission: ['admin_operation_management']
        }
      },
      // 功能迭代，去除
      // {
      //   path: 'setting',
      //   component: () =>
      //     import(/* webpackChunkName: "operations_setting" */ '@/views/super/operations-management/setting'),
      //   name: 'SuperOperationsManagementSetting',
      //   meta: {
      //     noCache: true,
      //     title: 'operations_setting',
      //     permission: ['admin_operation_management']
      //   }
      // },
      {
        path: 'marketing',
        redirect: '/operations-system/marketing/banner',
        name: 'SuperIngredientsLibraryx',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'marketing',
          permission: ['admin_operation_management']
        },
        children: [
          {
            path: 'banner',
            component: () =>
              import(/* webpackChunkName: "super_ingredients_library" */ '@/views/super/operations-management/marketing/banner/Banner'),
            name: 'SuperBanner',
            meta: {
              noCache: true,
              title: 'banner_setting',
              permission: ['admin_operation_management']
            }
          },
          {
            path: 'add_banner/:type',
            component: () =>
              import(/* webpackChunkName: "super_add_banner" */ '@/views/super/operations-management/marketing/banner/AddBanner'),
            name: 'SuperAddBanner',
            hidden: true,
            meta: {
              noCache: true,
              title: 'banner',
              activeMenu: '/operations-system/marketing/banner',
              permission: ['admin_operation_management']
            }
          },
          {
            path: 'mobile_popup',
            component: () =>
              import(/* webpackChunkName: "super_mobile_popup" */ '@/views/super/operations-management/marketing/popup/MobilePopup'),
            name: 'SuperMobilePopup',
            meta: {
              noCache: true,
              title: 'mobile_popup',
              permission: ['admin_operation_management']
            }
          },
          {
            path: 'add_mobile_popup/:type',
            component: () =>
              import(/* webpackChunkName: "super_add_mobile_popup" */ '@/views/super/operations-management/marketing/popup/AddMobilePopup'),
            name: 'SuperAddMobilePopup',
            hidden: true,
            meta: {
              noCache: true,
              title: 'mobile_popup',
              activeMenu: '/operations-system/marketing/mobile_popup',
              permission: ['admin_operation_management']
            }
          }
        ]
      }
    ]
  },
  {
    path: '/operations-system/guoxun',
    component: Layout,
    redirect: '/operations-system/guoxun/sms',
    alwaysShow: true,
    name: 'GuoxunSmsManagement',
    meta: {
      title: 'guoxun_sms_management',
      icon: '',
      permission: ['admin_operation_management']
      // no_permission: true
    },
    children: [
      {
        path: 'sms',
        component: () =>
          import(/* webpackChunkName: "sms_template" */ '@/views/super/operations-management/guoxun-management'),
        name: 'SuperGuoxunSmsManagement',
        meta: {
          noCache: true,
          title: 'sms_template',
          permission: ['admin_operation_management']
        }
      }
    ]
  },
  {
    path: '/super_merchant',
    component: Layout,
    redirect: '/super_merchant/organization',
    alwaysShow: true,
    name: 'SuperMerchantManagement',
    meta: {
      title: 'admin_merchant_management',
      icon: 'org_s',
      permission: ['admin_merchant_management']
    },
    children: [
      {
        path: 'organization',
        component: () =>
          import(
            /* webpackChunkName: "super_organization" */ '@/views/super/merchant-admin/organization'
          ),
        name: 'SuperOrganizationAdmin',
        meta: {
          noCache: true,
          title: 'organizationAdmin',
          permission: ['admin_merchant_management']
        }
      },
      {
        path: 'agreement_list',
        component: () =>
          import(
            /* webpackChunkName: "super_agreementlist" */ '@/views/super/merchant-admin/AgreementList'
          ),
        name: 'SuperAgreementList',
        meta: {
          noCache: true,
          title: 'agreement_list',
          permission: ['background.admin.agreement.list']
        }
      },
      {
        path: 'agreement_record',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/AgreementRecord'
          ),
        name: 'SuperAgreementRecord',
        meta: {
          noCache: true,
          title: 'agreement_record',
          permission: ['background.admin.agreement_record']
        }
      },
      {
        path: 'face_traceback',
        component: () =>
          import(
            '@/views/super/merchant-admin/FaceTraceback'
          ),
        name: 'FaceTraceback',
        meta: {
          title: 'face_traceback',
          permission: ['background.admin.face_traceback']
        }
      },
      {
        path: 'face_traceback_detail',
        component: () =>
          import(
            '@/views/super/merchant-admin/FaceTracebackDetail'
          ),
        name: 'FaceTracebackDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'face_traceback_detail',
          permission: ['background.admin.face_traceback']
        }
      },
      {
        path: 'bank_merchant',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/BankMerchantManage'
          ),
        name: 'BankMerchantManage',
        meta: {
          noCache: true,
          title: 'bank_merchant',
          permission: ['background.sub_merchant_info']
        }
      },
      {
        path: 'add_bank_merchant/:type',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/BankMerchantManageDetail'
          ),
        name: 'BankMerchantManageDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/super_merchant/bank_merchant',
          title: 'bank_merchant_detail',
          permission: ['background.sub_merchant_info']
        }
      },
      {
        path: 'merchant_banner',
        component: () =>
          import(
            /* webpackChunkName: "super_merchant_banner" */ '@/views/super/merchant-admin/merchant-banner/MerchantBanner'
          ),
        name: 'SuperMerchantBanner',
        meta: {
          noCache: true,
          title: 'super_merchant_banner',
          permission: ['background.admin.merchant_banner.list']
        }
      },
      {
        path: 'add_merchant_banner/:type',
        component: () =>
          import(
            /* webpackChunkName: "super_add_merchant_management" */ '@/views/super/merchant-admin/merchant-banner/AddMerchantBanner'
          ),
        name: 'SuperAddMerchantBanner',
        hidden: true,
        meta: {
          noCache: true,
          title: 'super_merchant_banner',
          activeMenu: '/super_merchant/merchant_banner',
          permission: ['background.admin.merchant_banner.add']
        }
      },
      {
        path: 'merchant_channel_manager',
        component: () =>
          import(
            /* webpackChunkName: "merchant_channel_manager" */ '@/views/super/merchant-admin/ChannelManagementSuper'
          ),
        name: 'ChannelManagementSuper',
        meta: {
          noCache: true,
          title: 'super_merchant_channel',
          // permission: ['background_channel.admin_channel']
          permission: ['background.admin.channel']
        }
      },
      {
        path: 'report_verify_handle',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/ReportVerifyHandle'
          ),
        name: 'ReportVerifyHandle',
        meta: {
          noCache: true,
          title: 'report_verify_handle',
          no_permission: true
          // permission: ['background.sub_merchant_info']
        }
      },
      {
        path: 'nutrition_orders',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/merchant-admin/NutritionOrders'
          ),
        name: 'NutritionOrders',
        meta: {
          noCache: true,
          title: 'nutrition_orders',
          no_permission: true
          // permission: ['background.sub_merchant_info']
        }
      },
      {
        path: 'history_record',
        component: () =>
          import(
            /* webpackChunkName: "SuperHistoryRecord" */ '@/views/super/merchant-admin/HistoryRecord'
          ),
        name: 'SuperHistoryRecord',
        meta: {
          noCache: true,
          title: 'history_record',
          permission: ['background.log.operation_history_list']
        }
      }
    ]
  },
  {
    path: '/supplier',
    component: Layout,
    redirect: '/supplier/supplier_management',
    alwaysShow: true,
    name: 'AdminSuperManagement',
    meta: {
      title: 'supplier_management',
      permission: ['background.admin.supplier_manage']
    },
    children: [
      // 供应商
      {
        path: 'supplier_management',
        component: () =>
          import(
            /* webpackChunkName: "admin_supplier_management" */ '@/views/super/merchant-admin/supplier-management/SupplierManagement'
          ),
        name: 'adminSupplierManagement',
        meta: {
          noCache: true,
          activeMenu: '/super_merchant/supplier/supplier_management',
          title: 'supplier_management_info',
          permission: ['background.admin.supplier_manage']
        }
      }
    ]
  },
  {
    path: '/supervise_fund_management',
    component: Layout,
    redirect: '/supervise_fund_management/supervision_channel',
    alwaysShow: true,
    name: 'SuperLargeScreenConfiguration',
    meta: {
      title: 'supervise_fund_management',
      permission: ['background.admin.monitoring_screen']
    },
    children: [
      {
        path: 'supervision_channel',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/merchant-admin/SupervisionChannel'
          ),
        name: 'SupervisionChannel',
        meta: {
          noCache: true,
          title: 'supervision_channel',
          permission: ['background.admin.supervision_channel']
        }
      },
      {
        path: 'super_large_screen_configuration',
        component: () =>
          import(
            /* webpackChunkName: "super_organization" */ '@/views/super/merchant-admin/LargeScreenConfiguration'
          ),
        name: 'SuperLargeScreenConfigurationList',
        meta: {
          noCache: true,
          title: 'large_screen_configuration',
          permission: ['background.admin.monitoring_screen']
        }
      }
    ]
  },
  {
    path: '/jimu_statement',
    component: Layout,
    redirect: '/jimu_statement/index',
    alwaysShow: false,
    name: 'SuperJimuStatement',
    meta: {
      title: 'jimu_statement',
      permission: ['background.admin.monitoring_screen']
    },
    children: [
      {
        path: 'index',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/jimu-statement/JiMuStatement'
          ),
        name: 'SuperJimu',
        meta: {
          noCache: true,
          title: 'jimu_statement',
          no_permission: true
        }
      },
      {
        path: 'report_application',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/jimu-statement/ReportApplication'
          ),
        name: 'SuperReportApplication',
        meta: {
          noCache: true,
          title: 'report_application',
          no_permission: true
        }
      }
    ]
  },
  {
    path: '/super_user_admin',
    component: Layout,
    redirect: '/super_user_admin/list',
    alwaysShow: true,
    name: 'SuperUserAdmin',
    meta: {
      title: 'user_admin',
      icon: 'user',
      permission: ['background.admin.user.user_list']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(/* webpackChunkName: "super_user_admin" */ '@/views/super/user-admin/UserAdmin'),
        name: 'SuperUserAdminList',
        meta: {
          noCache: true,
          title: 'user_admin_list',
          permission: ['background.admin.user.user_list']
        }
      },
      {
        path: 'list_detail/:id',
        component: () =>
          import(
            /* webpackChunkName: "super_user_detail" */ '@/views/super/user-admin/UserAdminDetail'
          ),
        name: 'SuperUserAdminDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'user_admin_list_detail',
          activeMenu: '/super_user_admin/list',
          permission: ['background.admin.user.user_list']
        }
      }
    ]
  },
  {
    path: '/super_device_admin',
    component: Layout,
    redirect: '/super_device_admin/active_code_admin',
    alwaysShow: true, // will always show the root menu
    name: 'SuperDevice',
    meta: {
      title: 'active_code_admin',
      icon: 'code',
      permission: ['admin_device']
    },
    children: [
      {
        path: 'active_code_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_active_code" */ '@/views/super/device-admin/ActiveCodeAdmin'
          ),
        name: 'SuperActiveCodeAdmin',
        meta: {
          noCache: true,
          title: 'active_code_admin',
          permission: ['background.admin.device.device_info']
        }
      },
      {
        path: 'device_ceil_set',
        component: () =>
          import(
            /* webpackChunkName: "device_ceil_set" */ '@/views/super/device-admin/DeviceCeilSet'
          ),
        name: 'SuperDeviceCeilSet',
        hidden: true,
        meta: {
          noCache: true,
          title: 'device_ceil_set',
          activeMenu: '/super_device_admin/active_code_admin',
          permission: ['background.admin.device.device_info']
        }
      },
      {
        path: 'secret_key_admin',
        component: () => import(/* webpackChunkName: "secret_key_admin" */ '@/views/super/device-admin/SecretkeyAdmin'),
        name: 'SuperSecretkeyAdmin',
        meta: {
          noCache: true,
          title: 'secret_key_admin',
          activeMenu: '/super_device_admin/secret_key_admin',
          permission: ['background.admin.disclaimer']
        }
      },
      {
        path: 'import_active_code',
        component: () =>
          import(
            /* webpackChunkName: "import_active_code" */ '@/views/super/device-admin/ImportActiveCode'
          ),
        name: 'SuperImportActiveCode',
        hidden: true,
        meta: {
          noCache: true,
          title: 'import_active_code_admin',
          permission: ['background.admin.device.device_info']
        }
      },
      // 设备组管理
      {
        path: 'device_group',
        component: () => import(/* webpackChunkName: "secret_key_admin" */ '@/views/super/device-admin/DeviceGroup'),
        name: 'SuperDeviceGroup',
        meta: {
          noCache: true,
          title: 'device_group',
          // activeMenu: '/super_device_admin/active_code_admin',
          permission: ['background_device.admin.device_group']
        }
      }
    ]
  },
  {
    path: '/super_device_admin/super_device',
    component: Layout,
    redirect: '/super_device_admin/super_device_log',
    alwaysShow: false, // will always show the root menu
    name: 'SuperDevice',
    meta: {
      title: 'super_device_log',
      // icon: 'el-icon-monitor',
      permission: ['background.admin.device.device_info.log_list']
    },
    children: [
      {
        path: 'super_device_log',
        component: () =>
          import(/* webpackChunkName: "super_device_log" */ '@/views/super/device-admin/DeviceLog'),
        name: 'SuperDeviceLog',
        meta: {
          noCache: true,
          title: 'super_device_log',
          permission: ['background.admin.device.device_info.log_list']
        }
      }
    ]
  },
  {
    path: '/super_device_admin/super_device_version',
    component: Layout,
    redirect: '/super_device_admin/super_device_version/device',
    alwaysShow: false, // will always show the root menu
    name: 'SuperDeviceVersion',
    meta: {
      title: 'super_device_version',
      // icon: 'el-icon-monitor',
      permission: ['background_device.admin.device_version']
    },
    children: [
      {
        path: 'device',
        component: () =>
          import(/* webpackChunkName: "super_device_version" */ '@/views/super/device-admin/ClientVersion'),
        name: 'SuperDeviceVesion',
        hidden: true,
        meta: {
          noCache: true,
          title: 'super_device_version',
          permission: ['background_device.admin.device_version']
        }
      },
      {
        path: 'device_version_list',
        component: () =>
          import(/* webpackChunkName: "super_device_version" */ '@/views/super/device-admin/ClientVersionList'),
        name: 'SuperDeviceVesionList',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/super_device_admin/super_device_version',
          title: 'super_device_version_list',
          permission: ['background_device.admin.device_version']
        }
      },
      {
        path: 'add_super_device_version',
        component: () =>
          import(/* webpackChunkName: "add_super_device_version" */ '@/views/super/device-admin/AddClientVersion'),
        name: 'SuperAddClientVersion',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_super_device_version',
          activeMenu: '/super_device_admin/super_device_version',
          permission: ['background_device.admin.device_version']
        }
      }
    ]
  },
  {
    path: '/super_device_admin/super_third_party_equipment',
    component: Layout,
    redirect: '/super_third_party_equipment_admin',
    alwaysShow: false, // will always show the root menu
    name: 'superThirdPartyEquipment',
    meta: {
      title: 'third_party_equipment_admin',
      // icon: 'el-icon-monitor',
      permission: ['background_device.admin.third_device']
    },
    children: [
      {
        path: 'super_third_party_equipment_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_third_party_equipment_admin" */ '@/views/super/device-admin/ThirdPartyEquipmentAdmin'
          ),
        name: 'superThirdPartyEquipmentAdmin',
        meta: {
          noCache: true,
          title: 'third_party_equipment_admin',
          permission: ['background_device.admin.third_device']
        }
      },
      {
        path: 'import_third_party_equipment',
        component: () =>
          import(
            /* webpackChunkName: "super_add_ingredients" */ '@/views/super/device-admin/ImportThirdPartyEquipment'
          ),
        name: 'SuperImportThirdPartyEquipment',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/super_third_party_equipment/super_third_party_equipment_admin',
          title: 'import_third_party_equipment',
          permission: ['background_device.admin.third_device']
        }
      }
    ]
  },
  {
    path: '/super_health_system',
    component: Layout,
    // redirect: '/super_health_system/health_nutrition/ingredients_library',
    redirect: '/super_health_system/user_health_records',
    alwaysShow: true, // will always show the root menu
    name: 'SuperHealthSystem',
    meta: {
      title: 'health_system',
      icon: 'health_system',
      permission: ['admin_food']
    },
    children: [
      {
        path: 'user_health_records',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/user-health-records/UserHealthRecords'
          ),
        name: 'SuperUserHealthRecords',
        meta: {
          noCache: true,
          title: 'user_health_records',
          permission: ['background.admin.ingredient.list']
        },
        children: [
          {
            path: 'records_detail',
            component: () =>
              import(
                /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/user-health-records/RecordsDetail'
              ),
            name: 'SuperRecordsDetail',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/user_health_records',
              title: 'records_detail',
              permission: ['background.admin.ingredient.list']
            }
          },
          {
            path: 'body_detail',
            component: () =>
              import(
                /* webpackChunkName: "BodyDetail" */ '@/views/super/health-system/user-health-records/detail/BodyDetail'
              ),
            name: 'SuperBodyDetail',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/user_health_records',
              title: 'body_detail',
              permission: ['background.admin.ingredient.list']
            }
          },
          {
            path: 'body_view_report',
            component: () =>
              import(
                /* webpackChunkName: "BodyDetail" */ '@/views/super/health-system/user-health-records/detail/BodyViewReport'
              ),
            name: 'SuperBodyViewReport',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/user_health_records',
              title: 'view_report',
              permission: ['background.admin.ingredient.list']
            }
          }
        ]
      },
      {
        path: 'nutritional_analysis',
        component: () =>
          import(
            /* webpackChunkName: "super_nutritional_analysis" */ '@/views/super/health-system/nutritional-analysis/index'
          ),
        name: 'SuperNutritionalAnalysis',
        hidden: true,
        meta: {
          noCache: true,
          title: 'nutritional_analysis',
          activeMenu: '/super_health_system/user_health_records',
          permission: ['background.admin.ingredient.list']
        }
      },
      {
        path: 'health_nutrition',
        redirect: '/health_nutrition/ingredients_library',
        // component: () => import(/* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/IngredientsLibrary'),
        name: 'SuperIngredientsLibraryx',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'health_nutrition_library',
          permission: ['background.admin.ingredient']
        },
        children: [
          {
            path: 'ingredients_library',
            component: () =>
              import(
                /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/health-nutrition/IngredientsLibrary'
              ),
            name: 'SuperIngredientsLibrary',
            meta: {
              noCache: true,
              title: 'ingredients_library',
              permission: ['background.admin.ingredient.list']
            }
          },
          {
            path: 'ingredients_detail_:type',
            component: () =>
              import(
                /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/AddIngredients'
              ),
            name: 'SuperAddIngredients',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/ingredients_library',
              title: 'ingredients',
              permission: ['background.admin.ingredient.list']
            }
          },
          {
            path: 'import_ingredients/:type',
            component: () =>
              import(
                /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/ImportIngredients'
              ),
            name: 'SuperImportIngredients',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/ingredients_library',
              title: 'import_ingredients',
              permission: ['background.admin.ingredient.list']
            }
          },
          {
            path: 'ingredients_category',
            component: () =>
              import(
                /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/IngredientsCategoryNew'
              ),
            name: 'SuperIngredientsCategoryNew',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/ingredients_library',
              title: 'ingredients_category',
              permission: ['background.admin.ingredient.list']
            }
          },
          {
            path: 'commodity',
            component: () =>
              import(
                /* webpackChunkName: "super_commodity" */ '@/views/super/health-system/health-nutrition/CommodityLibrary'
              ),
            name: 'SuperCommodity',
            meta: {
              noCache: true,
              title: 'super_commodity',
              permission: ['background.admin.food.list']
            }
          },
          {
            path: 'add_commodity',
            component: () =>
              import(
                /* webpackChunkName: "super_commodity" */ '@/views/super/health-system/health-nutrition/AddCommodity'
              ),
            name: 'SuperAddCommodity',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/commodity',
              title: 'super_add_commodity',
              permission: ['background.admin.food.list']
            }
          },
          {
            path: 'add_commodity_to_super',
            component: () =>
              import(
                /* webpackChunkName: "super_commodity" */ '@/views/super/health-system/health-nutrition/AddMerchantCommodityToSuper'
              ),
            name: 'SuperAddMerchantCommodityToSuper',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/commodity',
              title: 'super_add_commodity',
              permission: ['background.admin.food.list']
            }
          },
          {
            path: 'import_commodity/:type',
            component: () =>
              import(
                /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/ImportCommodity'
              ),
            name: 'SuperImportCommodity',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/commodity',
              title: 'import_commodity',
              permission: ['background.admin.food.list']
            }
          },
          {
            path: 'import_commodity_image',
            component: () =>
              import(
                /* webpackChunkName: "import_commodity_image" */ '@/views/super/health-system/health-nutrition/ImportCommodityImage'
              ),
            name: 'SuperImportCommodityImage',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/commodity',
              title: 'import_commodity_image',
              permission: ['background.admin.food.list']
            }
          },
          {
            path: 'import_ingredient_image',
            component: () =>
              import(
                /* webpackChunkName: "super_import_ingredient_image" */ '@/views/super/health-system/health-nutrition/ImportIngredientImage'
              ),
            name: 'SuperImportIngredientImage',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/health_nutrition/ingredients_library',
              title: 'import_ingredient_image',
              permission: ['background.admin.ingredient.ingredient_image_bat_add']
            }
          },
          {
            path: 'meal_food_classification',
            component: () =>
              import(
                /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/health-nutrition/MealFoodClassificationNew'
              ),
            name: 'SuperMealFoodClassificationNew',
            hidden: true,
            meta: {
              activeMenu: '/super_health_system/health_nutrition/commodity',
              noCache: true,
              title: 'meal_food_classification',
              permission: ['background.admin.food.list']
            }
          }
        ]
      },
      {
        path: 'parameter-config',
        redirect: '/parameter-config/nutrition-health',
        // component: () => import(/* webpackChunkName: "super_ingredients_library" */ '@/views/super/parameter-config/nutrition-health/NutritionHealth'),
        name: 'SuperParameterConfig',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'parameter_config',
          permission: ['system_settings']
        },
        children: [
          // {
          //   path: 'nutrition_health',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/nutrition-health/NutritionHealth'
          //     ),
          //   name: 'SuperNutritionHealth',
          //   meta: {
          //     noCache: true,
          //     title: 'nutrition_health',
          //     permission: ['background.admin.healthy_info.healthy_nutrition_list']
          //   },
          //   children: []
          // },
          // {
          //   path: 'add_modify_nutrition_health',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "" */ '@/views/super/health-system/parameter-config/nutrition-health/AddModifyNutritionHealth'
          //     ),
          //   name: 'SuperAddOrModifyNutritionHealth',
          //   hidden: true,
          //   meta: {
          //     // noCache: true,
          //     activeMenu: '/super_health_system/parameter-config/nutrition_health',
          //     title: 'add_modify_nutrition_health',
          //     permission: ['background.admin.healthy_info.healthy_nutrition_add']
          //   }
          // },
          // {
          //   path: 'health_info_config',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/health-info-config/HealthInfoConfig'
          //     ),
          //   name: 'SuperHealthInfoConfig',
          //   meta: {
          //     noCache: true,
          //     title: 'health_info_config',
          //     permission: ['background.admin.ingredient.list']
          //   },
          //   children: []
          // },
          // {
          //   path: 'health_fraction_rule',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/health-rule/HealthFractionRule'
          //     ),
          //   name: 'SuperHealthFractionRule',
          //   meta: {
          //     noCache: true,
          //     title: 'health_fraction_rule',
          //     permission: ['background.admin.healthy_info.healthy_score_list']
          //   },
          //   children: []
          // },
          // {
          //   path: 'add_modify_health_fraction_rule',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "" */ '@/views/super/health-system/parameter-config/health-rule/AddModifyHealthFractionRule'
          //     ),
          //   name: 'SuperAddModifyHealthFractionRule',
          //   hidden: true,
          //   meta: {
          //     // noCache: true,
          //     activeMenu: '/super_health_system/parameter-config/health_fraction_rule',
          //     title: 'add_modify_health_fraction_rule',
          //     permission: ['background.admin.healthy_info.healthy_modify']
          //   }
          // },
          {
            path: 'score_time',
            component: () =>
              import(
                /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/score-time/ScoreTime'
              ),
            name: 'SuperScoreTime',
            meta: {
              noCache: true,
              title: 'score_time',
              permission: ['background.admin.healthy_info.healthy_meal_time_list']
            },
            children: []
          },
          {
            path: 'modify_score_time',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/parameter-config/score-time/ModifyScoreTime'
              ),
            name: 'SuperModifyScoreTime',
            hidden: true,
            meta: {
              // noCache: true,
              activeMenu: '/super_health_system/parameter-config/score_time',
              title: 'modify_score_time',
              permission: ['background.admin.healthy_info.healthy_meal_time_list']
            }
          },
          {
            path: 'crowd_admin',
            component: () =>
              import(
                /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/crowd-admin/CrowdAdmin'
              ),
            name: 'SuperCrowdAdmin',
            meta: {
              noCache: true,
              title: 'crowd_admin',
              permission: ['background_healthy.admin.crowd.list']
            },
            children: []
          },
          {
            path: 'add_or_modify_crowd_:type',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "ingredients_category" */ '@/views/super/health-system/parameter-config/crowd-admin/AddOrModifyCrowd'
              ),
            name: 'SuperAddOrModifyCrowd',
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/parameter-config/crowd_admin',
              title: 'add_or_modify_crowd'
              // no_permission: true
              // permission: ['meal_management']
            }
          },
          { // 运动管理
            path: 'motion_admin',
            component: () =>
              import(
                /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/motion-admin/MotionAdmin'
              ),
            name: 'SuperMotionAdmin',
            meta: {
              noCache: true,
              title: 'motion_admin',
              permission: ['background.admin.ingredient.list']
            }
          },
          { // 疾病管理
            path: 'auto_label',
            component: () =>
              import(
                /* webpackChunkName: "super_auto_label" */ '@/views/super/health-system/parameter-config/auto-label/AutoLabel'
              ),
            name: 'SuperAutoLabel',
            meta: {
              noCache: true,
              title: 'auto_label',
              permission: ['background.admin.ingredient.list']
            }
          },
          { // 疾病管理
            path: 'disease_admin',
            component: () =>
              import(
                /* webpackChunkName: "super_disease_admin" */ '@/views/super/health-system/parameter-config/disease-admin/DiseaseAdmin'
              ),
            name: 'SuperDiseaseAdmin',
            meta: {
              noCache: true,
              title: 'disease_admin',
              permission: ['background.admin.ingredient.list']
            }
          }
        ]
      },
      // {
      //   path: 'health_assessment',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "" */ '@/views/super/health-system/health-assessment/HealthAssessment'
      //     ),
      //   name: 'SuperHealthAssessment',
      //   meta: {
      //     noCache: true,
      //     title: 'super_health_assessment',
      //     permission: ['background.admin.healthy_question']
      //   },
      //   children: [
      //     {
      //       path: 'add_edit_questionnaire',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/health-assessment/AddEditQuestionnaire'
      //         ),
      //       name: 'SuperAddEditQuestionnaire',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         activeMenu: '/super_health_system/health_assessment',
      //         title: 'add_edit_questionnaire',
      //         permission: ['background.admin.healthy_question']
      //       }
      //     }
      //   ]
      // },
      {
        path: 'habit_cultivate',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/health-habit-cultivate/HabitCultivate'
          ),
        name: 'SuperHabitCultivate',
        meta: {
          noCache: true,
          title: 'super_habit_cultivate',
          permission: ['background.admin.healthy_question']
        },
        children: [
          {
            path: 'habit/:type',
            component: () =>
              import(/* webpackChunkName: "habit_cultivate" */ '@/views/super/health-system/health-habit-cultivate/AddModifyHabit'
              ),
            name: 'SuperAddModifyHabit',
            hidden: true,
            meta: {
              noCache: true,
              title: 'super_habit_cultivate',
              activeMenu: '/super_health_system/habit_cultivate',
              permission: ['background.admin.healthy_question']
            }
          }
        ]
      },
      {
        path: 'nutrition_rules_core',
        redirect: '/nutrition_rules_core/guidance_rules',
        // component: () =>
        //   import(
        //     /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/GuidanceRules'
        //   ),
        name: 'SuperNutritionRulesCore',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'nutrition_rules_core',
          permission: ['background_healthy.admin.nutrition_rule']
        },
        children: [
          {
            path: 'guidance_rules',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/GuidanceRules'
              ),
            name: 'SuperNutritionGuidanceRules',
            meta: {
              noCache: true,
              title: 'nutrition_guidance_rules',
              permission: ['background_healthy.admin.nutrition_rule.list']
            }
          },
          {
            path: 'modify_guidance_rules',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/ModifyGuidanceRules'
              ),
            name: 'SuperModifyGuidanceRules',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/super_health_system/nutrition_rules_core/guidance_rules',
              title: 'modify_guidance_rules',
              permission: ['background_healthy.admin.nutrition_rule.list']
            }
          }
        ]
      },
      {
        path: 'label_admin',
        redirect: '/label_admin/food_label',
        // component: () =>
        //   import(
        //     /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/GuidanceRules'
        //   ),
        name: 'SuperLabelAdmin',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'label_admin',
          permission: ['background_healthy.admin.label_group']
        },
        children: [
          {
            path: 'food_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/label-admin/FoodLabel'
              ),
            name: 'SuperFoodLabel',
            meta: {
              noCache: true,
              title: 'food_label',
              permission: ['background_healthy.admin.label_group.list']
            }
          },
          {
            path: 'ingredients_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/label-admin/IngredientsLabel'
              ),
            name: 'SuperIngredientsLabel',
            meta: {
              noCache: true,
              title: 'ingredients_label',
              permission: ['background_healthy.admin.label_group.list']
            }
          },
          {
            path: 'user_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/label-admin/UserLabel'
              ),
            name: 'SuperUserLabel',
            meta: {
              noCache: true,
              title: 'user_label',
              permission: ['background_healthy.admin.label_group.list']
            }
          }
        ]
      },
      // {
      //   path: 'member_center',
      //   redirect: '/member_center/member_list',
      //   name: 'SuperMemberCenter',
      //   alwaysShow: true,
      //   meta: {
      //     noCache: true,
      //     title: 'member_center',
      //     no_permission: true
      //     // permission: ['background_healthy.admin.label_group']
      //   },
      //   children: [
      //     {
      //       path: 'member_list',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberList'
      //         ),
      //       name: 'SuperMemberList',
      //       meta: {
      //         noCache: true,
      //         title: 'member_list',
      //         permission: ['background_member.member_user.list']
      //       }
      //     },
      //     {
      //       path: 'member_detail',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberDetail'
      //         ),
      //       name: 'SuperMemberDetail',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_detail',
      //         activeMenu: '/super_health_system/member_center/member_list',
      //         permission: ['background_member.member_user.list']
      //       }
      //     },
      //     {
      //       path: 'member_level',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberLevel'
      //         ),
      //       name: 'SuperMemberLevel',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_level',
      //         permission: ['background_member.member_grade.list']
      //       }
      //     },
      //     {
      //       path: 'member_level/:type',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberLevel'
      //         ),
      //       name: 'SuperAddOrEditMemberLevel',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_level',
      //         activeMenu: '/super_health_system/member_center/member_level',
      //         permission: ['background_member.member_grade.list']
      //       }
      //     },
      //     {
      //       path: 'member_label',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberLabel'
      //         ),
      //       name: 'SuperMemberLabel',
      //       meta: {
      //         noCache: true,
      //         title: 'member_label',
      //         permission: ['background_member.member_label.list']
      //       }
      //     },
      //     {
      //       path: 'member_label/:type',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberLabel'
      //         ),
      //       name: 'SuperAddOrEditMemberLabel',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_label',
      //         activeMenu: '/super_health_system/member_center/member_label',
      //         permission: ['background_member.member_label.list']
      //       }
      //     },
      //     {
      //       path: 'member_permission',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberPermission'
      //         ),
      //       name: 'SuperMemberPermission',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_permission',
      //         permission: ['background_member.member_permission.list']
      //       }
      //     },
      //     {
      //       path: 'member_permission/:type',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberPermission'
      //         ),
      //       name: 'SuperAddOrEditMemberPermission',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_permission',
      //         activeMenu: '/super_health_system/member_center/member_permission',
      //         permission: ['background_member.member_permission.list']
      //       }
      //     },
      //     {
      //       path: 'member_charge_rule_index',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberChargeRuleIndex'
      //         ),
      //       name: 'SuperMemberChargeRule',
      //       meta: {
      //         noCache: true,
      //         title: 'member_charge_rule',
      //         permission: ['background_member.member_charge_rule.list']
      //       }
      //     },
      //     {
      //       path: 'member_charge_rule_detail',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberChargeRuleDetail'
      //         ),
      //       name: 'SuperMemberChargeRuleDetail',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_charge_rule_detail',
      //         permission: ['background_member.member_charge_rule.list']
      //       }
      //     },
      //     {
      //       path: 'member_receive_record',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberReceiveRecord'
      //         ),
      //       name: 'SuperMemberReceiveRecord',
      //       meta: {
      //         noCache: true,
      //         title: 'member_receive_record',
      //         permission: ['background_member.member_receive.list']
      //       }
      //     },
      //     {
      //       path: 'member_exclusive_rights',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberExclusiveRightsList'
      //         ),
      //       name: 'MemberExclusiveRightsList',
      //       meta: {
      //         noCache: true,
      //         title: 'member_exclusive_rights',
      //         permission: ['background_member.rights_setting.list']
      //       }
      //     },
      //     {
      //       path: 'member_exclusive_setting',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberExclusiveSetting'
      //         ),
      //       name: 'MemberExclusiveSetting',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_exclusive_setting',
      //         permission: ['background_member.rights_setting.list']
      //       }
      //     },
      //     {
      //       path: 'member_permission_manager',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberPermissionManager'
      //         ),
      //       name: 'MemberPermissionManager',
      //       meta: {
      //         noCache: true,
      //         title: 'member_permission_manager',
      //         permission: ['background_member.member_permission.list']
      //       }
      //     },
      //     {
      //       path: 'member_key_manager',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberKeyManager'
      //         ),
      //       name: 'MemberKeyManager',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         title: 'member_key_manager',
      //         permission: ['background_member.member_permission.list']
      //       }
      //     },
      //     {
      //       path: 'promotional_picture_setting_list',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/PromotionalPictureSettingList'
      //         ),
      //       name: 'PromotionalPictureSettingList',
      //       meta: {
      //         noCache: true,
      //         title: 'promotional_picture_setting_list',
      //         permission: ['background_member.promotional_picture.list']
      //       }
      //     },
      //     {
      //       path: 'promotional_picture_setting',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/PromotionalPictureSetting'
      //         ),
      //       name: 'PromotionalPictureSetting',
      //       hidden: true,
      //       meta: {
      //         noCache: true,
      //         activeMenu: '/super_health_system/member_center/promotional_picture_setting_list',
      //         title: 'promotional_picture_setting',
      //         permission: ['background_member.promotional_picture.list']
      //       }
      //     }
      //   ]
      // },
      {
        path: 'diet_manage',
        redirect: '/diet_manage/menu_plan_list',
        name: 'SuperDietManage',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'diet_manage',
          no_permission: true,
          permission: ['background_healthy.diet_manage']
        },
        children: [
          {
            path: 'menu_plan_list',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/MenuPlanList'
              ),
            name: 'SuperMenuPlanList',
            meta: {
              noCache: true,
              title: 'menu_plan_list',
              permission: ['background_healthy.menu_plan.list']
            }
          },
          {
            path: 'menu_plan_list/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/AddOrEditMenuPlanList'
              ),
            name: 'SuperAddOrEditMenuPlanList',
            hidden: true,
            meta: {
              noCache: true,
              title: 'menu_plan_list',
              activeMenu: '/super_health_system/diet_manage/menu_plan_list',
              permission: ['background_healthy.menu_plan.list']
            }
          },
          {
            path: 'leave_message',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/LeaveMessage'
              ),
            name: 'SuperLeaveMessage',
            hidden: true,
            meta: {
              noCache: true,
              title: 'leave_message',
              activeMenu: '/super_health_system/diet_manage/menu_plan_list',
              permission: ['background_healthy.menu_plan.list']
            }
          },
          {
            path: 'three_meal_list',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/ThreeMealList'
              ),
            name: 'SuperThreeMealList',
            meta: {
              noCache: true,
              title: 'three_meal_list',
              permission: ['background_healthy.three_meal.list']
            }
          },
          {
            path: 'three_meal_list/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/AddOrEditThreeMealList'
              ),
            name: 'SuperAddOrEditThreeMealList',
            hidden: true,
            meta: {
              noCache: true,
              title: 'three_meal_list',
              activeMenu: '/super_health_system/diet_manage/three_meal_list',
              permission: ['background_healthy.three_meal.list']
            }
          }
        ]
      }
      // ,
      // {
      //   path: 'sms_manager',
      //   redirect: '/sms_manager/sms_call_record',
      //   name: 'SmsManager',
      //   alwaysShow: true,
      //   meta: {
      //     noCache: true,
      //     title: 'sms_manager',
      //     no_permission: true,
      //     permission: ['background_member.sms_push_receive.list']
      //     // permission: ['background_healthy.admin.label_group']
      //   },
      //   children: [
      //     {
      //       path: 'sms_call_record',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberSmsCallRecord'
      //         ),
      //       name: 'smsCallRecord',
      //       meta: {
      //         noCache: true,
      //         title: 'sms_call_record',
      //         permission: ['background_member.sms_push_receive.list']
      //       }
      //     }
      //   ]
      // }
    ]
  },
  {
    path: '/super_article_push',
    component: Layout,
    redirect: '/super_article_push/article_admin',
    alwaysShow: true, // will always show the root menu
    name: 'SuperArticlePush',
    meta: {
      title: 'super_article_push',
      icon: 'super_article_push',
      permission: ['admin_article']
    },

    children: [
      {
        path: 'article_admin',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/article-push/article-admin/articleAdmin'
          ),
        name: 'SuperArticleAdmin',
        meta: {
          noCache: true,
          title: 'super_article_admin',
          permission: ['background.admin.article.list']
        }
      },
      {
        path: 'add_edit_article',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/article-push/article-admin/addEditArticle'
          ),
        name: 'SuperAddEditArticle',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/super_article_push/article_admin',
          title: 'add_edit_article',
          permission: ['background.admin.article.add']
        }
      },
      {
        path: 'article_category',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/article-push/article-admin/ArticleCategory'
          ),
        name: 'SuperArticleCategory',
        meta: {
          noCache: true,
          title: 'super_article_category',
          permission: ['background.admin.article_category.list']
        },
        children: []
      }
      // {
      //   path: 'article_label',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "" */ '@/views/super/article-push/article_label/articleLabel'
      //     ),
      //   name: 'SuperArticleLabel',
      //   meta: {
      //     noCache: true,
      //     title: 'super_article_label',
      //     permission: ['background.admin.article_tag.list']
      //   },
      //   children: []
      // }
    ]
  },
  {
    path: '/messages_push',
    component: Layout,
    redirect: '/messages_push/recharge_rules',
    alwaysShow: true,
    name: 'MessagesPush',
    meta: {
      title: 'messages_push',
      icon: 'device',
      permission: ['messages_center']
    },
    children: [
      {
        path: 'recharge_rules',
        component: () =>
          import(/* webpackChunkName: "recharge_rules" */ '@/views/super/messages-center/messages-push/RechargeRules'),
        name: 'RechargeRules',
        meta: {
          noCache: true,
          title: 'recharge_rules',
          permission: ['messages_center']
        }
      }
    ]
  },
  {
    path: '/sms_setting',
    component: Layout,
    alwaysShow: false,
    redirect: '/sms_setting/setting',
    name: 'SmsSetting',
    meta: {
      title: 'sms_setting',
      icon: 'device',
      permission: ['admin_operation_management']
    },
    children: [
      {
        path: 'sms_setting',
        component: () =>
          import(
          /* webpackChunkName: "SmsSetting" */ '@/views/super/system-settings/SmsSetting'
          ),
        name: 'SmsSetting',
        meta: {
          noCache: true,
          title: 'sms_setting',
          permission: ['admin_operation_management']
        }
      }
    ]
  }
]
export default system
