/* eslint-disable no-unused-vars */
import {
  getToken,
  setToken,
  removeToken,
  getUserInfo,
  setSessionStorage,
  getSessionStorage,
  removeSessionStorage,
  sleep,
  getTreeDeepkeyList,
  getRouterFlattenList
} from '@/utils'
import router, { resetRouter, asyncRoutes } from '@/router'
import apis from '@/api'
import { Message } from 'element-ui'
import { changeThemeByColor } from '@/utils/themeColorClient'
import { CHANNEL_PERMISSION_LIST } from '@/views/merchant/channel-manage/common/channelHome'
import { localPermissionsTree } from '@/constants/index'

let organization = getSessionStorage('organization')

const state = {
  token: '',
  userInfo: {}, // 用户信息
  organization: organization ? Number(organization) : '', // 当前登录账号的组织
  permissions: [], // 用户的所有权限key列表
  allPermissions: [], // 当前登录用户的所有权限
  permissionsTree: [], // 用户权限tree
  agreementInfo: {}, // 协议内容信息
  stopServiceMsg: {}, // 服务停止信息
  orgsLevelList: JSON.parse(getSessionStorage('orgsLevelList') || "{}"), // 登录组织登记保存
  allNavMenuList: [] // 菜单列表
}

const mutations = {
  SET_STOPSERVICEMSG: (state, stopServiceMsg) => {
    state.stopServiceMsg = stopServiceMsg
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_ALLPERMISSIONS: (state, permissions) => {
    state.allPermissions = permissions
  },
  SET_USERINFO: (state, data) => {
    state.userInfo = Object.assign({}, data)
  },
  SET_PERMISSIONTREE: (state, data) => {
    state.permissionsTree = Object.assign([], data)
  },
  SET_ORGANIZATION: (state, organization) => {
    state.organization = organization
  },
  SET_AGREEMENTINFO: (state, agreementInfo) => {
    state.agreementInfo = agreementInfo
  },
  SET_ORGSLEVELLIST: (state, orgsList) => {
    state.orgsLevelList = orgsList
    setSessionStorage('orgsLevelList', JSON.stringify(orgsList))
  }
}

const actions = {
  setStopServiceMsg({ commit }, data) {
    return new Promise(resolve => {
      commit('SET_STOPSERVICEMSG', data)
      resolve()
    })
  },
  // user login
  login({ commit, dispatch }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', data.token)
      setToken(data.token)
      dispatch('setUserInfo', data)
      dispatch('setAllPermissions', data.role_permission)
      commit('SET_ORGSLEVELLIST', data.orgs_level)
      var color = '#ff9b45'
      if (Reflect.has(data, "color") && data.color) {
        color = data.color
      }
      console.log("login", data);
      changeThemeByColor(color)
      resolve()
    })
  },
  // set organization
  setOrganization({ commit, state }, data) {
    return new Promise(resolve => {
      setSessionStorage('organization', data)
      commit('SET_ORGANIZATION', data)
      // 切换组织需要请除菜品数据
      removeSessionStorage('systemIngredientsList')
      removeSessionStorage('foodIngredientList')
      resolve()
    })
  },
  // set user info
  setUserInfo({ commit, dispatch, state }, data) {
    return new Promise(resolve => {
      let orgs = Object.keys(data.orgs)
      let organization = getSessionStorage('organization')
      if (orgs.length > 0) {
        // 处理下orgs 别的地方需要用到
        if (!data.organizationList) {
          data.organizationList = orgs.map((key, index) => {
            // if (!organization && !index) {
            //   dispatch('setOrganization', Number(key))
            // }
            return {
              id: Number(key),
              name: data.orgs[key],
              level: data.orgs_level ? data.orgs_level[key] : ''
              // isSelect: !index
            }
          })
        }
      }
      // 这里兼容360那些浏览器，新建tag有缓存数据，如果跟use_org 不一样的时候重新赋值一次
      if (organization !== data.use_org) {
        dispatch('setOrganization', Number(data.use_org))
      }
      setSessionStorage('USERINFO', encodeURIComponent(JSON.stringify(data)))
      commit('SET_USERINFO', data)
      resolve()
    })
  },
  // 更新userinfo的数据
  updateUserInfo({ commit, dispatch }, data) {
    return new Promise(resolve => {
      if (data.token) {
        commit('SET_TOKEN', data.token)
        setToken(data.token)
      }
      if (data.role_permission) {
        dispatch('setAllPermissions', data.role_permission)
      }
      setSessionStorage('USERINFO', encodeURIComponent(JSON.stringify(data)))
      commit('SET_USERINFO', data)
      resolve()
    })
  },
  // Set the permissions owned for the current user
  setAllPermissions({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_ALLPERMISSIONS', data)
      resolve()
    })
  },
  // set permission tree
  setPermissionsTree({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_PERMISSIONTREE', data)
      resolve()
    })
  },
  // set permission key list
  // 注意，这是当前左侧栏线上的菜单的权限表，不是用户所有的，do you konw
  setPermissions({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_PERMISSIONS', data)
      resolve()
    })
  },
  // F5 刷新用户的信息，先从本地拿用户数据先
  async setDefaultInfo({ commit, dispatch }, data) {
    const userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO')))
    // let organization = getSessionStorage('organization')
    commit('SET_TOKEN', getToken())
    commit('SET_USERINFO', userInfo)
    // if (organization) {
    //   dispatch('setOrganization', organization)
    // }
    await dispatch('setAllPermissions', userInfo.role_permission)
  },
  // 获取菜单列表
  getPermissionList({ dispatch, rootGetters }, data) {
    return new Promise((resolve, reject) => {
      // 调用获取权限的接口
      apis
        .apiBackgroundBaseMenuGetListPost(data)
        .then(async res => {
          if (res.code === 0) {
            let allPermissions = []
            state.allNavMenuList = res.data.filter(v => {
              allPermissions = allPermissions.concat(getTreeDeepkeyList([v]))
              return v.is_menu
            })
            // zrj+ 非超管端添加首页权限
            let id = this.state.user.userInfo.account_type
            // 获取渠道用户信息
            var isChannel = this.state.user.userInfo.is_channel_account || ''
            var permissionList = this.state.user.userInfo.role_permission || []
            console.log("isChannel", isChannel);
            // operations_system
            // 如果他是渠道用户就手动添加两个页面
            if (isChannel) {
              state.allNavMenuList = state.allNavMenuList.concat(CHANNEL_PERMISSION_LIST)
              state.allNavMenuList.forEach(v => {
                allPermissions = allPermissions.concat(getTreeDeepkeyList([v]))
              });
              dispatch('setAllPermissions', allPermissions)
            } else {
              let reservationData = {
                permissions: 'background_reservation.list',
                setting_permissions: ['background_reservation.background_reservation_settings.list'],
                api: 'apiBackgroundReservationBackgroundReservationSettingsReservationOrganizationCheckPost'
                // // 预约设置里面除了预约点餐明细以外其他的权限
                // // 现在是只有配置了预约/报餐层级的可以查看全部权限，其他层级只能看预约/报餐明细
                // other_permissions: [
                //   'background_reservation.background_reservation_settings.list',
                //   'background_order.reservation_order.collect_list',
                //   'background_order.reservation_order.group_collect_list',
                //   'background_order.reservation_order.food_collect_list',
                //   'background_food.set_meal.list',
                //   'background_order.reservation_order.cupboard_order_list'
                // ],
                // detail_permissions: 'background_order.reservation_order.info_list'
              }
              await dispatch('isAllowMealSetting', reservationData)
              // 控制报餐权限
              let mealReportData = {
                permissions: 'report_meal_settings',
                setting_permissions: ['background_report_meal.report_meal_settings.list', 'background_order.order_report_meal.not_report_list'],
                api: 'apiBackgroundReportMealReportMealSettingsReportMealOrganizationCheckPost'
                // 预约设置里面除了报餐点餐明细以外其他的权限
                // 现在是只有配置了预约/报餐层级的可以查看全部权限，其他层级只能看预约/报餐明细
                // other_permissions: [
                //   'background_report_meal.report_meal_settings.list',
                //   'background_order.order_report_meal.group_collect_list',
                //   'background_order.order_report_meal.collect_list',
                //   'background_reservation.report_meal_settings.add'
                // ],
                // detail_permissions: 'background_order.order_report_meal.info_list'
              }
              await dispatch('isAllowMealSetting', mealReportData)
              // 控制未报餐人员统计
              // let headCountData = {
              //   permissions: '',
              //   setting_permissions: '',
              //   api: 'apiBackgroundReportMealReportMealSettingsReportMealOrganizationCheckPost'
              // }
              // await dispatch('isAllowMealSetting', headCountData)
            }
            // state.allNavMenuList.push({
            //   verbose_name: "进销存",
            //   index: "9.1",
            //   key: "background_drp",
            //   is_menu: true,
            //   parent: "1",
            //   level: 1,
            //   perm_type: 2
            // })
            // allPermissions.push('inventory_system')
            // dispatch('setAllPermissions', allPermissions)
            const permissions = await dispatch('navTabs/setDefaultMenu', state.allNavMenuList, {
              root: true
            })
            await dispatch('setPermissions', permissions)
            await dispatch('setPermissionsTree', res.data)
            resolve(permissions)
          } else {
            resolve([])
            Message.error(res.msg)
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  },
  // logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', '')
      commit('SET_PERMISSIONS', [])
      commit('SET_PERMISSIONTREE', [])
      commit('SET_ALLPERMISSIONS', [])
      commit('SET_USERINFO', {})
      commit('SET_AGREEMENTINFO', {})

      // removeToken()
      // removeSessionStorage('USERINFO')
      // removeSessionStorage('organization')
      // removeSessionStorage('CHECKDOUBLEFACTOR')
      // removeSessionStorage('systemIngredientsList')
      // removeSessionStorage('foodIngredientList')
      removeToken()
      removeSessionStorage('USERINFO')
      removeSessionStorage('organization')
      removeSessionStorage('CHECKDOUBLEFACTOR')
      removeSessionStorage('theme_color')
      removeSessionStorage('systemIngredientsList')
      removeSessionStorage('foodIngredientList')
      removeSessionStorage('showAgreement')
      removeSessionStorage('backUrl')
      removeSessionStorage('PWDV4TOKEN')
      window.sessionStorage.clear()

      resetRouter()

      dispatch('navTabs/delAllViews', null, { root: true })
      dispatch('permission/clearRoute', null, { root: true })
      dispatch('navTabs/setNavMenuList', [], { root: true })
      // dispatch('settings/setBackUrl', '', { root: true })

      resolve()
    })
  },
  getMenuStatus({ state }, api) {
    let id = state.userInfo.company_id
    return new Promise((resolve, reject) => {
      apis[api](id)
        .then(async res => {
          if (res.code === 0) {
            resolve('YES')
          } else {
            resolve('NO')
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  },
  async isAllowMealSetting({ state, dispatch }, data) {
    let allPermissions = state.allPermissions
    let navMenuList = state.allNavMenuList
    let status = await dispatch('getMenuStatus', data.api)
    let index = allPermissions.findIndex(e => e === data.permissions)
    // 递归函数，用于遍历权限树
    const traverseMenu = (menuList) => {
      for (let i = menuList.length - 1; i >= 0; i--) {
        const menu = menuList[i]
        // 检查当前节点的key是否在setting_permissions中
        let shouldRemove = false
        data.setting_permissions.forEach((item) => {
          if (menu.key === item) {
            // 如果找到匹配的key，移除
            shouldRemove = true
          }
        })
        // 如果当前节点需要删除，直接从menuList中删除
        if (shouldRemove) {
          menuList.splice(i, 1)
        } else if (menu.children && menu.children.length > 0) {
          // 递归处理子节点
          traverseMenu(menu.children)
        }
      }
    }

    if (status === 'YES') { // 是配置预约/报餐的层级
      if (index === -1) allPermissions.push(data.permissions)
      data.setting_permissions.forEach((item) => {
        let setIndex = allPermissions.findIndex(e => e === item)
        if (setIndex === -1) allPermissions.push(item)
      })
    } else if (status === 'NO') { // 不是配置预约/报餐的层级，但
      data.setting_permissions.forEach((item) => {
        let setIndex = allPermissions.findIndex(e => e === item)
        if (setIndex !== -1) {
          allPermissions.splice(setIndex, 1)
        }
      })
      // 是有这些权限，去掉
      traverseMenu(navMenuList)
    }
    dispatch('setAllPermissions', allPermissions)
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_PERMISSIONS', [])
      removeToken()
      resolve()
    })
  },
  // 设置协议内容信息
  setAgreementInfo({ commit }, data) {
    if (data) {
      commit('SET_AGREEMENTINFO', data)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
