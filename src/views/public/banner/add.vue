<template>
  <div class="add-banner" v-loading="isLoading">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <el-form-item label="名称：" prop="name">
        <el-input v-model="formData.name" class="w-300 ps-input" placeholder="不超过20字" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="图片：" prop="fileLists">
        <!-- <template #label>
          <span>上传人脸</span>
        </template> -->
        <file-upload
          ref="faceFileRef"
          :fileList="formData.fileLists"
          type="enclosure"
          :before-upload="beforeUpload"
          @fileLists="getFileLists"
          class="avatar-uploader"
          :show-file-list="false"
        >
          <img v-if="formData.fileLists.length" :src="formData.fileLists[0].url" class="avatar" @click="clearFileHandle">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </file-upload>
        <span class="tips m-l-20">上传格式 jpg/png, 比例:750*360, 图片最大不能超过10M</span>
      </el-form-item>
      <el-form-item label="有效日期：" prop="effective_time">
        <el-date-picker
          v-model="formData.effective_time"
          type="datetime"
          placeholder="选择日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          />
        <el-checkbox v-model="isForever" class="m-l-40">永久</el-checkbox>
      </el-form-item>
      <el-form-item label="跳转地址：" prop="jump_type">
        <el-radio-group v-model="formData.jump_type" class="ps-radio">
          <el-radio label="inner" value="inner">内部界面</el-radio>
          <el-radio v-if="type === 'super'" label="outsite" value="outsite">外部链接</el-radio>
        </el-radio-group>
        <el-form-item v-if="formData.jump_type === 'inner'" label="" prop="jump_innerselect_type" class="p-b-20">
          <el-select v-model="formData.jump_innerselect_type" placeholder="请选择跳转地址" class="ps-select" clearable>
            <el-option v-for="item in innerList" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item  v-else-if="formData.jump_type === 'outsite' && type === 'super'" label-width="80px" label="地址：" prop="jump_url" class="p-b-20">
          <el-input v-model="formData.jump_url" class="w-300 ps-input"></el-input>
        </el-form-item>
      </el-form-item>
      <el-form-item v-if="type === 'super'" label="显示项目点：" prop="show_orgs">
        <company-select
          class="search-item-w ps-select"
          v-model="formData.show_orgs"
          clearable
          multiple
          collapse-tags
          filterable
          placeholder=""
          :disabled="formData.is_all_orgs"
          @change="changeCompany"
        ></company-select>
        <el-checkbox v-model="formData.is_all_orgs" class="m-l-40">默认全选</el-checkbox>
      </el-form-item>
      <el-form-item label="优先级：" prop="priority">
        <el-input v-model="formData.priority" class="w-300 ps-input" placeholder=""></el-input>
      </el-form-item>
      <div class="error-text m-t-20" style="margin-left: 120px;">优先级数值小的优先显示</div>
      <el-form-item>
        <div class="m-t-50">
          <el-button plain @click="closeHandler">取消</el-button>
          <el-button type="primary" class="ps-origin-btn" @click="submitHandler('publish')">确认发布</el-button>
          <el-button v-if="!is_first" type="primary" class="ps-origin-btn" @click="submitHandler('unpublished')">保存不发布</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { camelToUnderline } from '@/utils'
import CompanySelect from '@/components/CompanySelect'

export default {
  name: 'WithdrawOrder',
  components: {
    CompanySelect
  },
  data() {
    let validateNumber = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!reg.test(value)) {
        callback(new Error('请输入小于100的数字'))
      } else {
        if (value > 100) {
          return callback(new Error('请输入小于100的数字'))
        }
        callback()
      }
    }
    let checkoutDate = (rule, value, callback) => {
      if (this.isForever) {
        callback()
      } else {
        if (value) {
          callback()
        } else {
          return callback(new Error('请输入有效日期或勾选永久'))
        }
      }
    }
    let checkoutOrgsList = (rule, value, callback) => {
      if (this.formData.is_all_orgs) {
        callback()
      } else {
        if (value) {
          callback()
        } else {
          return callback(new Error('请选择项目点'))
        }
      }
    }
    return {
      type: '', // super, merchant
      operate: '', // add,modify
      is_first: false, // 是否是第一条
      isLoading: false,
      apiList: {
        super: {
          add: 'apiBackgroundAdminMarketingBannerAddPost',
          modify: 'apiBackgroundAdminMarketingBannerModifyPost'
        },
        merchant: {
          add: 'apiBackgroundMarketingMarketingBannerAddPost',
          modify: 'apiBackgroundMarketingMarketingBannerModifyPost'
        }
      },
      isForever: true, // 控制是否永久
      formData: { // 表单字段
        id: '',
        name: '', // name
        fileLists: [],
        effective_time: '', // 有效日期
        img_url: '', // 图片
        jump_type: 'inner', // 跳转类型
        jump_url: '', // 跳转地址
        jump_innerselect_type: '',
        show_orgs: '', // 显示项目点
        priority: '', // 优先级
        status: '', // 状态
        is_all_orgs: true // 是否全选项目点
      },
      formRules: { // 表单校验规则
        name: [
          { required: true, message: '名称不能为空', trigger: "blur" }
        ],
        fileLists: [
          { required: true, message: '图片不能为空', trigger: "blur" }
        ],
        jump_type: [
          { required: true, message: '请选择跳转类型', trigger: "blur" }
        ],
        // jump_innerselect_type: [
        //   { required: true, message: '请选择跳转地址', trigger: "blur" }
        // ],
        // jump_url: [
        //   { required: true, message: '请选择输入跳转地址', trigger: "blur" }
        // ],
        effective_time: [
          { validator: checkoutDate, trigger: 'blur' }
        ],
        show_orgs: [
          { validator: checkoutOrgsList, trigger: "blur" }
        ],
        priority: [
          { required: true, validator: validateNumber, trigger: "blur" }
        ]
      },
      innerList: [
        { value: "", name: "无" },
        { value: "app_management", name: "移动端管理" },
        { value: "charge", name: "充值" },
        { value: "order", name: "订单" },
        { value: "reservation", name: "预约点餐/我的预约" },
        { value: "report", name: "报餐" },
        { value: "account_info", name: "账户信息" },
        { value: "tray_bind", name: "托盘绑定" },
        { value: "jiaofei", name: "缴费中心" },
        { value: "intent_food", name: "意向菜谱" },
        { value: "attendance", name: "门禁考勤" },
        { value: "free_payment_setting", name: "免密代扣" },
        { value: "shop_feedback", name: "食堂建议" },
        { value: "order_review", name: "审核查询" }
      ]
    }
  },
  created() {
    this.type = this.$route.query.role
    this.operate = this.$route.params.type
    if (this.$route.query.is_first) {
      this.is_first = Boolean(this.$route.query.is_first)
    }
    this.initLoad()
  },
  mounted() {
  },
  watch: {
    'formData.effective_time': {
      handler(newVal) {
        if (!newVal) {
          this.isForever = true
        } else {
          this.isForever = false
        }
      }
    },
    isForever(newVal) {
      if (newVal) {
        this.formData.effective_time = ''
      }
    }
  },
  computed: {
  },
  methods: {
    changeCompany(e) {
      console.log(111, e)
    },
    initLoad() {
      if (this.$route.query.data) {
        let detail = this.$decodeQuery(this.$route.query.data)
        console.log(detail)
        this.formData.id = detail.id
        this.formData.name = detail.name
        // this.formData.fileLists = detail.img_url ? [detail.img_url] : []
        if (detail.img_url) {
          let imgName = detail.img_url.split('/')
          imgName = imgName[imgName.length - 1]
          this.formData.fileLists = [{
            name: imgName,
            status: "success",
            uid: new Date().getTime(),
            url: detail.img_url
          }]
        }
        this.formData.jump_type = detail.jump_type
        if (detail.jump_type === 'inner') {
          this.formData.jump_innerselect_type = detail.jump_url
        } else {
          this.formData.jump_url = detail.jump_url
        }
        this.formData.show_orgs = detail.show_orgs
        this.formData.priority = detail.priority
        this.formData.status = detail.status
        this.formData.effective_time = detail.effective_time || ''
        if (!this.formData.effective_time) {
          this.isForever = true
        }
        this.formData.is_all_orgs = detail.is_all_orgs
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        let k = camelToUnderline(key)
        if (data[key].value !== '' && data[key].value !== null) {
          if (k !== 'select_time') {
            params[k] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    getFileLists(fileLists) {
      this.formData.fileLists = fileLists
      console.log('fileLists', fileLists)
    },
    beforeUpload(file) {
      let unUploadType = ['.jpg', '.png']
      if (!unUploadType.includes(this.getSuffix(file.name))) {
        this.$message.error('上传图片只能是 jpg/png 格式')
        return false
      }
      const isLt10M = (file.size / 1024 / 1024) < 10;
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
        return false
      }
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    clearFileHandle() {
      console.log('clear')
      this.$refs.faceFileRef.clearHandle()
      this.formData.fileLists = []
    },
    // 获取数据列表
    async modifyHandle(params) {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[this.apiList[this.type].modify](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.closeTab()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取数据列表
    async addHandle(params) {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[this.apiList[this.type].add](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.closeTab()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化下参数
    getParams() {
      let params = {
        name: this.formData.name,
        img_url: this.formData.fileLists[0].url,
        jump_type: this.formData.jump_type,
        priority: this.formData.priority
      }
      // 超管才有外部链接
      if (this.type === 'super' && this.formData.jump_type === 'outsite') {
        params.jump_url = this.formData.jump_url
      }
      if (this.formData.jump_type === 'inner') {
        params.jump_url = this.formData.jump_innerselect_type
      }
      // 超管才有选择项目点
      if (this.type === 'super') {
        if (this.formData.is_all_orgs) {
          params.is_all_orgs = this.formData.is_all_orgs
        } else {
          params.show_orgs = this.formData.show_orgs
        }
        if (this.isForever) {
          params.effective_time = null
        } else {
          params.effective_time = this.formData.effective_time
        }
      }
      if (this.operate === 'modify') {
        params.id = this.formData.id
      }
      return params
    },
    // 提交数据
    submitHandler(status) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          let params = this.getParams()
          console.log('params', params)
          params.status = status
          console.log('params', params)
          if (this.operate === 'modify') {
            if (this.formData.status === status) delete params.status // 后端不处理，我是服了
            this.modifyHandle(params)
          } else {
            this.addHandle(params)
          }
        } else {
          console.log('error validate')
        }
      })
    },
    // 关闭当前页面
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.closeTab()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    closeTab() {
      this.$closeCurrentTab(this.$route.path)
    }
  }
}
</script>

<style lang="scss">
.add-banner{
  margin: 20px 0;
  padding: 30px 0;
  border-radius: 12px;
  background-color: #fff;
  .w-300{
    width: 300px;
  }
  .avatar-uploader {
    vertical-align: middle;
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 80px;
      height: 80px;
      line-height: 80px;
      text-align: center;
    }
    .avatar {
      max-width: 200px;
      max-height: 160px;
      display: block;
    }
  }
  .tips{
    color: #a3a3a3;
  }
}
</style>
