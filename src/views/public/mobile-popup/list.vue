<template>
  <div class="banner container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form v-if="type==='super'" ref="searchRef" label-width="105px" :form-setting="searchSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="plain" type="setting">报表设置</button-icon> -->
          <button-icon color="origin" type="" @click="modifyHandle('add')" :v-permission="type === 'merchant' ? ['background_marketing.marketing_popup.add'] : []">新增弹窗</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
          <el-table
            border
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in tableSetting" :key="item.key" :col="item" :index="indexHandle">
              <template #effectiveTime="{ row }">
                {{ row.effective_time ? effectiveTime(row.effective_time) : '永久' }}
              </template>
              <template #status="{ row }">
                <el-switch v-model="row.status" active-value="publish" inactive-value="unpublished" @change="switchStatus(row.id, row.status)"  :disabled="type === 'merchant' ? !allPermissions.includes('background_marketing.marketing_popupchange_status') : false" />
              </template>
              <template #operate="{ row }">
                <el-button  type="text" size="small" class="ps-text" @click="modifyHandle('modify', row)" :v-permission="type === 'merchant' ? ['background_marketing.marketing_popup.mpdify'] : []">编辑</el-button>
                <el-button v-if="row.status === 'unpublished'"  type="text" size="small" class="ps-text" @click="deleteHandle('one', row)" :v-permission="type === 'merchant' ? ['background_marketing.marketing_popup.delete'] : []">删除</el-button>
              </template>
            </table-column>
             <!-- <el-table-column fixed="right" label="操作" width="130px" align="center">
              <template slot-scope="scope">
                <el-button v-if="scope.row.review_status === 'wait'" type="text" size="small" class="ps-text" @click="gotoDetail(scope.row, 1)">处理</el-button>
                <el-button type="text" size="small" class="ps-text" @click="gotoDetail(scope.row, 2)">查看详情</el-button>
              </template>
            </el-table-column> -->
          </el-table>
      </div>
      <!-- table content end -->
      <!-- 统计 start -->
      <!-- <table-statistics :statistics="collect" /> -->
      <!-- end -->
       <!-- 分页 start -->
       <div v-if="type !== 'super'">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
       </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce, camelToUnderline } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mapGetters } from 'vuex'
export default {
  name: 'PopupMobile',
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  props: {
    type: String
  },
  data() {
    return {
      isLoading: false,
      searchSetting: {},
      tableSetting: [
        // { label: '序号', key: 'index', type: 'index', width: '80px' },
        { label: '名称', key: 'name' },
        { label: '图片', key: 'img_url', isComponents: true, type: 'image', preview: true },
        { label: '有效期', key: 'effective_time', type: 'slot', slotName: 'effectiveTime' },
        { label: '优先级', key: 'priority' },
        { label: '显示页面', key: 'show_page_url_alias' },
        { label: '跳转类型', key: 'jump_type_alias' },
        { label: '跳转页面', key: 'jump_url_alias' },
        { label: '创建时间', key: 'create_time' },
        { label: '显示项目点', key: 'show_orgs_text_alias', showTooltip: true },
        { label: '修改时间', key: 'update_time' },
        { label: '发布状态', key: 'status', type: 'slot', slotName: "status" },
        { label: '操作人', key: 'operator_alias' },
        { key: 'operate', label: '操作', type: 'slot', slotName: "operate", fixed: 'right' }
      ],
      // 数据列表
      tableData: [],
      // collect: [
      //   { key: 'wait_count', value: 0, label: '待审核合计：', unit: '条' }
      // ],
      totalPageSize: 0,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      apiList: {
        super: {
          list: 'apiBackgroundAdminMarketingPopupListPost',
          delete: 'apiBackgroundAdminMarketingPopupDeletePost'
        },
        merchant: {
          list: 'apiBackgroundMarketingMarketingPopupListPost',
          delete: 'apiBackgroundMarketingMarketingPopupDeletePost'
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  computed: {
    ...mapGetters(['allPermissions']),
    effectiveTime() {
      return d => {
        return dayjs(d).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  methods: {
    initLoad(flag) {
      if (this.type === 'super') {
        this.searchSetting = {
          name: {
            label: '名称',
            type: 'input',
            value: '',
            clearable: true
          },
          show_orgs: {
            label: '显示项目点',
            type: 'select',
            value: '',
            listNameKey: 'name',
            listValueKey: 'id',
            filterable: true,
            collapseTags: true,
            multiple: true,
            clearable: true,
            dataList: []
          }
        }
        this.getOrgList()
      } else if (flag) {
        // do nothing
      } else {
        this.tableSetting.splice(8, 1)
      }
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        let k = camelToUnderline(key)
        if (data[key].value !== '' && data[key].value !== null) {
          if (k !== 'select_time') {
            params[k] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 自定义序号
    indexHandle(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1
    },
    // 切换审核状态
    changeOrderStatus(e) {
      this.currentPage = 1
      this.getDataList()
    },
    // 获取数据列表
    async getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchSetting),
        page: this.currentPage,
        page_size: this.type !== 'super' ? this.pageSize : 9999
      }
      const [err, res] = await this.$to(this.$apis[this.apiList[this.type].list](params))
      this.isLoading = false
      if (err) {
        this.tableData = []
        // this.collect[0].value = 0
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results.map(v => {
          v.show_orgs_text_alias = v.show_orgs_alias.join('，')
          return v
        })
        this.totalCount = res.data.count
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 获取项目点
    async getOrgList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminMarketingPopupGetOrgsPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.searchSetting.show_orgs.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 编辑
    modifyHandle(type, data) {
      this.$router.push({
        name: this.type === 'super' ? 'SuperAddMobilePopup' : 'MerchantAddMobilePopup',
        params: {
          type: type
        },
        query: {
          role: this.type,
          data: data ? this.$encodeQuery(data) : ''
        }
      })
    },
    // 删除
    deleteHandle(type, row) {
      this.$confirm(`确定删除吗?`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            let deleteIds = [row.id]
            let params = {
              ids: deleteIds
            }
            const [err, res] = await this.$to(
              this.$apis[this.apiList[this.type].delete](params)
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1 && type === 'one') {
                  this.currentPage--
                } else if (this.currentPage === this.totalPageSize && deleteIds.length === this.tableData.length) {
                  this.currentPage--
                }
              }
              this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    switchStatus(id, status) {
      let apiStr = 'apiBackgroundAdminMarketingPopupChangeStatusPost'
      if (this.type === 'merchant') {
        apiStr = 'apiBackgroundMarketingMarketingPopupChangeStatusPost'
      }
      this.$apis[apiStr]({
        ids: [id],
        status: status
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.initLoad(true)
        } else {
          this.$message.error('修改失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.banner {
  .sumWrapper{
    padding-left: 20px;
    padding-bottom: 20px;
    ul, li { list-style: none; }
    li{
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }
}
</style>
