<template>
  <div class="ModuleAdmin container-wrapper">
    <!-- 一级组织下才展示配置 -->
    <div class="wrapper" v-if="isTopLevel">
      <el-form label-position="left" label-width="100px">
        <el-form-item
          ref="paymentQrcodeRefreshFormItem"
          label="付款码设置">
          有效时长
          <el-input v-model="paymentQrcodeRefresh" @blur="validatePaymentQrcodeRefresh" type="number" :max="180" :min="5" placeholder="请输入1到180的数字" class="ps-input"></el-input>
          分钟，结束后自动刷新。
          <div v-if="inputTimeError" class="tips-text">{{ inputTimeError }}</div>
          <div class="color-666">设置时间过长容易造成风险，影响用户的账户安全，请谨慎操作。</div>
        </el-form-item>

        <el-form-item label="功能配置">
          <el-button type="text" :disabled="isLoading" @click="visible = true">去设置</el-button>
          <span class="m-l-20">功能数量：{{ allMenuList.filter(item => item.isOpen).length }}/{{ allMenuList.length }}</span>
          <div class="color-666">功能设置后记得点击下面保存按钮才能生效。</div>
        </el-form-item>

        <el-form-item label=" ">
          <el-button size="small" :loading="isLoading" type="primary" class="ps-origin-btn w-100" @click="saveMenu">保存</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="no-data" v-else>
      <img class="empty-img" src="@/assets/img/table-no-data.png" alt="empty">
      <div class="no-data-text">因权限层级差异，本功能仅允许一级权限用户执行操作。</div>
    </div>

    <!-- 功能配置抽屉 -->
    <el-drawer
      v-if="isTopLevel"
      custom-class="ps-import-dialog-message"
      title="功能配置"
      :visible.sync="visible"
      :wrapperClosable="false"
      :show-close="false"
      @open="openDrawer"
      size="30%">
      <div class="drawer-main">
        <div class="tips-text m-b-10">注意：关闭后用户在移动端无法找到该功能，鼠标长按可进行拖拽排序。</div>
        <draggable
          v-model="allMenuList"
          ref="draggableContainer"
          :disabled="allowDrag"
          :scrollSensitivity="200"
          :scrollSpeed="10"
          animation="200"
          >
          <div v-for="(item, index) in allMenuList" :key="item.key" class="menu-item">
            <div class="menu-item-left" @mouseenter="startLongPress" @mouseleave="endLongPress">
              <img src="@/assets/img/draggable_icon.png" alt="拖拽图标"/>
              <el-tooltip v-if="index !== 0" placement="top" effect="light" content="置顶">
                <img @click="moveToTop(item.key)" class="top_draggable" src="@/assets/img/top_draggable.png" alt="置顶图标"/>
              </el-tooltip>
              <span style="margin-left: 8px;">{{ item.name }}</span>
            </div>
            <el-switch v-model="item.isOpen" @change="changeSceneHandle(item)" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </div>
        </draggable>
      </div>
      <span class="dialog-footer-drawer">
        <el-button class="ps-cancel-btn w-100" @click="cancelChanges">取消</el-button>
        <el-button class="ps-btn m-l-40 w-100" type="primary" @click="visible = false">保存</el-button>
        <span class="m-l-20">功能数量：{{ allMenuList.filter(item => item.isOpen).length }}/{{ allMenuList.length }}</span>
      </span>
    </el-drawer>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { to, deepClone } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  name: 'ModuleAdmin',
  components: { draggable },
  data() {
    return {
      visible: false,
      isLoading: false,
      inputTimeError: '',
      paymentQrcodeRefresh: '', // 付款码刷新时间 默认后台返回  5分钟
      allowDrag: false, // 控制是否允许拖拽
      longPressTimer: null, // 长按计时器
      longPressThreshold: 300, // 长按阈值
      menuKey: 0,
      allMenuList: [
        {
          icon: require('@/assets/img/h5/canteen_chongzhi.png'),
          name: '充值',
          key: 'charge',
          permission: 'charge',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/canteen_dingdan.png'),
          name: '全部订单',
          key: 'order',
          permission: 'order',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/canteen_yuyue.png'),
          name: '预约点餐/我的预约',
          key: 'reservation',
          permission: 'reservation',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/canteen_baocan.png'),
          name: '报餐',
          key: 'report',
          permission: 'report',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/canteen_zahnghu.png'),
          name: '账户信息',
          key: 'account_info',
          permission: 'account_info',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/buffet.png'),
          name: '托盘绑定',
          key: 'tray_bind',
          permission: 'tray_bind',
          isOpen: false
        },
        // {
        //   icon: require('@/assets/img/h5/my_appoint.png'),
        //   name: '我的预约',
        //   key: 'myReservation',
        //   permission: 'reservation',
        //   isOpen: false,
        // },
        {
          icon: require('@/assets/img/h5/jiaofei.png'),
          name: '缴费中心',
          key: 'jiaofei',
          permission: 'jiaofei',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/control_attendance.png'),
          name: '门禁考勤',
          key: 'attendance',
          permission: 'attendance',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/control_attendance.png'),
          name: '后勤门禁',
          key: 'myAttendance',
          permission: 'myAttendance',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/canteen_caipu.png'),
          name: '意向菜谱',
          key: 'intent_food',
          permission: 'intent_food',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/icon_review.png'),
          name: '审核查询',
          key: 'order_review',
          permission: 'order_review',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/sign_icon.png'),
          name: '免密支付',
          key: 'free_payment_setting',
          permission: 'free_payment_setting',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/feedback.png'),
          name: '食堂建议',
          key: 'shop_feedback',
          permission: 'shop_feedback',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/face_collect.png'),
          name: '人脸采集',
          key: 'face_collect',
          permission: 'face_collect',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/washing_system_black.png'),
          name: '洗衣中心',
          key: 'zk_laundry',
          permission: 'zk_laundry',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/icon-car.png'),
          name: '车辆管理',
          key: 'car_travel',
          permission: 'car_travel',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/marketing_notice.png'),
          name: '消息通知',
          key: 'marketing_notice',
          permission: 'marketing_notice',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/order_evaluation.png'),
          name: '我的评价',
          key: 'order_evaluation',
          permission: 'order_evaluation',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/invoice_record.png'),
          name: '开票记录',
          key: 'invoice_record',
          permission: 'invoice_record',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/abc_ebank.png'),
          name: '电子账户',
          key: 'ebank',
          permission: 'ebank',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/alipay_qy_code.png'),
          name: '支付宝企业码',
          key: 'alipay_qycode',
          permission: 'alipay_qycode',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/meal_apply.png'),
          name: '访客餐',
          key: 'approve_order',
          permission: 'approve_order',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/third_shop.png'),
          name: '商城',
          key: 'third_shop',
          permission: 'third_shop',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/meituan.png'),
          name: '美团',
          key: 'mei_tuan',
          permission: 'mei_tuan',
          isOpen: false
        },
        {
          icon: 'https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/bundle/images/coupon.png',
          name: '卡券中心',
          key: 'coupon',
          permission: 'coupon',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/img_smart_campus.png'),
          name: '智慧校园',
          key: 'zhxy',
          permission: 'zhxy',
          isOpen: false
        },
        {
          icon: require('@/assets/img/h5/voip.png'),
          name: '视频公话',
          key: 'voip',
          permission: 'voip',
          isOpen: false
        }
      ],
      lastClosedItem: null, // 新增：跟踪最近关闭的项
      originalAllMenuList: [], // 保存修改前的数据
      hasMenuList: [],
      nohasMenuList: []
      // sortList: [],
      // SortWrap: null
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization']),
    // 是否显示添加按钮，只有一级组织才能添加
    isTopLevel() {
      let show = false
      console.log('this.userInfo', this.userInfo)
      if (this.userInfo.orgs_level && this.userInfo.orgs_level[this.organization] === 0) {
        show = true
      }
      return show
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    /**
     * @method 初始化加载
     */
    initLoad() {
      this.getMenuList()
    },
    /**
     * @method 获取菜单列表
     */
    async getMenuList () {
      if (this.isLoading) return;
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationGetInfoPost({
          id: this.$store.getters.organization
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.paymentQrcodeRefresh = res.data.payment_qrcode_refresh
        this.hasMenuList = []
        res.data.already_permission.map(item => {
          this.allMenuList.forEach(menuItem => {
            if (item === menuItem.permission) {
              this.hasMenuList.push(menuItem)
              menuItem.isOpen = true
            }
          })
        })
        this.nohasMenuList = []
        res.data.not_app_permission.map(item => {
          this.allMenuList.forEach(menuItem => {
            if (item === menuItem.permission) {
              this.nohasMenuList.push(menuItem)
              menuItem.isOpen = false
            }
          })
        })
        // 关闭的权限菜单放在后面展示 并且 根据后台already_permission返回的排序规则进行排序
        this.allMenuList.sort((a, b) => {
          if (a.isOpen !== b.isOpen) {
            return a.isOpen ? -1 : 1
          }
          if (a.isOpen && b.isOpen) {
            const indexA = res.data.already_permission.indexOf(a.permission)
            const indexB = res.data.already_permission.indexOf(b.permission)
            return indexA - indexB
          }
          return 0
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * @method 拖拽时鼠标移入
     */
    startLongPress() {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = setTimeout(() => {
        this.allowDrag = false
      }, 300);
    },
    /**
     * @method 拖拽时鼠标移出
     */
    endLongPress() {
      clearTimeout(this.longPressTimer);
      this.allowDrag = true
    },
    /**
     * @method 切换场景开启/关闭菜单
     * @param item 当前项
     */
    changeSceneHandle(item) {
      // 预约点餐和我的预约权限相同 预约点餐权限打开，则我的预约权限也打开 关闭同理
      if (item.permission === 'reservation') {
        this.allMenuList.forEach(menuItem => {
          if (menuItem.permission === 'reservation') {
            menuItem.isOpen = item.isOpen
          }
        })
      }
      // 重新排序 allMenuList
      // 更新 lastClosedItem：仅当项被关闭时记录
      if (!item.isOpen) {
        this.lastClosedItem = item.permission
      } else {
        this.lastClosedItem = null // 重新打开时清空记录
      }

      // 重新排序菜单
      this.allMenuList.sort((a, b) => {
        // 1. 优先按 isOpen 排序（true 在前）
        if (a.isOpen !== b.isOpen) {
          return a.isOpen ? -1 : 1
        }
        // 2. 若均为 false，将最近关闭的项排到最后
        if (!a.isOpen && !b.isOpen) {
          if (a.permission === this.lastClosedItem) return 1
          if (b.permission === this.lastClosedItem) return -1
        }
        return 0
      })
    },
    // 打开抽屉
    openDrawer() {
      // 保存当前的 allMenuList 数据
      this.originalAllMenuList = deepClone(this.allMenuList)
    },
    // 抽屉点击取消
    cancelChanges() {
      // 恢复修改前的数据
      this.allMenuList = deepClone(this.originalAllMenuList)
      this.visible = false
    },
    /**
     * @method 有效时长失去焦点时验证输入的时长
     */
    validatePaymentQrcodeRefresh() {
      let value = this.paymentQrcodeRefresh
      if (value < 1 || value > 180) {
        this.inputTimeError = '有效时长请设置在1到180分钟之间'
      } else if (!/^\d+$/.test(value) || value.startsWith('0')) {
        this.inputTimeError = '有效时长请输入正整数'
      } else {
        this.inputTimeError = ''
      }
    },
    /**
     * @method 点击置顶功能
     */
    moveToTop(key) {
      const index = this.allMenuList.findIndex(item => item.key === key)
      if (index === -1) return
      const [movedItem] = this.allMenuList.splice(index, 1)
      this.allMenuList.unshift(movedItem)
    },

    /**
     * @method 保存
     */
    async saveMenu () {
      if (this.isLoading) return
      if (this.inputTimeError) return this.$message.error('请检查有效时长')
      this.isLoading = true
      let has = []
      let nohas = []
      // has = this.allMenuList.filter(item => item.isOpen).map(item => item.permission)
      // nohas = this.allMenuList.filter(item => !item.isOpen).map(item => item.permission)
      this.allMenuList.map(item => {
        if (item.isOpen) {
          has.push(item.permission)
        } else {
          nohas.push(item.permission)
        }
      })
      // let arr = this.allMenuList.map(item => item.key)
      const params = {
        already_permission: Array.from(new Set(has)),
        not_app_permission: Array.from(new Set(nohas)),
        id: this.$store.getters.organization,
        payment_qrcode_refresh: this.paymentQrcodeRefresh
      }
      // console.log('params',params)
      // this.isLoading = false
      // return false
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationModifyPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('成功')
        this.getMenuList()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  margin-bottom: 0;
  padding: 23px 20px;
  background: #e7e9ef !important;
}
::v-deep .el-form-item {
  margin-bottom: 5px;
}

.tips-text{
  color: #FF445B;
}
.color-666{
  color: #666666;
}
.ModuleAdmin{
  .wrapper{
    padding: 20px;
    margin-top: 20px;
    border-radius: 10px;
    background-color: #FFF;
    .ps-input{
      width: 180px;
    }
  }
  .dialog-footer-drawer{
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 100%;
    padding: 20px 0 20px 20px;
    background-color: #ffffff;
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.1); // 增加上边框阴影
  }
}

// 弹窗样式
.drawer-main{
  padding: 10px 30px 100px 30px;
  max-height: 100%; /* 根据实际情况调整 */
  overflow-y: auto;
  position: relative;
  .menu-item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 5px;
    background-color: #f5f5f5;
    border-radius: 10px;
    .menu-item-left{
      display: flex;
      align-items: center;
      cursor: move;
      width: 85%;
      .top_draggable{
        width: 25px;
        height: 25px;
        position: relative;
        top: 1px;
        cursor: pointer !important;
      }
    }
    .menu-item-right{
      width: 15%;
    }
  }
}

// 一级以下无权限样式
.no-data{
 position: absolute;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 .no-data-text{
  margin-top: 20px;
  color: #999999;
 }
}
</style>
