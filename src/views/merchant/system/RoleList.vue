<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="role-container">
      <!-- 组织结构 start -->
      <div class="organization-tree">
        <el-input
          class="tree-search ps-input"
          :placeholder="$t('placeholder.role_tree_search')"
          v-model="treeFilterText"
        ></el-input>
        <div
          :class="['all-tree', this.searchForm.id ? '' : 'is-current']"
          @click="treeHandleNodeClick('', 'all')"
        >
          <i class="tree-search-icon"><img src="@/assets/img/icon all.png" alt="" /></i>
          {{ $t('search.all') }}
        </div>
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :filter-node-method="filterTreeNode"
          :check-on-click-node="true"
          :default-expand-all="true"
          :highlight-current="true"
          :current-node-key="this.searchForm.id"
          :class="{ 'tree-box': searchForm.id }"
          ref="tree"
          @node-click="treeHandleNodeClick"
        ></el-tree>
      </div>
      <!-- end -->
      <!--  -->
      <div class="role-list">
        <search-form
          ref="searchRef"
          :form-setting="searchFormSetting"
          @search="searchHandle"
          :autoSearch="false"
        ></search-form>
        <!-- <div class="search-form ps-search-form flex-between">
          <div class="search-item search-l">
            <el-input v-model="searchForm.name" @input="searchHandle" :placeholder="$t('placeholder.role_search')" class="ps-input"></el-input>
            <el-button @click="searchHandle" icon="el-icon-search" class="ps-btn" style="margin-left: 10px;">{{ $t('search.btn') }}</el-button>
          </div>
          <div class="ps-inline">
            <el-button v-permission="['background_organization.role.add']" type="primary" class="ps-blue-btn" @click="openDialogHaldler('add')">
              <i class="ps-icon"><img class="ps-icon-img" src="@/assets/img/icon-01.png" alt="" /></i>
              {{ $t('search.role_add') }}
            </el-button>
            <el-button v-permission="['background_organization.role.list_export']" type="primary" class="ps-origin-btn" @click="gotoExport">
              <i class="ps-icon"><img class="ps-icon-img" src="@/assets/img/icon-02.png" alt="" /></i>
              {{ $t('search.export') }}
            </el-button>
            <el-button v-permission="['background_organization.role.delete']" type="primary" class="ps-red-btn" @click="deleteHaldler('mult')">
              <i class="ps-icon"><img class="ps-icon-img" src="@/assets/img/icon-07.png" alt="" /></i>
              {{ $t('search.multi_del') }}
            </el-button>
          </div>
        </div> -->
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <!-- <el-button v-permission="['background_organization.role.add']" type="primary" size="mini" class="ps-origin-btn" @click="openDialogHaldler('add')">
                <i class="ps-icon"><img class="ps-icon-img" src="@/assets/img/icon-01.png" alt="" /></i>
                {{ $t('search.role_add') }}
              </el-button>
              <el-button v-permission="['background_organization.role.list_export']" size="mini" class="ps-plain-btn" @click="gotoExport">
                <i class="ps-icon"><img class="ps-icon-img" src="@/assets/img/icon-02.png" alt="" /></i>
                {{ $t('search.export') }}
              </el-button>
              <el-button v-permission="['background_organization.role.delete']" size="mini" class="ps-plain-btn" @click="deleteHaldler('mult')">
                <i class="ps-icon"><img class="ps-icon-img" src="@/assets/img/icon-07.png" alt="" /></i>
                {{ $t('search.multi_del') }}
              </el-button> -->
              <button-icon
                :buttonData="buttonData"
                @openDialogHaldler="openDialogHaldler('add')"
                @gotoExport="gotoExport"
                @deleteHaldler="deleteHaldler('mult')"
              ></button-icon>
            </div>
          </div>
          <div class="table-content">
            <!-- table start -->
            <el-table
              v-loading="isLoading"
              :data="tableData"
              ref="tableData"
              style="width: 100%"
              row-key="id"
              stripe
              :row-class-name="tableRowClassName"
              header-row-class-name="ps-table-header-row"
              class="ps-table"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                type="selection"
                width="55"
                class-name="ps-checkbox"
              ></el-table-column>
              <el-table-column
                prop="name"
                show-overflow-tooltip
                :label="$t('table.role_name')"
              ></el-table-column>
              <el-table-column
                prop="organization_alias"
                show-overflow-tooltip
                align="right"
                :label="$t('table.account_organization')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.organization_alias | formatArrayToText }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="account_num"
                width="90"
                align="right"
                :label="$t('table.role_account_num')"
              ></el-table-column>
              <el-table-column
                prop="create_time"
                width="170"
                align="right"
                :label="$t('table.create_time')"
              ></el-table-column>
              <el-table-column
                class-name="tools-row"
                align="center"
                width="220"
                :label="$t('table.operate')"
              >
                <template slot-scope="scope">
                  <!-- v-if="checkPermission($store.getters.userInfo.account_type, scope.row.account_type)" -->
                  <el-button
                    v-if="$store.getters.userInfo.role_id !== scope.row.id"
                    v-permission="['background_organization.role.set_permission']"
                    type="text"
                    size="small"
                    @click="gotoRoleSetting(scope.row)"
                  >
                    {{ $t('table.role_setting_btn') }}
                  </el-button>
                  <el-button
                    v-permission="['background_organization.role.account_list']"
                    type="text"
                    size="small"
                    @click="openDialogHaldler('account', scope.row)"
                  >
                    {{ $t('table.role_show_account_btn') }}
                  </el-button>
                  <el-button
                    v-permission="['background_organization.role.modify']"
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="openDialogHaldler('edit', scope.row)"
                  >
                    {{ $t('table.edit') }}
                  </el-button>
                  <el-button
                    v-permission="['background_organization.role.delete']"
                    type="text"
                    size="small"
                    class="ps-warn-text"
                    v-if="scope.row.can_delete"
                    @click="deleteHaldler('one', scope.row)"
                  >
                    {{ $t('table.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- table end -->
          </div>
          <!-- 分页 start -->
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-size="pageSize"
              layout="total, prev, pager, next, jumper"
              :total="totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
          <!-- 分页 end -->
        </div>
        <!-- 编辑/添加弹窗 start -->
        <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogVisible"
          :width="dialogWidth"
          top="20vh"
          custom-class="ps-role-dialog"
          :close-on-click-modal="false"
          @closed="dialogHandleClose"
        >
          <el-form
            v-if="dialogType === 'add' || dialogType === 'edit'"
            ref="dialogFormRef"
            v-loading="dialogLoading"
            :rules="dialogFormDataRuls"
            :model="dialogFormData"
            class="dialog-form ps-small-box"
            label-width="120px"
            size="small"
          >
            <!-- <template v-if="dialogType === 'add'">
              <el-form-item v-for="(name, i) in dialogFormData.nameList" :key="i" :label="$t('form.role_add_name')" :rules="dialogFormDataRuls.name" :prop="'nameList['+i+']'">
                <div class="flex">
                  <el-input class="ps-input" v-model="dialogFormData.nameList[i]"></el-input>
                  <img v-if="dialogFormData.nameList.length>0&&dialogFormData.nameList.length-1===i" @click="clickChangeRoleName('add',i)" class="add_img form-img" src="@/assets/img/ad1.png" alt="add">
                  <img v-else @click="clickChangeRoleName('del',i)" class="del_img form-img" src="@/assets/img/ad2.png" alt="del">
                </div>
              </el-form-item>
            </template> -->
            <el-form-item
              v-if="dialogType === 'add' || dialogType === 'edit'"
              :label="$t('form.role_add_name')"
              prop="name"
            >
              <el-input class="ps-input" v-model="dialogFormData.name"></el-input>
            </el-form-item>
            <el-form-item
              v-if="dialogType === 'add' || dialogType === 'edit'"
              :label="$t('form.role_select_organization')"
            >
              <organization-select
                class="search-item-w ps-input"
                placeholder="请选择所属组织"
                :multiple="true"
                :check-strictly="true"
                v-model="dialogFormData.organization"
                :append-to-body="true"
                :disabled-level="disabledLevel"
              ></organization-select>
            </el-form-item>
          </el-form>
          <div class="" v-if="dialogType === 'account'">
            <el-table
              v-loading="dialogLoading"
              :data="accountData"
              ref="dialogTableData"
              style="width: 100%"
              :height="accountData.length > 6 ? '60vh' : '350'"
              stripe
              :row-class-name="tableRowClassName"
              header-row-class-name="ps-table-header-row"
              class="ps-table"
            >
              <el-table-column
                prop="username"
                show-overflow-tooltip
                :label="$t('table.account_username')"
              ></el-table-column>
              <el-table-column
                prop="member_name"
                show-overflow-tooltip
                :label="$t('table.account_member_name')"
              ></el-table-column>
              <el-table-column
                prop="create_time"
                width="170"
                align="right"
                :label="$t('table.create_time')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.create_time | formatDate }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button
              v-if="dialogType !== 'account'"
              class="ps-cancel-btn"
              :disabled="dialogLoading"
              @click="dialogVisible = false"
            >
              {{ $t('dialog.cancel_btn') }}
            </el-button>
            <el-button
              class="ps-origin-btn"
              :disabled="dialogLoading"
              type="primary"
              @click="submitDialogHandler('dialogFormRef')"
            >
              {{
                dialogType === 'edit'
                  ? $t('dialog.confirm_btn')
                  : dialogType === 'add'
                  ? $t('dialog.add_btn')
                  : $t('dialog.close_btn')
              }}
            </el-button>
          </span>
        </el-dialog>
        <!-- 弹窗 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { to, debounce } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import OrganizationSelect from '@/components/OrganizationSelect'

export default {
  name: 'RoleList',
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  components: {
    OrganizationSelect
  },
  data() {
    return {
      treeLoading: false,
      treeList: [],
      treeFilterText: '',
      treeProps: {
        children: 'children_list',
        label: 'name'
      },
      searchForm: {
        name: '',
        id: ''
      },
      checkList: [],
      tableData: [], // 列表数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      totalPageSize: 0, // 总页数
      currentPage: 1, // 第几页
      isLoading: false,
      dialogData: {},
      dialogTitle: '',
      dialogType: '',
      dialogVisible: false,
      dialogLoading: false,
      accountData: [],
      dialogWidth: '380px',
      dialogFormData: {
        id: '',
        organization: [],
        permission: '',
        nameList: [''],
        name: '',
        status: ''
      },
      dialogFormDataRuls: {
        name: [
          { required: true, message: this.$t('placeholder.role_name_empty'), trigger: 'blur' }
        ],
        organization: [
          {
            required: true,
            message: this.$t('placeholder.role_organization_empty'),
            trigger: 'blur'
          }
        ]
      },
      organizationOpts: {
        multiple: true,
        checkStrictly: true,
        value: 'id',
        label: 'name',
        children: 'children_list'
      },
      time: new Date().getTime(),
      searchFormSetting: {
        name: {
          type: 'input',
          label: '',
          value: '',
          placeholder: this.$t('placeholder.role_search')
        }
      },
      buttonData: [
        {
          name: this.$t('search.role_add'),
          click: 'openDialogHaldler',
          type: 'add',
          color: 'origin',
          permission: ['background_organization.role.add']
        },
        {
          name: this.$t('search.export'),
          click: 'gotoExport',
          type: 'export',
          color: 'plain',
          permission: ['background_organization.role.list_export']
        },
        {
          name: this.$t('search.multi_del'),
          click: 'deleteHaldler',
          type: 'del',
          color: 'plain',
          permission: ['background_organization.role.delete']
        }
      ],
      rule: [3, 2, 1, 5, 0, 4, 6], // 操作员没权限操作的0，4，6是操作员
      disabledLevel: 1
    }
  },
  watch: {
    treeFilterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getOrganizationTreeList()
      this.getRoleList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.searchForm.id = ''
      this.currentPage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getRoleList()
      }
    }, 300),
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      this.treeLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationTreeListPost())
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.treeList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取角色列表
    async getRoleList() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationRoleListPost({
          organization__in: this.searchForm.id ? [this.searchForm.id] : [],
          name__contains: this.searchFormSetting.name.value,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.totalCount = res.data.count
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
        // this.tableData = res.data.results
        this.tableData = []
        res.data.results.forEach(item => {
          if (item.status === 1) {
            this.tableData.push(item)
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 检查下权限的操作
    checkPermission(accountType, listType) {
      let hasPermission = false
      let index =
        this.rule.indexOf(accountType) > -1 ? this.rule.indexOf(accountType) + 1 : this.rule.length
      let permission = this.rule.slice(index)
      if (permission.includes(listType)) {
        hasPermission = true
      }
      return hasPermission
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // 点击tree node
    treeHandleNodeClick(e, type) {
      console.log(e, type)
      this.searchForm.id = e.id
      // this.searchForm.id = ''
      if (type === 'all') {
        this.searchForm.id = ''
      }
      this.searchHandle()
    },
    // 过滤tree数据
    filterTreeNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 列表选择
    handleSelectionChange(e) {
      this.checkList = e.map(item => {
        return item.id
      })
    },
    deleteHaldler(type, data) {
      let deleteIds = []
      if (type === 'one') {
        deleteIds = [data.id]
      } else {
        deleteIds = this.checkList
      }
      if (!deleteIds.length) {
        return this.$message.error(this.$t('message.role_select_empty'))
      }
      this.$confirm(`${this.$t('message.role_delete_select')}？`, this.$t('message.delete'), {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundOrganizationRoleDeletePost({
                ids: deleteIds
              })
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              if (type === 'mult') {
                this.checkList = []
              }
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1 && type === 'one') {
                  this.currentPage--
                } else if (
                  this.currentPage === this.totalPageSize &&
                  deleteIds.length === this.tableData.length
                ) {
                  this.currentPage--
                }
              }
              this.getRoleList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getRoleList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRoleList()
    },
    // 打开弹窗
    openDialogHaldler(type, data) {
      this.dialogType = type
      this.dialogData = data
      // 当前登录账号所属的层级禁止
      this.disabledLevel = 0
      switch (type) {
        case 'account':
          this.dialogWidth = '420px'
          this.dialogTitle = this.$t('dialog.account_title')
          this.getAccountList()
          break
        case 'edit':
          this.dialogWidth = '380px'
          this.dialogTitle = this.$t('dialog.edit_title')
          this.dialogFormData.id = data.id
          if (data.organization.length) {
            this.dialogFormData.organization = data.organization
            // this.dialogFormData.organization = data.organization.map(item => {
            //   return getTreeDeepArr(this.treeList, item, 'id', 'children_list')
            // })
          }
          // this.dialogFormData.organization = data.organization
          this.dialogFormData.name = data.name
          if (this.$store.getters.userInfo.role_id === data.id) {
            this.disabledLevel = 1
          } else {
            this.disabledLevel = 0
          }
          break
        case 'add':
          this.dialogWidth = '380px'
          this.dialogTitle = this.$t('dialog.add_title')
          break
      }
      this.dialogVisible = true
    },
    // 获取当前角色下的账号列表
    async getAccountList() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationRoleAccountListPost({
          id: this.dialogData.id
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.accountData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加角色+-操作
    clickChangeRoleName(type, index) {
      if (type === 'add') {
        this.dialogFormData.nameList.push('')
      } else {
        this.dialogFormData.nameList.splice(index, 1)
      }
    },
    submitDialogHandler(refType) {
      if (this.dialogType === 'account') {
        this.dialogVisible = false
      } else {
        this.$refs[refType].validate(valid => {
          if (valid) {
            if (this.dialogLoading) return
            if (this.dialogType === 'add') {
              this.addRoleHandle()
            } else {
              this.modifyRoleHandle()
            }
          }
        })
      }
    },
    async addRoleHandle() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationRoleAddPost({
          name: this.dialogFormData.name,
          organization: this.dialogFormData.organization
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.dialogVisible = false
        this.getRoleList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async modifyRoleHandle() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationRoleModifyPost({
          id: this.dialogFormData.id,
          name: this.dialogFormData.name,
          organization: this.dialogFormData.organization
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.dialogVisible = false
        this.getRoleList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转权限设置页面
    gotoRoleSetting(data) {
      this.$router.push({
        name: 'MerchantRoleSetting',
        query: {
          id: data.id
        }
      })
    },
    // 关闭弹窗回调
    dialogHandleClose(e) {
      // 重置数据
      if (this.dialogType === 'add' || this.dialogType === 'edit') {
        this.$refs.dialogFormRef.clearValidate()
        this.dialogFormData = {
          id: '',
          organization: [],
          permission: '',
          nameList: [''],
          name: '',
          status: ''
        }
      }
      this.dialogData = {}
      this.dialogTitle = ''
      this.dialogType = ''
      this.accountData = []
    },
    gotoExport() {
      const option = {
        type: 'RoleList',
        params: {
          organization__in: this.searchForm.id ? [this.searchForm.id] : [],
          name: this.searchForm.name,
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
#role-container {
  display: flex;
  .role-list {
    flex: 1;
    min-width: 0;
    // background-color: ;
  }
}
.ps-role-dialog {
  .dialog-form {
    .flex {
      display: flex;
      align-items: center;
    }
    .form-img {
      display: inline-block;
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
      opacity: 0.8;
      &:hover {
        opacity: 1;
      }
    }
  }
}
</style>
