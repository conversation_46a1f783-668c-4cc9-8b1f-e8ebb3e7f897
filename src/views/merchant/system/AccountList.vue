<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="account-container">
      <!-- 组织结构 start -->
      <div class="organization-tree">
        <el-input
          class="tree-search ps-input"
          :placeholder="$t('placeholder.role_tree_search')"
          v-model="treeFilterText">
        </el-input>
        <div :class="['all-tree', this.searchForm.id?'':'is-current']" @click="treeHandleNodeClick('', 'all')"><i class="tree-search-icon"><img src="@/assets/img/icon all.png" alt="" /></i>{{ $t('search.all') }}</div>
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :filter-node-method="filterTreeNode"
          :check-on-click-node="true"
          :default-expand-all="true"
          :highlight-current="true"
          :current-node-key="this.searchForm.id"
          :class="{ 'tree-box': searchForm.id} "
          ref="tree"
          @node-click="treeHandleNodeClick"
        ></el-tree>
      </div>
      <!-- end -->
      <!--  -->
      <div class="account-list">
        <search-form class="search-lt-shadow" ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <button-icon
                color="origin"
                type="add"
                @click="openDialogHaldler('add')"
                v-permission="['background_organization.account.add']">
                {{this.$t('search.account_add')}}
              </button-icon>
              <button-icon
                color="plain"
                type="export"
                @click="gotoExport"
                v-permission="['background_organization.account.list_export']">
                {{this.$t('search.export')}}
              </button-icon>
              <button-icon
                color="plain"
                type="del"
                @click="deleteHaldler('mult')"
                v-permission="['background_organization.account.delete']">
                批量停用
              </button-icon>
              <button-icon color="plain" v-permission="['background_organization.account.batch_import']" type="Import" @click="openImport('mulImportAccount')">批量导入账号</button-icon>
              <button-icon color="plain" v-permission="['background_organization.account.batch_import_user_faces']" type="Import" @click="gotoImportFace">批量导入人脸</button-icon>
            </div>
          </div>
        <div class="table-content">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            row-key="id"
            stripe
            :row-class-name="tableRowClassName"
            header-row-class-name="ps-table-header-row"
            class="ps-table"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" class-name="ps-checkbox" :selectable="selectableHandle"></el-table-column>
            <el-table-column prop="username" show-overflow-tooltip :label="$t('table.account')"></el-table-column>
            <el-table-column prop="mobile" show-overflow-tooltip :label="$t('table.mobile')"></el-table-column>
            <el-table-column prop="member_name" show-overflow-tooltip :label="$t('table.account_member_name')"></el-table-column>
            <el-table-column prop="role_alias" show-overflow-tooltip :label="$t('table.role_name')"></el-table-column>
            <el-table-column prop="organization_alias" show-overflow-tooltip align="right" :label="$t('table.account_organization')">
              <template slot-scope="scope">
                <span>{{ scope.row.organization_alias | formatArrayToText }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status_alias" align="right" :label="$t('table.status')">
              <template slot-scope="scope">
                <span :class="['status-s', scope.row.status===1?'yellow':'red']">{{scope.row.status_alias}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" show-overflow-tooltip align="right" :label="$t('table.create_time')" ></el-table-column>
            <el-table-column class-name="tools-row" align="center" width="220" :label="$t('table.operate')">
              <template slot-scope="scope">
                <el-button
                  v-permission="['background_organization.account.modify']"
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="openDialogHaldler('edit', scope.row)"
                >
                  {{ $t('table.edit') }}
                </el-button>
                <el-button
                  v-permission="['background_organization.account.delete']"
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  v-if="scope.row.status !== 0"
                  :disabled="!scope.row.can_delete"
                  @click="deleteHaldler('one', scope.row)"
                >
                  {{ scope.row.status === 1 ? '停用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- table end -->
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next,sizes,jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
        </div>
      </div>
      <!-- 编辑/添加弹窗 start -->
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="460px"
        top="16vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
        @closed="dialogHandleClose"
      >
        <el-form
          v-if="dialogType === 'add' || dialogType === 'edit'"
          ref="dialogFormRef"
          v-loading="dialogLoading"
          :rules="dialogFormDataRuls"
          :model="dialogFormData"
          class="dialog-form"
          label-width="100px"
          size="small"
        >
          <el-form-item prop="member_name" label="用户名称">
            <el-input class="ps-input" v-model="dialogFormData.member_name" placeholder="请输入用户名称"></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="username">
            <el-input class="ps-input" :disabled="dialogType === 'edit'" v-model="dialogFormData.username" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item label="账号密码" prop="password">
            <el-input class="ps-input" v-model="dialogFormData.password" :placeholder="dialogType==='add' ? '请输入密码' : '无需修改密码则不填'"></el-input>
            <div style="margin-top:3px; color: #F56C6C; line-height: 1; font-size: 12px;">密码有效期为90天，请在期限前重置密码</div>
          </el-form-item>
          <el-form-item label="管理卡号" prop="manage_card">
            <el-input class="ps-input" v-model="dialogFormData.manage_card" placeholder="请输入管理卡号"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input class="ps-input" v-model="dialogFormData.mobile" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item class="" label="" prop="">
            <el-checkbox
              v-model="dialogFormData.is_double_factor"
              class="ps-checkbox"
            >
              手机验证码验证登录
            </el-checkbox>
          </el-form-item>
          <el-form-item label="选择角色" prop="role">
            <!-- <el-select v-model="dialogFormData.role" style="width: 100%;" :placeholder="$t('placeholder.account_search_status')">
              <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id">
              </el-option>
            </el-select> -->
            <custom-select :select.sync="dialogFormData.role" shape="square" style="width: 100%;" :placeholder="$t('placeholder.account_search_role')" :select-list="roleList" />
          </el-form-item>
          <el-form-item label="选择状态" prop="status">
            <!-- <el-checkbox-group v-model="dialogFormData.status" @change="changeDialogStatus" class="ps-checkbox">
              <el-checkbox :label="1">启用</el-checkbox>
              <el-checkbox :label="0">停用</el-checkbox>
            </el-checkbox-group> -->
            <el-radio-group v-model="dialogFormData.status" class="ps-custom-radio">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0" :disabled="dialogType === 'edit'?!dialogData.can_delete:false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <template #label>
              <span>上传人脸</span>
              <!-- <el-tooltip class="item" effect="dark" content="注：仅支持jpg格式文件大小不超过1M，分辨率不得超过1280*1280" placement="right">
                <i class="el-icon-question" style="cursor: pointer;"></i>
              </el-tooltip> -->
            </template>
            <file-upload
              ref="faceFileRef"
              :fileList="dialogFormData.fileLists"
              type="enclosure"
              :before-upload="beforeUpload"
              @fileLists="getFileLists"
              class="avatar-uploader"
              :show-file-list="false"
            >
              <img v-if="dialogFormData.fileLists.length" :src="dialogFormData.fileLists[0].url" class="avatar" @click="clearFileHandle">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </file-upload>
            <div class="tips">注：仅支持jpg格式文件大小不超过1M，分辨率不得超过1280*1280</div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="dialogType !== 'account'" class="ps-cancel-btn" :disabled="dialogLoading" @click="cancelDialog">
            {{ $t('dialog.cancel_btn') }}
          </el-button>
          <el-button
            class="ps-origin-btn"
            :disabled="dialogLoading"
            type="primary"
            @click="submitDialogHandler('dialogFormRef')"
          >
            {{ dialogType==='edit' ? $t('dialog.confirm_btn') : (dialogType==='add'?$t('dialog.add_btn'):$t('dialog.close_btn')) }}
          </el-button>
        </span>
      </el-dialog>
      <!-- 导入账号的弹窗 start -->
      <import-dialog-drawer
        :templateUrl="templateUrl"
        :tableSetting="tableSetting"
        :show.sync="importShowDialog"
        :title="importDialogTitle"
        :openExcelType="openExcelType"
      ></import-dialog-drawer>
    <!-- 导入数据的弹窗 end -->
      <!-- 弹窗 end -->
    </div>
  </div>
</template>

<script>
import md5 from 'js-md5';
import { to, debounce } from '@/utils'
import { validateTelphone } from '@/assets/js/validata'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'AccountList',
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    let validateAccount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("账号不能为空"));
      } else {
        // let regNum = /(^\d{3,12}$)|(^[a-zA-Z]{3,12}$)/;
        let regNum = /^\w{5,20}$/;
        if (!regNum.test(value)) {
          callback(
            new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合")
          );
        } else {
          callback();
        }
      }
    };
    let validatePass = (rule, value, callback) => {
      // let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      let regPass = /(^\w{8,20}$)/;
      if (!value) {
        return callback();
      } else {
        if (!regPass.test(value)) {
          callback(new Error("密码长度8~20位，英文加数字"));
        } else {
          callback();
        }
      }
    };
    return {
      treeLoading: false,
      treeList: [],
      treeFilterText: '',
      treeProps: {
        children: 'children_list',
        label: 'name'
      },
      searchForm: {
        username: '',
        id: '',
        member_name: '',
        status: 1
      },
      checkList: [],
      tableData: [], // 列表数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      totalPageSize: 0, // 总页数
      currentPage: 1, // 第几页
      isLoading: false,
      dialogData: {},
      dialogTitle: '',
      dialogType: '',
      dialogVisible: false,
      dialogLoading: false,
      accountData: [],
      dialogFormData: {
        id: '',
        member_name: '',
        username: '',
        password: '',
        manage_card: '',
        status: '',
        role: '',
        mobile: '',
        fileLists: [],
        is_double_factor: false
      },
      dialogFormDataRuls: {
        role: [
          { required: true, message: this.$t('placeholder.role_name_empty'), trigger: "blur" }
        ],
        member_name: [
          { required: true, message: '用户名称不能为空', trigger: "blur" }
        ],
        manage_card: [
          { required: false }
        ],
        username: [
          { required: true, message: this.$t('placeholder.account_name_empty'), trigger: "blur" },
          { validator: validateAccount, trigger: "blur" }
        ],
        password: [
          { required: false, message: this.$t('placeholder.role_password_empty'), trigger: "blur" },
          { validator: validatePass, trigger: "blur" }
        ],
        status: [
          { required: true, message: this.$t('placeholder.account_status_empty'), trigger: "blur" }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: "blur" },
          { validator: validateTelphone, trigger: "change" }
        ]
      },
      roleList: [],
      time: new Date().getTime(),
      searchFormSetting: {
        username: {
          type: 'input',
          label: '账号',
          value: '',
          placeholder: ''
        },
        member_name: {
          type: 'input',
          label: '用户名称',
          value: '',
          placeholder: ''
        },
        status: {
          type: 'select',
          label: '状态',
          value: 1,
          placeholder: '',
          dataList: [{
            label: '启用',
            value: 1
          }, {
            label: '禁用',
            value: 0
          }]
        }
      },
      // 导入账号的弹窗数据
      importDialogTitle: '',
      importShowDialog: false,
      templateUrl: '',
      openExcelType: '',
      tableSetting: []
    }
  },
  watch: {
    treeFilterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
      this.getOrganizationTreeList()
      this.getAccountList()
      this.getRoleList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.searchForm.id = ''
      this.currentPage = 1;
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getAccountList()
      }
    }, 300),
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      this.treeLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationTreeListPost()
      )
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.treeList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取账号列表
    async getAccountList () {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationAccountListPost({
          role__organization__in: [this.searchForm.id],
          username: this.searchFormSetting.username.value,
          member_name: this.searchFormSetting.member_name.value,
          status: this.searchFormSetting.status.value,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
        this.tableData = res.data.results
        console.log(this.tableData, '账号数据');
        // this.tableData = []
        // res.data.results.forEach(item => {
        //   if (item.status === 1) {
        //     this.tableData.push(item)
        //   }
        // })
      } else {
        this.$message.error(res.msg)
        this.tableData = []
      }
    },
    // 获取角色列表
    async getRoleList () {
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationRoleListPost({
          organization__in: [],
          name: '',
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.totalCount = res.data.count
        // this.tableData = res.data.results
        this.roleList = []
        res.data.results.forEach(item => {
          if (item.status === 1) {
            this.roleList.push(item)
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // 点击tree node
    treeHandleNodeClick(e, type) {
      this.searchForm.id = e.id
      if (type === 'all') {
        this.searchForm.id = ''
      }
      this.searchHandle()
    },
    // 过滤tree数据
    filterTreeNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 列表选择
    handleSelectionChange(e) {
      this.checkList = e.map(item => {
        return item.id
      })
    },
    deleteHaldler(type, data) {
      let deleteIds = []
      if (type === 'one') {
        deleteIds = [data.id]
      } else {
        deleteIds = this.checkList
      }
      if (!deleteIds.length) {
        return this.$message.error(this.$t('message.role_select_empty'))
      }
      this.$confirm(`是否停用当前账号？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundOrganizationAccountDeletePost({
                ids: deleteIds
              })
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              if (type === 'mult') {
                this.checkList = []
              }
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1 && type === 'one') {
                  this.currentPage--
                } else if (this.currentPage === this.totalPageSize && deleteIds.length === this.tableData.length) {
                  this.currentPage--
                }
              }
              this.getAccountList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getAccountList()
    },
    // 打开弹窗
    openDialogHaldler(type, data) {
      this.resetDialogForm()
      this.dialogType = type
      this.dialogData = data
      switch (type) {
        case 'account':
          this.dialogTitle = this.$t('dialog.account_title')
          break;
        case 'edit': {
          this.dialogTitle = this.$t('dialog.edit_title')
          this.dialogFormData.id = data.id
          let role = this.roleList.filter(v => {
            return v.id === data.role
          })
          this.dialogFormData.role = role.length ? role[0].id : ''
          this.dialogFormData.username = data.username
          this.dialogFormData.member_name = data.member_name
          this.dialogFormData.manage_card = data.manage_card || ''
          this.dialogFormData.status = data.status
          this.dialogFormData.mobile = data.mobile
          this.dialogFormDataRuls.password[0].required = false
          if (data.face_url) {
            let name = new Date().getTime()
            this.dialogFormData.fileLists = [{
              name: name,
              url: data.face_url,
              status: "success",
              uid: name
            }]
          }
          this.dialogFormData.is_double_factor = data.is_double_factor
          break;
        }
        case 'add':
          this.dialogTitle = this.$t('dialog.add_title')
          this.dialogFormDataRuls.password[0].required = true
          this.dialogFormData.status = 1
          break;
      }
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    submitDialogHandler(refType) {
      if (this.dialogType === 'account') {
        this.dialogVisible = false
      } else {
        this.$refs[refType].validate(valid => {
          if (valid) {
            if (this.dialogLoading) return;
            if (this.dialogType === 'add') {
              this.addAccountHandle()
            } else {
              this.modifyAccountHandle()
            }
          }
        })
      }
    },
    resetDialogForm() {
      let obj = {
        id: '',
        member_name: '',
        username: '',
        password: '',
        manage_card: '',
        status: '',
        role: '',
        mobile: '',
        fileLists: [],
        is_double_factor: false
      }
      this.dialogFormData = { ...obj }
    },
    cancelDialog() {
      this.$refs.dialogFormRef.resetFields()
      this.resetDialogForm()
      this.$nextTick(() => {
        this.dialogVisible = false
      })
    },
    getFileLists(fileLists) {
      this.dialogFormData.fileLists = fileLists
      console.log('fileLists', fileLists)
    },
    beforeUpload(file) {
      let unUploadType = ['.jpg']
      if (!unUploadType.includes(this.getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式')
        return false
      }
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        return false
      }
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    clearFileHandle() {
      console.log('clear')
      this.$refs.faceFileRef.clearHandle()
      this.dialogFormData.fileLists = []
    },
    async addAccountHandle() {
      this.dialogLoading = true
      let params = {
        member_name: this.dialogFormData.member_name,
        username: this.dialogFormData.username,
        password: md5(this.dialogFormData.password),
        status: this.dialogFormData.status,
        role: this.dialogFormData.role,
        mobile: this.dialogFormData.mobile,
        is_double_factor: this.dialogFormData.is_double_factor,
        manage_card: this.dialogFormData.manage_card
      }
      if (this.dialogFormData.fileLists.length) {
        params.face_url = this.dialogFormData.fileLists[0].url
      } else {
        params.face_url = null
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationAccountAddPost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.dialogVisible = false
        this.getAccountList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async modifyAccountHandle() {
      this.dialogLoading = true
      let params = {}
      for (let key in this.dialogFormData) {
        switch (key) {
          case 'password':
            if (this.dialogFormData[key]) {
              params[key] = md5(this.dialogFormData[key])
            }
            break;
          case 'fileLists':
            if (this.dialogFormData[key].length) {
              params.face_url = this.dialogFormData.fileLists[0].url
            } else {
              params.face_url = null
            }
            break;
          default:
            params[key] = this.dialogFormData[key]
            break;
        }
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationAccountModifyPost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.dialogVisible = false
        this.getAccountList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转权限设置页面
    gotoRoleSetting(data) {
      this.$router.push({
        // path: '/organization/role_setting',
        name: 'MerchantRoleSetting',
        query: {
          id: data.id
        }
      })
    },
    changeDialogStatus(e) {
      if (e.length > 1) {
        this.dialogFormData.status.splice(0, 1)
      }
    },
    // 关闭弹窗回调
    dialogHandleClose(e) {
      // 重置数据
      if (this.dialogType === 'add' || this.dialogType === 'edit') {
        this.$refs.dialogFormRef.clearValidate()
        this.dialogFormData = {
          id: '',
          member_name: '',
          username: '',
          password: '',
          status: '',
          mobile: '',
          role: '',
          fileLists: [],
          is_double_factor: false
        }
      }
      this.dialogData = {}
      this.dialogTitle = ''
      this.dialogType = ''
    },
    // 导出
    gotoExport() {
      const option = {
        type: "AccountList",
        url: 'apiBackgroundOrganizationAccountListExportPost',
        params: {
          id: this.searchForm.id,
          username: this.searchForm.username,
          member_name: this.searchForm.member_name,
          page: this.currentPage,
          page_size: this.pageSize,
          status: this.searchForm.status
        }
      }
      this.exportHandle(option)
    },
    openImport(type) {
      this.importShowDialog = true
      this.importDialogTitle = '批量导入账号'
      this.templateUrl = location.origin + '/api/temporary/template_excel/account_import_template.xlsx'
      this.openExcelType = type
      this.tableSetting = [
        { key: 'name', label: '用户名称' },
        { key: 'account', label: '账号' },
        { key: 'password', label: '账号密码' },
        { key: 'phone', label: '手机号' },
        { key: 'role', label: '角色' }
      ]
    },
    // 批量上传人脸
    gotoImportFace() {
      this.$router.push({
        name: 'MerchantMulImportFace'
      })
    },
    selectableHandle(row, index) {
      return row.can_delete
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

#account-container {
  display: flex;
  .account-list {
    flex: 1;
    min-width: 0;
    .status-s{
      border-radius: 25px;
      display: inline-block;
      padding: 2px 10px 2px 15px;
      color: #FFFFFF;
      position: relative;
      font-size: 12px;
      line-height: 1.2;
      &.yellow{
        background-color: #49d498;
      }
      &.red{
        background-color: #fd594e;
      }
      &::before{
        content: ' ';
        position: absolute;
        left: 7px;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: #FFFFFF;
      }
    }
  }
}
.ps-dialog{
  .dialog-form{
    padding-right: 60px;
    .flex{
      display: flex;
      align-items: center;
    }
    .form-img{
      display: inline-block;
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
      opacity: .8;
      &:hover{
        opacity: 1;
      }
    }
    .is-error{
      margin-bottom: 30px !important;
    }
    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 60px;
      height: 60px;
      line-height: 60px;
      text-align: center;
    }
    .avatar {
      width: 60px;
      height: 60px;
      display: block;
    }
    .tips{
      line-height: 18px;
      font-size: 12px;
      color: #a2a2a2;
    }
  }
}
</style>
