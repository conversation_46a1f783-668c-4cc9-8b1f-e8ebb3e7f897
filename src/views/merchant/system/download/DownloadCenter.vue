<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <!-- <div class="table-title">数据列表</div> -->
        <div class="m-l-20">导出文件仅保存30天，请尽快下载！</div>
        <div class="align-r">
          <!-- v-permission="['background_download_center.download_record.delete']" -->
          <!-- <button-icon color="origin" @click="gotoHandle('add')">新增</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button v-if="row.file_url" :disabled="row.handle_status !== 'finish'" type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
              <el-button v-if="row.file_url" :disabled="row.handle_status !== 'finish'" type="text" size="small" class="ps-text" @click="downloadFile('export', row)">下载</el-button>
              <el-button type="text" size="small" :disabled="row.handle_status !== 'finish'" class="ps-text" @click="clickOperationHandle('delete', row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange, getUrlFilename, encodeQuery } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import FileSaver from 'file-saver'

export default {
  name: 'DownloadCenter',
  mixins: [exportExcel],
  data() {
    const defaultdate = getSevenDateRange(7);
    return {
      isLoading: false, // 刷新数据
      tabType: 1,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '文件名称', key: 'file_name' },
        { label: '导出模块', key: 'export_module_alias' },
        { label: '处理进度', key: 'handle_status_alias' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作时间', key: 'create_time' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '操作时间',
          format: 'yyyy-MM-dd',
          clearable: false,
          value: [defaultdate[0], defaultdate[1]]
        },
        handle_status: {
          type: 'select',
          value: '',
          label: '处理进度',
          // multiple: true,
          clearable: true,
          placeholder: '请选择',
          dataList: [
            { label: '完成', value: 'finish' },
            { label: '失败', value: 'fail' }
            // { label: '处理中', value: 'processing' }
          ]
        },
        export_module: {
          type: 'select',
          value: '',
          label: '导出模块',
          placeholder: '请选择',
          dataList: [],
          listNameKey: 'value',
          listValueKey: 'key'
        },
        operator_name: {
          type: 'input',
          value: '',
          label: '操作人',
          placeholder: '请输入'
        }
      },
      showDialog: false,
      dialogLoading: false
    }
  },
  created() {
    this.initLoad()
    this.getExportModuleList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDownloadList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getDownloadList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.getDownloadList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDownloadList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationReportDownloadListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取导出模块数据
    async getExportModuleList() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationReportDownloadExportModuleListPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.export_module.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDownloadList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundOrganizationReportDownloadDeletePost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'export':
          title = '确定导出xxx吗？'
          apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          this.showOperationDialog(title, apiUrl, { ids: [data.id] })
          break;
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis[apiUrl](params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getDownloadList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    showDialogHandle(data) {
      this.showDialog = true
    },
    closeDialogHandle() {
      this.showDialog = false
    },
    clickDialogConfirm() {
      this.showDialog = false
    },
    // 跳转
    gotoHandle(type, row) {
      // 详情
      if (type === 'detail') {
        this.$router.push({
          name: 'MerchantDownloadCenterDetail',
          query: {
            type,
            data: encodeQuery(row)
          }
        })
        return
      }
    },
    // 导出
    handleExport(row) {
      const option = {
        type: 'InquiryList',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {
          id: row.id
        }
      }
      this.exportHandle(option)
    },
    downloadFile(type, row) {
      let filsename = getUrlFilename(row.file_url)
      FileSaver.saveAs(row.file_url, filsename)
    }
  }
}
</script>

<style lang="scss" scoped>
.container-wrapper{
  .align-center{
    line-height: 36px;
  }
  .w-medium{
    width: 140px;
    height: 40px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
}
</style>
