<template>
  <div>
    <!-- <table-template
      :tableInfo="tableInfo"
      :templateList="tableTemplateList"
      :colorList="colorList"
      :actionUrl="actionUrl"
      :headersOpts="headersOpts"
      :confirm="getQrocdeInfo"
    ></table-template> -->
    <address-template
      type="addr"
      title="配送地址二维码模板"
      :defautPxInfo="addrDefautPxInfo"
      :addrInfo="addrInfo"
      :templateList="addrTemplateList"
      :colorList="colorList"
      :actionUrl="actionUrl"
      :headersOpts="headersOpts"
      :confirm="getQrocdeInfo"
    ></address-template>
    <!-- <address-template
      type="fk"
      title="访客餐二维码模板"
      :defautPxInfo="visitorDefautPxInfo"
      :addrInfo="visitorInfo"
      :templateList="visitorTemplateList"
      :colorList="colorList"
      :actionUrl="actionUrl"
      :headersOpts="headersOpts"
      :confirm="getQrocdeInfo"
    ></address-template> -->
  </div>
</template>
<script>
import AddressTemplate from './components/AddressTemplate.vue'
// import TableTemplate from './components/TableTemplate.vue'
import { getToken } from '@/utils'
export default {
  name: 'QrcodeTemplate',
  components: { AddressTemplate },
  props: {},
  data() {
    return {
      addrTemplateList: [],
      addrInfo: {},
      addrDefautPxInfo: {
        titlePx: '1217',
        codePx: '493',
        addressPx: '996'
      },
      tableTemplateList: [],
      tableInfo: {},
      visitorTemplateList: [],
      visitorInfo: {},
      visitorDefautPxInfo: {
        titlePx: 0,
        codePx: '493',
        addressPx: 0
      },
      colorList: ['#000000', '#013300', '#006633', '#07AD3B', '#00C768', '#003367', '#0266CC', '#009AFF', '#494949', '#990001', '#CC3301', '#FC4421', '#FF8900', '#772B5D', '#CC3399', '#CC66CC'],
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getQrocdeInfo()
      this.getSystemTemplateList()
    },
    // 获取系统模板
    async getSystemTemplateList() {
      const res = await this.$apis.apiBackgroundTablecodeRsvQrocodeTemplateListPost({
        organization_id: this.$store.getters.organization
      })
      if (res.code === 0) {
        this.addrTemplateList = [
          ...res.data.addr_templates,
          {
            file_path: 'custom',
            name: '自定义模板'
          }
        ]
        this.tableTemplateList = [
          ...res.data.table_templates,
          {
            file_path: 'custom',
            name: '自定义模板'
          }
        ]
        this.visitorTemplateList = [
          ...res.data.fk_templates
          // {
          //   file_path: 'custom',
          //   name: '自定义模板'
          // }
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取码的配置
    async getQrocdeInfo() {
      const res = await this.$apis.apiBackgroundTablecodeRsvQrocodeTemplateGetCurrentOrgQrcodePost({
        organization_id: this.$store.getters.organization
      })
      if (res.code === 0) {
        this.addrInfo = res.data.results.find(item => item.qrcode_type === 'addr')
        this.tableInfo = res.data.results.find(item => item.qrcode_type === 'table')
        this.visitorInfo = res.data.results.find(item => item.qrcode_type === 'fk')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">

</style>
