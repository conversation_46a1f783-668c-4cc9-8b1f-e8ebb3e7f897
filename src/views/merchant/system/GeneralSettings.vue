<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="general-settings">
      <!-- 组织结构 start -->
      <div class="organization-tree">
        <el-input
          class="tree-search ps-input"
          type="primary"
          :placeholder="$t('placeholder.role_tree_search')"
          clearable
          v-model="treeFilterText">
        </el-input>
        <!-- <div v-if="!treeFilterText" :class="['all-tree tree-flex', !selectId?'is-current':'']" @click="treeHandleNodeClick(rootTreeData, 'admin')">
          <span>
            <i class="tree-search-icon"><img src="@/assets/img/icon all.png" alt="" /></i>全部
          </span>
        </div> -->
        <!-- :load="load"
          lazy -->
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :load="loadTree"
          :lazy="isLazy"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          :current-node-key="selectId"
          :class="{ 'tree-box': selectId} "
          ref="treeRef"
          node-key="id"
          @node-click="treeHandleNodeClick($event, 'tree')"
        >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <!-- <el-tooltip class="item" effect="dark" :content="data.level_name + '-' +data.name" placement="top"> -->
            <span class="ellipsis tree-lable">
              {{ data.level_name + '-' +node.label }}
              <span class="stop-box" v-if="data.status === 'disable'">停</span>
            </span>
          <!-- </el-tooltip> -->
        </div>
        </el-tree>
        <div v-if="treeCount > treeSize" class="ps-pagination" style="text-align:right; margin-top: 20px;">
          <el-pagination
            @current-change="treePaginationChange"
            :current-page="treePage"
            :page-size="treeSize"
            layout="total, prev, pager, next"
            :pager-count="3"
            background
            popper-class="ps-popper-select"
            :total="treeCount">
          </el-pagination>
        </div>
      </div>
      <!-- end -->
      <!--  -->
      <div class="organization-r" v-loading="isLoading">
        <!-- <transition-group name="slide">
        </transition-group> -->
        <div v-if="selectId" class="general-settings-container">
          <el-form
            ref="generalFormDataRef"
            v-loading="isLoading"
            :rules="formDataRuls"
            :model="generalFormData"
            size="small"
          >
            <div class="l-title">
              <span>充值退款</span>
            </div>
            <el-form-item prop="prechargeRefundType">
              <el-select size="small" class="min-w ps-select margin-r" v-model="generalFormData.prechargeRefundType">
                <el-option v-for="item in prechargeRefundTypeList" :key="item.key" :label="item.name" :value="item.key"></el-option>
              </el-select>
              <el-input-number size="small" class="margin-r" v-if="generalFormData.prechargeRefundType === 'custom_day'" v-model="generalFormData.prechargeRefundCustomDay" :min="1"></el-input-number>
              <span>天</span>
            </el-form-item>
            <div class="form-line"></div>
            <div class="l-title">
              <span>消费申诉</span>
            </div>
            <el-form-item prop="appealType">
              <el-checkbox-group class="ps-checkbox" v-model="generalFormData.consumeAppealOnType" @change="changeRppealType">
                <el-checkbox label="online">线上申诉</el-checkbox>
                <el-checkbox label="offline">线下申诉</el-checkbox>
              </el-checkbox-group>
              <!-- <el-checkbox class="ps-checkbox" v-model="generalFormData.consumeAppealOnlineOn">线上申诉</el-checkbox>
              <el-checkbox class="ps-checkbox" v-model="generalFormData.consumeAppealOffineOn">线下申诉</el-checkbox> -->
            </el-form-item>
            <el-form-item prop="prechargeRefundType" v-if="generalFormData.consumeAppealOnType.length">
              <el-select size="small" class="min-w ps-select margin-r" v-model="generalFormData.consumeAppealType">
                <el-option v-for="item in consumeAppealTypeList" :key="item.key" :label="item.name" :value="item.key"></el-option>
              </el-select>
              <el-input-number size="small" class="margin-r" v-if="generalFormData.consumeAppealType === 'custom_day'" v-model="generalFormData.consumeAppealCustomDay" :min="0"></el-input-number>
              <span>天</span>
            </el-form-item>
            <div class="l-title">
              <span>退款设置</span>
              <el-switch class="wallet-margin float-r refund-switch" inactive-text="是否支持退款" v-model="generalFormData.refundOn" active-color="#ff9b45"></el-switch>
            </div>
            <div v-if="generalFormData.refundOn">
              <el-form-item class="block-item" prop="refundPassword" label="退款密码：">
                <el-input size="small" class="min-w" type="password" v-model="generalFormData.refundPassword" placeholder="请输入退款密码"></el-input>
              </el-form-item>
              <el-form-item class="block-item" prop="refundTypeOnline" label="退款时间：">
                <el-select size="small" class="min-w ps-select margin-r" v-model="generalFormData.refundTypeOnline">
                  <el-option v-for="item in refundTypeOnlineList" :key="item.key" :label="item.name" :value="item.key"></el-option>
                </el-select>
                <span v-if="generalFormData.refundTypeOnline === 'custom_time'">
                  <span class="margin-r">订单可退款时间</span>
                  <el-input-number size="small" class="margin-r" v-model="generalFormData.refundcustomTime" :min="1" :max="99"></el-input-number>
                  <span>小时内</span>
                </span>
                <span v-if="generalFormData.refundTypeOnline === 'within'">
                  <span class="margin-r">允许餐段</span>
                  <el-input-number size="small" class="margin-r" v-model="generalFormData.refundMealTime" :min="0"></el-input-number>
                  <span>小时内，可进行退款</span>
                </span>
              </el-form-item>
            </div>
            <div class="l-title">
              <span>餐段设置</span>
              <span style="font-size: 12px; color: #7e7e7e;">(修改当前餐段设置下级组织将会同步修改)</span>
            </div>
            <div class="meal-box">
              <el-form-item v-for="(item, index) in mealList" :key="item.id" :error="item.error">
                <div class="switch-wrapper">
                  <span class="label-check">
                    <el-checkbox class="margin-r ps-checkbox" :disabled="item.disabled" v-model="item.checked" @change="changeTimePicker">{{ item.text }}</el-checkbox>
                  </span>
                  <!-- v-if="index < 3" -->
                  <el-time-picker
                    v-if="index < 4"
                    is-range
                    v-model="item.value"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围"
                    :clearable="false"
                    @change="changeTimePicker($event, item, index)"
                    :disabled="item.disabled"
                  ></el-time-picker>
                  <div v-else class="timePicker" :class="[item.disabled ? 'is-meal-disabled' : '']">
                    <el-time-picker :disabled="item.disabled" class="pickerInput-l" v-model="item.value[0]" @change="changeTimePicker($event, item, index, 0)" :align="'center'" format="HH:mm:ss" value-format="HH:mm:ss" :clearable="false" style="width: 120px" ></el-time-picker>
                    <span style="padding-right: 8px; box-sizing: border-box">至</span>
                    <el-time-picker :disabled="item.disabled" class="pickerInput-r" :prefix-icon="'el'" v-model="item.value[1]" @change="changeTimePicker($event, item, index, 1)" :align="'center'" format="HH:mm:ss" value-format="HH:mm:ss" :clearable="false" style="width: 120px" ></el-time-picker>
                  </div>
                </div>
              </el-form-item>
            </div>
            <div class="l-title">
              <span>离线模式临时记账</span>
            </div>
            <el-form-item class="block-item" prop="withdraw">
              <el-switch class="wallet-margin  refund-switch" inactive-text="余额不足时允许临时记账" v-model="generalFormData.balance_temp_payment" active-color="#ff9b45"></el-switch>
            </el-form-item>
            <el-form-item class="block-item" prop="withdraw">
              <el-switch class="wallet-margin  refund-switch" inactive-text="存在脱机扣款失败订单时允许临时记账" v-model="generalFormData.offline_order_temp_payment" active-color="#ff9b45"></el-switch>
            </el-form-item>
            <div class="l-title">
              <span>点餐设置</span>
            </div>
            <el-form-item label="可配置预约点餐的层级" v-if="selectTree.level===0">
              <el-select size="small" class="ps-select margin-r" v-model="generalFormData.reservationRole">
                <el-option v-for="item in levelTagList" :key="item.key" :label="item.name" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="扫码是否支持修改地址">
              <el-radio-group class="ps-radio" v-model="generalFormData.canModifyAddr">
                <el-radio :label="1">支持</el-radio>
                <el-radio :label="0">不支持</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否需要用户选择取餐柜">
              <el-radio-group class="ps-radio" v-model="generalFormData.autoSaveInCupboard">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
              <div class="auto-save-in-cupboard-text">下单<span class="auto-save-in-cupboard-label">需选择</span> 取餐柜 <span class="auto-save-in-cupboard-label">仅支持</span> 手动存餐、下单即刻分柜的规则；</div>
              <div class="auto-save-in-cupboard-text">下单 <span class="auto-save-in-cupboard-label">不需选择</span> 取餐柜则 <span class="auto-save-in-cupboard-label">不支持</span> 手动存餐、下单即刻分柜的规则；</div>
              <div class="auto-save-in-cupboard-text">保存后请及时修改取餐柜设置</div>
            </el-form-item>
            <el-form-item label="餐柜已满是否支持继续下单">
              <el-radio-group class="ps-radio" v-model="generalFormData.exCupboardEnable">
                <el-radio :label="1">支持</el-radio>
                <el-radio :label="0">不支持</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否支持免注册扫码点餐" v-if="selectTree.level===0">
              <el-radio-group class="ps-radio" v-model="generalFormData.canVisitorMeal">
                <el-radio :label="1">支持</el-radio>
                <el-radio :label="0">不支持</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="可配置报餐的层级" v-if="selectTree.level===0">
              <el-select size="small" class="ps-select margin-r" v-model="generalFormData.mealReportRole">
                <el-option v-for="item in levelTagList" :key="item.key" :label="item.name" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="报餐执行消费规则" v-if="selectTree.level===0">
              <el-radio-group class="ps-radio" v-model="generalFormData.reportMealExecuteRule">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <div>
            <el-switch
            class="wallet-margin  refund-switch ps-select margin-r"
              v-if="selectTree.level===0"
              v-model="generalFormData.order_wechat_notify"
              active-color="#ff9b45"
              inactive-text="公众号推送:">
            </el-switch>
            <el-switch
            class="wallet-margin  refund-switch ps-select margin-l"
              v-if="selectTree.level===0"
              v-model="generalFormData.order_mobile_notify"
              active-color="#ff9b45"
              inactive-text="移动端弹窗:">
            </el-switch>
            </div>
            <div class="l-title">
              <span>提现设置</span>
            </div>
            <el-form-item class="block-item" prop="withdraw">
              <el-switch class="wallet-margin  refund-switch" inactive-text="是否支持提现" v-model="generalFormData.withdraw" active-color="#ff9b45" @change="handlerWithdrawChange"></el-switch>
              <el-switch class="wallet-margin  refund-switch m-l-20" inactive-text="是否需要审批" v-model="generalFormData.withdraw_approval_on" active-color="#ff9b45" v-if="generalFormData.withdraw" ></el-switch>
            </el-form-item>
            <div class="l-title" v-if="selectTree.level===0">
              <span>人脸设置</span>
            </div>
            <el-form-item class="block-item" prop="faceCollectOffineOn" v-if="selectTree.level===0">
              <el-switch class="wallet-margin  refund-switch" inactive-text="人脸采集开关" v-model="generalFormData.faceCollectOffineOn" active-color="#ff9b45"></el-switch>
            </el-form-item>
            <div class="l-title">
              <span>H5充值退款设置</span>
            </div>
            <el-form-item class="block-item" prop="rechargeRefund">
              <el-switch class="wallet-margin  refund-switch" inactive-text="是否支持H5充值退款" v-model="generalFormData.withdraw_refund_on" active-color="#ff9b45"></el-switch>
            </el-form-item>
            <div class="l-title">
              <span>发票设置</span>
            </div>
            <el-form-item prop="invoiceApplyOffineOn" label="">
              <el-radio-group class="ps-radio" v-model="generalFormData.invoiceApplyOffineOn">
                <el-radio :label="true">开发票</el-radio>
                <el-radio :label="false">不开发票</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 20240815 郑鸿宇要求隐藏-->
            <div class="l-title" v-if="false">
              <span>库存设置</span>
            </div>
            <el-form-item class="block-item" v-if="false">
              <el-radio-group class="ps-radio" v-model="generalFormData.stockType">
                <el-radio label="standard">标准库存</el-radio>
                <el-radio label="special">特殊库存</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="指定分组" v-if="false && generalFormData.stockType === 'special'">
              <user-group-select multiple v-model="generalFormData.stockGroups"></user-group-select>
            </el-form-item>
            <!-- <div class="l-title">
              <span>其它设置</span>
            </div>
            <el-form-item prop="appealType" label="线上预约截止时间">
              <el-checkbox-group class="ps-checkbox" v-model="generalFormData.appointmentType" @change="changeAppointmentType">
                <el-checkbox :label="0">不限制</el-checkbox>
                <el-checkbox :label="1">限制时间</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item v-if="generalFormData.appointmentType.includes(1)" prop="appealType" label="线上预约截止时间">
              <el-select size="small" class="ps-select margin-r" style="width: 120px;" v-model="generalFormData.limitHour">
                <el-option v-for="item in 24" :key="item" :label="item-1" :value="item-1"></el-option>
              </el-select>
              <span>前可以预订 D+1 日期订单（D为当天，D+1为第二天），超过截止时间后只允许预订 D+2 订单</span>
            </el-form-item> -->
            <div v-if="selectId" class="footer m-t-20 t-a-c">
              <el-button class="ps-origin-btn w-130" type="primary"  @click="saveGeneralSetting" v-permission="['background_organization.organization.modify_common_settings']">保存</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
// import { PRECHARGE_REFUND_TYPE } from '@/utils/constants'
import { hasClass } from '@/utils/dom'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import UserGroupSelect from '@/components/UserGroupSelect'

export default {
  name: 'GeneralSettings',
  components: {
    UserGroupSelect
  },
  // mixins: [activatedLoadData],
  data() {
    // let nowDate = parseTime(new Date(), '{y}/{m}/{d}')
    return {
      treeLoading: false,
      treeList: [],
      isLazy: true,
      treeFilterText: '',
      treeProps: {
        children: 'children_list',
        label: 'name',
        isLeaf: (data, node) => {
          return !data.has_children
        }
      },
      treeSize: 10, // 每页数量
      treeCount: 0, // 总条数
      treePage: 1, // 第几页
      selectTree: {}, // 选中的tree数据
      selectId: '', // 点击选中的tree id
      treeSelectLevel: '',
      type: '', //
      organizationInfo: null, // 详情
      generalFormData: {
        prechargeRefundType: '', // 退款状态
        prechargeRefundCustomDay: '', // 自定义退款时间
        consumeAppealOnlineOn: false, // 线上申诉开关
        consumeAppealOffineOn: false, // 线下申诉开关
        consumeAppealOnType: [], // 线上和线下类型
        consumeAppealType: '', // 消费申诉类型
        consumeAppealCustomDay: '', // 自定义时间内申诉
        refundOn: false, // 支持退款
        refundPassword: '', // 退款密码
        refundTypeOnline: '', // 退款类型
        refundcustomTime: '', // 自定义退款时间
        refundMealTime: '', // 餐段时间内可退款
        reservationRole: '', // 配置预约点餐的层级
        canModifyAddr: 0, // 扫码是否支持修改地址
        canVisitorMeal: 0, // 是否支持免注册扫码点餐
        mealReportRole: '', // 配置报餐的层级
        reportMealExecuteRule: false, // 报餐执行消费规则
        // appointmentType: [0],
        // limitHour: 0,
        autoSaveInCupboard: 0, // 是否需用户选择取餐柜
        exCupboardEnable: 0, // 餐柜已满是否支持继续下单
        withdraw: false, // 提现
        faceCollectOffineOn: false, // 人脸
        stockType: '', // 库存类型
        stockGroups: [], // 库存分组
        order_sms_notify: false,
        order_wechat_notify: false,
        order_mobile_notify: false,
        invoiceApplyOffineOn: false, // 开票设置
        balance_temp_payment: false,
        offline_order_temp_payment: false,
        withdraw_approval_on: false, // 是否需要审批
        withdraw_refund_on: false // 是否支持H5充值退款
      },
      formDataRuls: {
        name: [{ required: true, message: '组织名称不能为空', trigger: "blur" }],
        level_name: [{ required: true, message: '层级名称不能为空', trigger: "blur" }],
        refundPassword: [{ required: true, message: '退款密码不能为空', trigger: "blur" }]
      },
      prechargeRefundTypeList: [
        { name: '不允许退款', key: 'not_allow' },
        { name: '只允许充值当天退款', key: 'today' },
        { name: '充值后自定义时间内退款', key: 'custom_day' }
      ],
      consumeAppealTypeList: [
        { name: '不允许申诉', key: 'not_allow' },
        { name: '只允许消费当天内申诉', key: 'today' },
        { name: '自定义时间内申诉', key: 'custom_day' }
      ],
      refundTypeOnlineList: [
        { name: '任意时间', key: 'anytime' },
        { name: '自定义时间', key: 'custom_time' },
        { name: '按餐段限制', key: 'within' }
      ],
      isLoading: false,
      mealList: [
        {
          value: [`06:00:01`, `10:11:00`],
          text: "早餐",
          checked: false,
          id: 'breakfast',
          error: ''
        },
        {
          value: [`10:00:01`, `14:00:00`],
          text: "午餐",
          checked: false,
          id: 'lunch',
          error: ''
        },
        {
          value: [`14:00:01`, `17:00:00`],
          text: "下午茶",
          checked: false,
          id: 'afternoon',
          error: ''
        },
        {
          value: [`17:00:01`, `21:00:00`],
          text: "晚餐",
          checked: false,
          id: 'dinner',
          error: ''
        },
        {
          value: [`21:00:01`, `23:59:59`],
          text: "宵夜",
          checked: false,
          id: 'supper',
          error: ''
        },
        {
          value: [`00:00:00`, `6:00:00`],
          text: "凌晨餐",
          checked: false,
          id: 'morning',
          error: ''
        }
      ],
      timeOverlay: false, // 餐段是否重叠了
      // 层级列表
      levelTagList: []
    }
  },
  watch: {
    treeFilterText(val) {
      this.filterHandle()
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    async initLoad() {
      await this.getOrganizationList()
      if (this.selectId) {
        await this.getLevelTag()
        this.getCommonSettings()
      } else {
        this.$message.error('暂无更多数据')
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      // this.$refs.searchRef.resetForm()
      this.treeFilterText = ''
      this.selectId = ''
      this.treeList = []
      this.treePage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    // 懒加载组织结构
    async loadTree(tree, resolve) {
      // 0级直接退出执行
      if (tree.level === 0) {
        return;
      }
      let params = {
        status__in: ['enable', 'disable'],
        page: 1,
        page_size: 99999
      }
      if (tree.data && tree.data.id) {
        params.parent__in = tree.data.id
      } else {
        // params.parent__is_null = '1'
        this.treeLoading = true
      }
      // 强制睡眠
      // await this.$sleep(1000);
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationListPost(params));
      this.treeLoading = false
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    // 名称搜索
    filterHandle: debounce(function() {
      this.getOrganizationList(this.treeFilterText)
    }, 300),
    // 获取组织，用于顶级的获取or搜索
    async getOrganizationList(name, parentId, callback) {
      let params = {
        status__in: ['enable', 'disable'],
        page: this.treePage,
        page_size: this.treeSize
      }
      if (parentId) {
        params.parent__in = parentId
      } else {
        // params.parent__is_null = '1'
      }
      if (name) {
        params.name__contains = name
      }
      this.treeLoading = true
      // 强制睡眠
      // await this.$sleep(1000);
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationListPost(params));
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let results = res.data.results
        if (name) {
          results = res.data.results.map(v => {
            if (name) {
              v.has_children = false
            }
            return v
          })
        } else {
          if (results.length && results.length < 6 && results[0].has_children) {
            this.$nextTick(() => {
              // 只能通过class去触发click事件了
              const expandEls = document.querySelectorAll('.el-tree-node__expand-icon')
              if (!hasClass(expandEls[0], 'expanded')) {
                expandEls[0] && expandEls[0].click()
              }
            })
          }
        }
        if (!this.treeList.length) {
          this.type = 'child'
          if (results.length) {
            this.selectId = results[0].id
            this.selectTree = results[0]
          }
        }
        if (!parentId) {
          this.treeList = results
          this.treeCount = res.data.count
        } else { // 有parentid和callback才能进行数据更新
          if (callback) {
            callback(parentId, results)
          }
        }

        // 更新数据时需要手动设置当前高亮选项
        if (this.selectId) {
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.selectId)
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 组织树的分页
    treePaginationChange(e) {
      this.treePage = e
      this.getOrganizationList(this.treeFilterText)
    },
    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取常规设置
    async getCommonSettings() {
      // await this.$sleep(1000);
      this.isLoading = true
      let params = {
        id: this.selectId
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationGetCommonSettingsPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.initSettingData(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取预约点餐的层级列表
    async getLevelTag() {
      const [err, res] = await to(this.$apis.apiBackgroundBaseMenuGetLevelTagPost());
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        for (const key in res.data) {
          this.levelTagList.push({
            key: Number(key),
            name: res.data[key]
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化显示的数据
    initSettingData(data) {
      this.generalFormData.prechargeRefundType = data.precharge_refund_type
      this.generalFormData.prechargeRefundCustomDay = data.precharge_refund_custom_day
      this.generalFormData.consumeAppealOnlineOn = data.consume_appeal_online_on
      this.generalFormData.consumeAppealOffineOn = data.consume_appeal_offine_on
      if (data.consume_appeal_online_on) {
        this.generalFormData.consumeAppealOnType = ['online']
      }
      if (data.consume_appeal_offine_on) {
        this.generalFormData.consumeAppealOnType = ['offline']
      }
      this.generalFormData.consumeAppealType = data.consume_appeal_type
      this.generalFormData.consumeAppealCustomDay = data.consume_appeal_custom_day
      this.generalFormData.refundOn = data.refund_on
      this.generalFormData.refundPassword = data.refund_password
      this.generalFormData.refundTypeOnline = data.refund_type_online
      this.generalFormData.refundcustomTime = data.refund_custom_time
      this.generalFormData.refundMealTime = data.refund_meal_time
      this.generalFormData.reservationRole = data.reservation_role // 配置预约点餐的层级
      this.generalFormData.canModifyAddr = data.can_modify_addr // 扫码是否支持修改地址
      this.generalFormData.canVisitorMeal = data.is_rsv_exempt_enroll // 是否支持免注册扫码点餐
      this.generalFormData.mealReportRole = data.report_meal_role // 配置报餐的层级
      this.generalFormData.reportMealExecuteRule = data.report_meal_execute_rule // 报餐执行消费规则
      this.generalFormData.autoSaveInCupboard = data.auto_save_in_cupboard // 扫码是否支持修改地址
      this.generalFormData.exCupboardEnable = data.ex_cupboard_enable // 扫码是否支持修改地址
      this.generalFormData.withdraw = data.withdraw_on
      this.generalFormData.faceCollectOffineOn = data.face_collect_offine_on
      this.generalFormData.stockType = data.inventory_calcul_type
      this.generalFormData.stockGroups = data.used_groups
      this.generalFormData.withdraw_approval_on = data.withdraw_approval_on
      this.generalFormData.withdraw_refund_on = data.withdraw_refund_on

      this.generalFormData.order_mobile_notify = data.order_mobile_notify
      this.generalFormData.order_sms_notify = data.order_sms_notify
      this.generalFormData.order_wechat_notify = data.order_wechat_notify
      this.generalFormData.invoiceApplyOffineOn = data.invoice_apply_offine_on // 配置报餐的层级
      this.generalFormData.stockType = data.inventory_calcul_type
      this.generalFormData.stockGroups = data.used_groups
      this.generalFormData.balance_temp_payment = data.balance_temp_payment
      this.generalFormData.offline_order_temp_payment = data.offline_order_temp_payment
      // if (data.limit_hour < 25) {
      //   this.generalFormData.limitHour = data.limit_hour
      //   this.generalFormData.appointmentType = [1]
      // } else {
      //   this.generalFormData.appointmentType = [0]
      // }
      // 初始化时间
      let time = data.meal_time_settings
      let currentTime = data.meal_time_settings ? deepClone(data.meal_time_settings) : null
      // let now = parseTime(new Date(), '{y}/{m}/{d}');
      let inherit = false // 是否继承父级
      if (!time) { // 如果自己没有开启则继承父级的
        inherit = true
        time = data.parent_meal_time_settings
      }
      if (time) {
        this.mealList.map((v, i) => {
          if (inherit) {
            // v.value = [null, null]
            v.checked = false
          }
          // } else {
          let start = `${time[v.id + '_start']}`
          let end = `${time[v.id + '_end']}`
          if (time[v.id + '_start'] && time[v.id + '_end']) {
            v.value = [start, end]
          }
          if (currentTime) {
            v.checked = !!currentTime['enable_' + v.id]
          } else {
            v.checked = false
          }
          // }
          // this.$set(this.mealList, i, v)
          // return v
          if (data.parent_meal_time_settings) {
            v.disabled = !data.parent_meal_time_settings['enable_' + v.id]
          } else {
            v.disabled = false
          }
        })
      } else { // 一级为0 可以设置
        if ((this.treeSelectLevel)) {
          this.mealList.map((v, i) => {
            v.disabled = true
          })
        } else {
          this.mealList.map((v, i) => {
            v.disabled = false
          })
        }
      }
      this.changeTimePicker()
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // 点击tree node click
    async treeHandleNodeClick(e, type, operate) {
      if (e.id === this.selectTree.id) return;
      this.selectTree = e
      this.selectId = e.id
      this.treeSelectLevel = e.level
      this.getCommonSettings()
    },
    // 消费申诉change
    changeRppealType(e) {
      if (e.length > 1) {
        this.generalFormData.consumeAppealOnType.splice(0, 1)
      }
    },
    // 线上预约时间限制change
    changeAppointmentType(e) {
      if (e.length > 1) {
        this.generalFormData.appointmentType.splice(0, 1)
      }
    },
    // 时间change事件
    changeTimePicker(val, item, index, k) {
      // 统一做个判断，下一餐段时间必须比上餐段时间大，凌晨餐除外（特殊，可能跨天）
      this.timeOverlay = false
      // 餐段提示信息初始化
      this.mealList.forEach(data => {
        data.error = ''
      })
      // 选中的餐段
      let checkMealList = this.mealList.filter(item => item.checked)
      // debugger
      // 两个及已上才判断
      if (checkMealList.length > 1) {
        let timeList = [] // 时间的集合
        checkMealList.forEach((data, i) => {
          let req = /:/g
          data.value.map(v => {
            // 添加时间
            timeList.push(v ? +v.replace(req, '') : 0)
          })
          let time1 = data.value.map(v => {
            return v ? +v.replace(req, '') : 0
          })
          // console.log(data)
          checkMealList.forEach((mealValue, k) => {
            if (i < k) {
              let time2 = mealValue.value.map(v => {
                return v ? +v.replace(req, '') : 0
              })
              // let isCompare = this.compareWidth(data, mealValue, timeList)
              let isCompare = this.compareWidthTime(time1, time2)
              // console.log(3332, isCompare)
              if (isCompare) {
                // if (!checkMealList[i].error) checkMealList[i].error = '餐段时间重叠，请检查！'
                if (!checkMealList[k].error) checkMealList[k].error = '餐段时间重叠，请检查！'
                this.timeOverlay = true
              }
            }
          })
        })
      }
    },
    
    // // 比较两个时间段，大于(重叠)返回1，小于（不闭合）返回-1，等于返回0，2当前餐段时间不正确需要比上一餐段时间大
    // // 一般情况date1要比date2小，如果是跨天的情况下就不一定了
    // compareWidth(date1, date2, timeList) {
    //   let status = -1
    //   let req = /:/g
    //   // let time1 = date1.value.map(v => {
    //   //   return v ? +v.replace(req, '') : 0
    //   // })
    //   let time2 = date2.value.map(v => {
    //     return v ? +v.replace(req, '') : 0
    //   })
    //   // 非跨天的情况
    //   // date2的开始时间要大于date1的开始时间，并且date2的开始时间也要大于date1结束时间，date2的结束时间必须要比date1的任何一个时间都大

    //   // 跨天的情况有两种
    //   // 1、date2的开始时间就是跨天的时间
    //   // 2、date2的结束时间才是跨天

    //   // 当前遍历的餐段最大的时间
    //   let maxTime = Math.max.apply(Math, timeList)
    //   let minTime = Math.min.apply(Math, timeList)
    //   // debugger
    //   if (time2[1] > time2[0]) {
    //     if (maxTime <= time2[1]) { // 不跨天
    //       if (maxTime >= time2[0]) {
    //         status = 1
    //         return status
    //       }
    //     } else if ((maxTime >= time2[1])) { // 凌晨餐跨天
    //       if (!(time2[1] <= minTime)) {
    //         status = 1
    //         console.log(status, time2, maxTime, minTime)
    //         return status
    //       }
    //     }
    //   } else if (time2[1] < time2[0]) {
    //     if (maxTime >= time2[0]) {
    //       status = 1
    //       // console.log(status, date1, date2, timeList, time2, maxTime, minTime)
    //       return status
    //     } else {
    //       if (time2[1] >= minTime) {
    //         status = 1
    //         return status
    //       }
    //     }
    //   } else { // 开始和结束时间相等？
    //     if (!((time2[0] > maxTime) || (time2[0] < minTime))) {
    //       status = 1
    //       return status
    //     }
    //   }
    //   return status
    // },
    // 时间time1, time2，isAcrossDay是否跨天，ranges时间范围
    compareWidthTime(timeA, timeB) {
      // true重叠，false不重叠
      let status = false
      if (timeA[0] <= timeA[1] && timeB[0] <= timeB[1]) { // 餐段设置不跨天
        status = this.checkTimeOverlap(timeA, timeB)
      } else { // 餐段设置跨天
        if (timeA[0] > timeA[1]) { // timeA是跨天的时间
          console.log(timeA, timeB)
          if (timeB[0] > timeB[1]) { // timeB是跨天的时间，当2个时间段都是跨天必定是重叠的
            status = true
          } else { // 拆分求是否重叠吧
            if (this.checkTimeOverlap([timeA[0], '235959'], timeB)) {
              status = true
            }
            if (this.checkTimeOverlap(['000000', timeA[1]], timeB)) {
              status = true
            }
          }
        } else { // timeA是不跨天的时间
          if (timeB[0] > timeB[1]) { // timeB是跨天的时间
            if (this.checkTimeOverlap([timeB[0], '235959'], timeA)) {
              status = true
            }
            if (this.checkTimeOverlap(['000000', timeB[1]], timeA)) {
              status = true
            }
          } else {
            status = this.checkTimeOverlap(timeA, timeB)
          }
        }
      }
      return status
    },
    // 判断时间范围是否重叠, true重叠，false不重叠
    checkTimeOverlap (timeA, timeB) {
      const max = [timeA[0], timeB[0]];
      const min = [timeA[1], timeB[1]];
      return (Math.max.apply(null, max) <= Math.min.apply(null, min))
    },
    async saveGeneralSetting() {
      if (this.timeOverlay || this.isLoading) {
        return
      }
      let params = {
        id: this.selectId,
        precharge_refund_type: this.generalFormData.prechargeRefundType,
        precharge_refund_custom_day: this.generalFormData.prechargeRefundCustomDay,
        consume_appeal_type: this.generalFormData.consumeAppealType,
        consume_appeal_custom_day: this.generalFormData.consumeAppealCustomDay,
        refund_on: this.generalFormData.refundOn,
        refund_password: this.generalFormData.refundPassword,
        refund_type_online: this.generalFormData.refundTypeOnline,
        refund_custom_time: this.generalFormData.refundcustomTime,
        refund_meal_time: this.generalFormData.refundMealTime,
        reservation_role: this.generalFormData.reservationRole, // 配置预约点餐的层级
        can_modify_addr: this.generalFormData.canModifyAddr, // 扫码是否支持修改地址
        auto_save_in_cupboard: this.generalFormData.autoSaveInCupboard, // 扫码是否支持修改地址
        ex_cupboard_enable: this.generalFormData.exCupboardEnable, // 扫码是否支持修改地址
        is_rsv_exempt_enroll: this.generalFormData.canVisitorMeal, // 是否支持免注册扫码点餐
        report_meal_role: this.generalFormData.mealReportRole, // 配置报餐的层级
        report_meal_execute_rule: this.generalFormData.reportMealExecuteRule, // 报餐执行消费规则
        withdraw_on: this.generalFormData.withdraw,
        face_collect_offine_on: this.generalFormData.faceCollectOffineOn,
        // inventory_calcul_type: this.generalFormData.stockType, // 库存类型
        // used_groups: this.generalFormData.stockGroups, // 库存分组
        withdraw_approval_on: this.generalFormData.withdraw_approval_on, // 是否需要审批
        withdraw_refund_on: this.generalFormData.withdraw_refund_on, // 是否支持H5充值退款

        order_mobile_notify: this.generalFormData.order_mobile_notify,
        order_sms_notify: this.generalFormData.order_sms_notify,
        order_wechat_notify: this.generalFormData.order_wechat_notify,
        invoice_apply_offine_on: this.generalFormData.invoiceApplyOffineOn,
        // 到时这里新增临时记账的传参即可
        balance_temp_payment: this.generalFormData.balance_temp_payment,
        offline_order_temp_payment: this.generalFormData.offline_order_temp_payment
      }
      if (this.generalFormData.consumeAppealOnType.includes('online')) {
        params.consume_appeal_online_on = true
        params.consume_appeal_offine_on = false
      }
      if (this.generalFormData.consumeAppealOnType.includes('offline')) {
        params.consume_appeal_online_on = false
        params.consume_appeal_offine_on = true
      }
      if (!this.generalFormData.consumeAppealOnType.length) {
        params.consume_appeal_online_on = false
        params.consume_appeal_offine_on = false
      }
      // 其它设置
      // if (!this.generalFormData.appointmentType[0]) {
      //   params.limit_hour = 25
      // } else {
      //   params.limit_hour = this.generalFormData.limitHour
      // }
      params.meal_time_settings = {}
      this.mealList.forEach(item => {
        // if (item.checked) {
        params.meal_time_settings[item.id + '_start'] = item.value[0] // parseTime(item.value[0], '{h}:{i}:{s}')
        params.meal_time_settings[item.id + '_end'] = item.value[1] // parseTime(item.value[1], '{h}:{i}:{s}')
        params.meal_time_settings['enable_' + item.id] = item.checked
        // }
      })
      // this.isLoading = false
      // return
      // meal_time_settings
      // if (this.treeSelectLevel < 5) {
      this.$confirm(`确定要更改吗?`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(2222)
            const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationModifyCommonSettingsPost(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getCommonSettings()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
      // } else {
      //   this.isLoading = true
      //   const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationModifyCommonSettingsPost(params));
      //   this.isLoading = false
      //   if (err) {
      //     this.$message.error(err.message)
      //     return
      //   }
      //   if (res.code === 0) {
      //     this.$message.success(res.msg)
      //     this.getCommonSettings()
      //   } else {
      //     this.$message.error(res.msg)
      //   }
      // }
    },
    // 监听是否提现改变
    handlerWithdrawChange(value) {
      // 如果是否支持提现关闭，需要重置是否需要审批
      if (value) {
        this.generalFormData.withdraw_approval_on = false
      }
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.general-settings {
  display: flex;
  // height: 100%;
  .refund-switch{
    .el-switch__label.is-active{
      color: #303133;
    }
  }
  .block-item{
    .el-form-item__label{
      float: none;
    }
  }
  .organization-r {
    position: relative;
    min-height: calc(100vh - 128px - 76px - 5px);
    // height: calc(100vh - 128px - 76px - 5px);
    // overflow-y: scroll;
    flex: 1;
    min-width: 0;
    background-color: #f8f9fa;
    box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3),
      inset 2px 2px 0px 0px#ffffff;
    border-radius: 0px 12px 12px 0;
    padding: 20px;
    .organization-tab-group{
      .organization-tab{
        display: inline-block;
        padding: 5px 10px;
        margin: 5px 10px 5px 0;
        font-size: 13px;
        letter-spacing: 1px;
        color: #7b7c82;
        border: solid 1px #dae1ea;
        border-radius: 15px;
        cursor: pointer;
        &.is-checked{
          color: #fff;
          border: solid 1px #dae1ea;
          background-color: #fd953c;
        }
        .is-checked+.tab-label{
          color: #fff;
        }
        &.el-radio:last-child {
          margin-right: 0;
        }
        &.is-disable {
          cursor: not-allowed;
          opacity: .5;
        }
      }
    }
    .item-box{
      // display: flex;
      padding: 10px 0;
      .item-b-l{
        // display: flex;
        // justify-content: center;
        // align-items: center;
        float: left;
        width: 56px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        vertical-align: middle;
        background-color: #ff9b45;
        border-radius: 8px;
        font-size: 30px;
        letter-spacing: 2px;
        color: #ffffff;
      }
      .item-b-r{
        margin-left: 76px;
      }
      .item-text-box{
        display: flex;
        padding: 5px 0;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #23282d;
        .item-label{
          opacity: 0.7;
        }
        .item-text{
          flex: 1;
        }
      }
    }
  }
  .general-settings-container{
    .min-w{
      width: 350px;
    }
    .margin-r{
      margin-right: 15px;
    }
    .margin-l{
      margin-left: 15px;
    }
    .el-form{
      max-width: 720px;
    }
    .meal-box{
      .el-form-item{
        display: inline-block;
        .el-date-editor--timerange{
          width: 265px;
          .el-range-separator{
            width: 20px !important;
          }
        }
        &:nth-child(odd) {
          margin-right: 15px;
        }
        .timePicker {
          display: inline-block;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          width: 265px;
          background-color: #fff;
          .pickerInput-r {
            padding-right: 11px;
            box-sizing: border-box;
            .el-input__inner{
              padding-left: 10px;
            }
          }
          .pickerInput-l {
            padding-left: 14px;
            box-sizing: border-box;
            .el-input__inner{
              padding-right: 20px;
            }
          }
          .el-input__inner {
            border: none;
            text-align: center;
          }
        }
        &.is-error {
          .timePicker{
            border-color: #F56C6C;
          }
        }
        .is-meal-disabled{
          background-color: #f5f7fa;
          color: #C0C4CC;
        }
      }
      .label-check{
        display: inline-block;
        min-width: 80px;
      }
    }
  }
  .organization-tree{
    width: auto;
    max-width: 320px;
  }
  .custom-tree-node {
    width: 100%;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #23282d;
    .el-icon-more{
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .tree-icon{
      width: 15px;
      text-align: center;
      &.el-icon-edit{
        color: #ff9b45;
      }
    }
    .stop-box{
      display: inline-block;
      text-align: center;
      color: red;
      border: 1px solid #ff5450;
      border-radius: 50%;
      font-size: 12px;
      padding: 2px 3px;
      transform: scale(0.7);
    }
  }
  .tree-flex{
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #23282d;
    .el-icon-more{
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .tree-icon{
      width: 15px;
      text-align: center;
    }
  }
}
.custon-tree-popper{
  min-width: 50px;
  padding: 0;
  .popover-btn-box{
    display: flex;
    flex-direction: column;
    .el-button{
      display: block;
      margin: 0 !important;
      padding: 8px 15px;
      color: #23282d;
      &.popper-del{
        color: #ff5450;
      }
      &:hover{
        color: #fd953c;
        background-color: #edeff5;
      }
      &.is-disabled{
        opacity: .5;
        &:hover{
          color: #23282d;
          background-color: unset;
        }
      }
    }
  }
}
.auto-save-in-cupboard-text{
  color:#87939a;
  font-size: 12px;
  .auto-save-in-cupboard-label{
    color:red !important;
  }
}
</style>
