<template>
  <div class="system-notice-detail" v-loading="isLoading">
    <h1 class="margin-b-10 title">{{ noticeForm.title }}</h1>
    <div class="content margin-b-10" v-html="noticeForm.content"></div>
    <div class="margin-b-10" v-if="noticeForm.fileLists.length">
      <div v-for="item in noticeForm.fileLists" :key="item.uid">
        <span>附件：{{ item.name }}</span>
        <el-button type="text" @click="downloadHandle(item)" style="margin-left: 10px;">下载</el-button>
      </div>
    </div>
    <div class="margin-b-10" v-if="noticeForm.status === 1">
      <!-- <el-button type="primary" size="small" @click="editHandle">编辑</el-button> -->
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { to, unescapeHTML } from '@/utils'
import FileSaver from 'file-saver'

export default {
  name: '',
  components: {},
  props: {},
  data() {
    return {
      isLoading: false,
      noticeForm: {
        msgNo: '',
        title: '',
        companyIds: [],
        content: '',
        isAll: false,
        fileLists: [],
        status: 2,
        postTime: ''
      },
      modifyData: {},
      fileName: ''
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.query.msg_no) {
      this.noticeForm.msgNo = this.$route.query.msg_no
    }

    if (this.$route.query.type === 'list') {
      this.getMessagesDetails()
    } else {
      this.getNoticeContentMsg()
    }
  },
  mounted() {},
  methods: {
    ...mapActions({
      _getNoticeContent: 'getNoticeContent'
    }),
    async getNoticeContentMsg() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMessagesMessagesDetailsPost({
          msg_no: this.noticeForm.msgNo
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.modifyData = res.data
        this.initDefaultInfo()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取公告详情，并已读
    async getMessagesDetails(data) {
      const [err, res] = await to(
        this.$apis.apiBackgroundMessagesMessagesGetMsgReceivePost({
          msg_no: this.noticeForm.msgNo
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.modifyData = res.data
        this.getMsgNum()
        this.initDefaultInfo()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getMsgNum() {
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgNumPost({}))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let unreadCount = 0
        if (res.data && res.data.unread_count) {
          unreadCount = res.data.unread_count > 99 ? '99+' : res.data.unread_count
        }
        this.$root.eventHub.$emit('updateNoticeCount', unreadCount)
      } else {
        this.$message.error(res.msg)
      }
    },
    initDefaultInfo() {
      this.noticeForm.status = this.modifyData.status
      let content = unescapeHTML(this.modifyData.content)
      // let str = ''
      // if (content.indexOf('\n') !== -1) {
      //   let contentArray = content.split('\n')
      //   contentArray.forEach((item, index) => {
      //     if (index === 1) {
      //       str += '<div>' + item + '</div>'
      //     } else if ((index === contentArray.length - 2 || index === contentArray.length - 3) && index !== 1) {
      //       str += '<div style="text-align: right; padding-right: 20px">' + item + '</div>'
      //     } else {
      //       str += '<div style="padding: 0px 20px">' + item + '</div>'
      //     }
      //   })
      // } else {
      //   str = content
      // }
      this.noticeForm.content = content
      this.noticeForm.title = this.modifyData.title
      this.noticeForm.isAll = this.modifyData.receiver_type === 'all_org'
      if (this.modifyData.post_time) {
        this.noticeForm.postTime = this.modifyData.post_time
      }
      this.noticeForm.organizationNames = ''
      if (this.modifyData.organization_ids && this.modifyData.organization_ids.length) {
        this.modifyData.organization_ids.forEach(v => {
          if (this.noticeForm.organizationNames) {
            this.noticeForm.organizationNames += `，${v.name}`
          } else {
            this.noticeForm.organizationNames = v.name
          }
        })
      }
      if (this.modifyData.resource && this.modifyData.resource.length) {
        let files = this.modifyData.resource
        let time = new Date().getTime()
        this.noticeForm.fileLists = files.map(v => {
          time += 1
          let fileName = v.substring(v.lastIndexOf('/') + 1)
          // 后端加了tk校验文件，要把它截掉
          let isSplit = fileName.indexOf('?')
          if (isSplit > -1) {
            fileName = fileName.substring(0, isSplit)
          }
          return {
            name: fileName,
            status: 'success',
            uid: time,
            url: v
          }
        })
      }
    },
    async downloadHandle(file) {
      const response = await fetch(file.url)
      const blob = await response.blob()
      FileSaver.saveAs(blob, file.name)
    },
    editHandle() {
      this.$router.push({
        name: 'SuperAddSystemNotice',
        query: {
          type: 'modify',
          msg_no: this.noticeForm.msgNo
        }
      })
    }
  },
  beforeDestroy() {}
}
</script>

<style lang="scss">
.system-notice-detail {
  word-break: break-all;
  .title {
    font-size: 18px;
    font-weight: 600;
  }
  // border-radius: 10px;
  // border: 1px solid #d3d3d3;
  .content {
    max-width: 980px;
    padding: 10px;
    // border: 1px solid #d3d3d3;
    word-break: break-all;
    background-color: rgba(242, 242, 242, 1);
    img {
      max-width: 100%;
      height: auto;
    }
    p {
      line-height: 1.5;
    }
  }
  .margin-b-10 {
    margin-bottom: 10px;
  }
}
</style>
