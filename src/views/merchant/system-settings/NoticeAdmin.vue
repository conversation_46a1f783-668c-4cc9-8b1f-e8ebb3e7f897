<template>
  <div id="notice-admin-container" class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="notice-admin-list">
      <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false" @reset="resetHandle" ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon :buttonData="buttonData" @gotoAddNotice="gotoAddNotice('add')"></button-icon>
          </div>
        </div>
        <div class="table-content">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            row-key="id"
            border
            :row-class-name="tableRowClassName"
            header-row-class-name="ps-table-header-row"
            class="ps-table"
          >
            <el-table-column
              prop="title"
              label="公告标题"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="organizationIds"
              label="接收组织"
              align="center"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.receiver_type == 'all_org'">全部组织</span>
                <div v-else>
                  <el-button @click="showCompanyDialog(scope.row)" type="text">查看</el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="status_alias"
              label="状态"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="read_count"
              label="查看人数"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="post_time"
              label="发布时间"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.post_time?scope.row.post_time:'--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="update_time"
              label="修改时间"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.update_time?scope.row.update_time:'--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="sender_name"
              label="创建人"
              align="center"
            ></el-table-column>
            <el-table-column fixed="right" label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="goToNoticeDetail(scope.row)"
                  >查看</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  :disabled="scope.row.status == 2"
                  @click="addNoticeHandle('modify', scope.row)"
                  v-permission="['background_messages.messages.mpdify']"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="deleteNoticeHandle(scope.row)"
                  v-permission="['background_messages.messages.delete']"
                  >删除</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  :disabled="scope.row.status == 2"
                  @click="releaseNoticeHandle(scope.row)"
                  v-permission="['background_messages.messages.bulk_push']"
                  >发布</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- table end -->
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!-- 接受商户 start -->
    <el-dialog
      title="接收商户"
      :visible.sync="dialogVisible"
      width="440px"
      @closed="closeDialogHandle"
      custom-class="notice-dialog"
    >
      <div class="content">
        <el-table :data="dialogData"  max-height="500">
          <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
          <el-table-column property="name" label="商户名称" align="center"></el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button type="primary" size="small" @click="dialogVisible = false">确定</el-button>
      </div>
    </el-dialog>
    <!-- end -->
  </div>
</template>

<script>
import { to, debounce } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'NoticeAdmin',
  // mixins: [activatedLoadData],
  data() {
    return {
      searchForm: {
        noticeTypeTitle: '',
        creator: ''
      },
      creatorList: [],
      tableData: [], // 列表数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isLoading: false,
      selectDate: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'update_time',
          maxWidth: '100px',
          placeholder: '请选择',
          dataList: [
            {
              label: '修改时间',
              value: 'update_time'
            },
            {
              label: '发布时间',
              value: 'post_time'
            }
          ]
        },
        select_time: {
          type: 'datetimerange',
          label: '',
          value: []
        },
        sender: {
          type: 'input',
          label: '创建人',
          value: '',
          placeholder: '请输入创建人'
        },
        title: {
          type: 'input',
          label: '公告标题',
          value: '',
          placeholder: '请输入公告标题'
        },
        status: {
          type: 'select',
          label: '公告状态',
          value: '',
          placeholder: '请选择公告状态',
          dataList: [
            { label: "全部", value: "" },
            { label: "待发布", value: 1 },
            { label: "已发布", value: 2 }
          ]
        }
      },
      buttonData: [
        {
          name: this.$t('button.add_notice'),
          click: 'gotoAddNotice',
          type: 'add',
          color: 'origin',
          permission: ['background_messages.messages.add']
        }
      ],
      dialogData: [],
      dialogVisible: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMsgList()
    },
    resetHandle() {
      this.currentPage = 1
      this.getMsgList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getMsgList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取公告列表
    async getMsgList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 公告撤销
    async revokeNotice(id) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMessagesMessagesDeletePost({
          msg_no: id,
          options: 0
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getMsgList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMsgList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMsgList()
    },
    gotoAddNotice(type, id) {
      this.$router.push({
        name: 'MerchantNoticeAdd',
        params: {
          type
        },
        query: {
          id
        }
      })
    },
    // 新建公告
    addNoticeHandle(type, data) {
      let params = {
        type: type
      }
      if (data) {
        params.msg_no = data.msg_no
      }
      this.$router.push({
        name: 'MerchantNoticeAdd',
        params: {
          type
        },
        query: params
      })
    },
    async goToNoticeDetail(data) {
      if (data.message_type === 1) {
        let id = ''
        const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgReceivePost({
          msg_no: data.msg_no
        }))
        if (err) {
          this.$message.error(err.message)
          return
        }
        if (res.code === 0) {
          id = res.data.content
        }
        const { href } = this.$router.resolve({
          name: 'QuestionnaireDetail',
          query: {
            id: parseInt(id)
          }
        })
        window.open(href, "_blank");
      } else {
        this.$router.push({
          name: 'MerchantNoticeDetail',
          query: {
            msg_no: data.msg_no
          }
        })
      }
    },
    // 删除
    async deleteNoticeHandle(row) {
      if (this.isLoading) return;
      let params = {
        msg_no: row.msg_no
      }
      this.$confirm('确定要删除公告吗，删除后不可恢复。', '提示', {
        confirmButtonText: '删 除',
        cancelButtonText: '取 消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesDeletePost(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            done()
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getMsgList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发布
    async releaseNoticeHandle(row) {
      if (this.isLoading) return;
      let params = {
        msg_nos: [row.msg_no]
      }
      this.$confirm('确定要发布公告吗，发布后不可撤回。', '提示', {
        confirmButtonText: '确 定',
        cancelButtonText: '取 消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesBulkPushPost(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            done()
            if (res.code === 0) {
              this.$message.success('发布成功')
              this.getMsgList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    closeDialogHandle() {
      this.dialogData = []
    },
    showCompanyDialog(row) {
      this.dialogData = row.receivers_name
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss">
#notice-admin-container {
  .notice-admin-list {
    min-width: 0;
    .search-form{
      padding: 20px 20px 0;
    }
    .table-content {
      .no-backgroundColor:hover > td {
        background-color: #ffffff !important;
      }
      .backgroundColor:hover > td {
        background-color: #f5f7fa !important;
      }
      .backgroundColor {
        background-color: #f5f7fa;
        // height: 100%;
      }
      th.el-table-column--selection .cell {
        display: none;
      }
    }
    .column-flex {
      display: flex;
      justify-content: space-between;
      .column-l {
        flex: 1;
        margin-right: 30px;
      }
    }
  }
}
</style>
