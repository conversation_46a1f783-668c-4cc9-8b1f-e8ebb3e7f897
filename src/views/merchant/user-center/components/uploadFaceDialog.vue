<template>
  <div>
    <dialog-message
      width="800px"
      :show-close="false"
      :show.sync="showDialog"
      :showFooter="false"
      center="center"
    >
      <template #title>编辑人脸信息</template>
      <template>
        <div class="dialog-content">
          <div class="left">
            <el-upload
              v-loading="uploading"
              element-loading-text="上传中"
              class="file-upload"
              ref="fileUpload"
              :drag="true"
              :action="serverUrl"
              :data="uploadParams"
              :file-list="fileLists"
              :on-success="uploadSuccess"
              :before-upload="beforeFoodImgUpload"
              :limit="1"
              :multiple="false"
              :show-file-list="false"
              :headers="headersOpts"
            >
              <slot>
                <div class="upload-t" v-if="!formData.imageList.length">
                  <!-- <div class="el-upload__text">
                    <span class="bg-text">上传食材图片</span>
                  </div> -->
                  <div class="">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <em>点击上传</em>
                    </div>
                  </div>
                </div>
                <el-image
                  class="el-upload-dragger upload-img"
                  v-if="formData.imageList.length"
                  :src="formData.imageList[0]"
                  fit="contain"
                  @click="changeFace"
                ></el-image>
              </slot>
            </el-upload>
          </div>
          <div class="right">
            <div class="upload-state">
              <!-- 进行时 -->
              <div class="upload-crossing" v-show="currentState === 'upload_crossing'">
                <i class="el-icon-loading icon-loading"></i>
                <span>正在上传图片</span>
              </div>
              <!-- 成功态 和 删除 -->
              <div
                class="upload-success"
                v-show="currentState === 'upload_success' || currentState === 'upload_delete' || currentState === 'upload_delete_save'"
              >
                <i class="el-icon-check icon-check"></i>
                <span v-show="currentState === 'upload_success'">上传图片成功，请保存</span>
                <!-- <span v-show="currentState === 'upload_delete' && !faceDetailInfo.face_url">
                  人脸信息已删除
                </span> -->
                <span v-show="currentState === 'upload_delete'">
                  图片信息已删除
                </span>
                <span v-show="currentState === 'upload_delete_save' && faceDetailInfo.face_url && deleteBehavior !== 'locality'">
                  图片信息已删除，请保存
                </span>
              </div>
              <!-- 失败态 -->
              <div class="upload-fail" v-show="currentState === 'upload_fail'">
                <i class="el-icon-close icon-close"></i>
                <span>上传失败 + ' ' + {{ uploadFailMsg }}</span>
              </div>
            </div>
            <div class="format-tips unify-font">支持jpg、png、bmp格式, 大小不超过10MB</div>
            <div class="unify-font">提示：</div>
            <div class="unify-font">1、确保上传的图片只存在一张人脸照片信息；</div>
            <div class="unify-font">2、确保上传的图片清晰, 无模糊；</div>
            <div class="unify-font">3、确保人脸位于图片正中央位置, 且占据图片超过60%的区域；</div>
            <div class="unify-font">4、确保拍摄时光线充足；</div>
            <div class="unify-font">5、确保照片为正90度；</div>
            <div class="unify-font">
              6、为避免影响人脸功能的正常使用, 建议6个月后更新一次人脸照片。
            </div>
          </div>
        </div>
      </template>
      <template #tool>
        <div class="footer-btn">
          <el-button class="unify-btn" @click="cancelBtn" :disabled="uploading">取消</el-button>
          <el-button class="ps-btn unify-btn save-btn" :disabled="showSave" @click="save">
            保存
          </el-button>
          <el-button
            class="ps-warn delete-btn unify-btn"
            :disabled="!formData.imageList[0]"
            @click="deleteFace"
          >
            删除
          </el-button>
        </div>
      </template>
    </dialog-message>
  </div>
</template>

<script>
import { getToken, getSuffix, to, deepClone } from '@/utils'
export default {
  props: {
    faceDetailInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    showSave() {
      let temp = true
      if (this.faceDetailInfo.face_url && this.currentState === 'upload_delete_save') {
        temp = false
      } else if (
        this.currentState === 'upload_success' &&
        this.formData.imageList[0]
      ) {
        temp = false
      }
      return temp
    }
  },
  data() {
    return {
      // 控制对话框显示
      showDialog: false,
      // 上传人脸upload组件的参数 start
      uploading: false,
      formData: {
        imageList: []
      },
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      fileLists: [],
      uploadParams: {
        // prefix: 'user_face_img',
        prefix: 'face',
        key: new Date().getTime() + Math.floor(Math.random() * 150)
      },
      // 上传人脸upload组件的参数 end
      // 当前状态 upload_waiting upload_crossing upload_success upload_fail upload_delete
      currentState: 'upload_waiting',
      // 上传失败后的文字信息保存到这里
      uploadFailMsg: '',
      // 备份一个图片地址
      tempFaceUrl: '',
      // 父组件传过来的人脸信息
      userFaceDetailInfo: {},
      // 用于判断是删除本地图片还是数据库的图片 locality本地 archive数据库
      deleteBehavior: ''
    }
  },
  methods: {
    openDialog() {
      this.showDialog = true
      // console.log('收到了传来的信息', this.faceDetailInfo)
      this.userFaceDetailInfo = deepClone(this.faceDetailInfo)
      // 如果有face_url，则为编辑
      if (this.faceDetailInfo.face_url) {
        this.formData.imageList[0] = this.faceDetailInfo.face_url
      }
    },
    closeDialog() {
      this.currentState = 'upload_waiting'
      this.formData.imageList = []
      this.$refs.fileUpload.clearFiles()
      this.showDialog = false
    },
    // 上传图片成功
    uploadSuccess(res, file, fileList) {
      console.log('成功后的res', res)
      console.log('成功后的file', file)
      console.log('成功后的fileList', fileList)
      this.uploading = false
      if (res.code === 0) {
        this.fileLists = fileList
        this.formData.imageList = [res.data.public_url]
        this.currentState = 'upload_success'
        this.deleteBehavior = ''
        console.log(this.formData.imageList)
      } else {
        this.currentState = 'upload_fail'
        this.$message.error(res.msg)
      }
    },
    // 上传之前
    beforeFoodImgUpload(file) {
      this.uploading = true
      this.currentState = 'upload_crossing'
      const unUploadType = ['.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 10
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是JPG/BMP/PNG格式!')
        this.uploading = false
        this.currentState = 'normal'
        this.deleteBehavior = ''
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        this.uploading = false
        this.currentState = 'normal'
        this.deleteBehavior = ''
        return false
      }
      // 手动转换图片格式
      return new Promise((resolve, reject) => {
        this.uploading = true
        const reader = new FileReader()
        reader.onload = event => {
          const img = new Image()
          // 这个是等img加载完成后执行的回调
          img.onload = () => {
            const canvas = document.createElement('canvas') // 创建画布对象
            const ctx = canvas.getContext('2d') // 建立一个二维渲染的上下文，这一步必须要，否则无法调用drawImage
            canvas.width = img.width // 做一个宽高处理，不然会被裁剪调一大部分
            canvas.height = img.height
            ctx.drawImage(img, 0, 0) // 像素处理，可以直接理解成将这个图片完整的渲染出来
            canvas.toBlob(blob => {
              // 这里开始正式进行格式转换，toBlob方法的第一个参数可以获得一个blob对象
              const convertedFile = new File([blob], file.name, {
                // new File生成一个新文件，这里的新文件其实指的就是上传的图片，第三个参数指定了这个文件的类型
                type: 'image/jpeg' // 转换后的图片格式，我们的项目要求是jpeg
              })
              resolve(convertedFile)
              this.currentState = 'upload_crossing'
            }, 'image/jpeg')
          }
          img.onerror = reject
          img.src = event.target.result
        }
        reader.readAsDataURL(file)
      }).catch(() => {
        this.currentState = 'upload_fail'
        this.uploading = false
      })
    },
    // 保存人脸
    async save() {
      if (
        this.currentState === 'upload_success' &&
        this.formData.imageList[0]
      ) {
        // 此时为新增人脸
        // this.interaction('add')
        this.addFaceInfo()
      } else if (this.faceDetailInfo.face_url && this.currentState === 'upload_delete_save') {
        // 此时为删除人脸
        // this.interaction('delete')
        this.deleteFaceAPI()
      }
    },
    // 新增人脸
    async addFaceInfo() {
      const [err, res] = await to(
        this.$apis.apiCardServiceCardUserUploadUserFacePost({
          face_url: this.formData.imageList[0],
          card_info_id: this.faceDetailInfo.id
        })
      )
      if (err) return this.$message.error(err.msg)
      if (res.code === 0) {
        this.$emit('uploadSuccess')
        this.closeDialog()
        this.$message.success(res.msg)
      } else {
        return this.$message.error(res.msg)
      }
    },
    // 删除人脸 --- 提交
    async deleteFaceAPI() {
      const [err, res] = await to(
        this.$apis.apiCardServiceCardUserDeleteFacePost({
          id: this.faceDetailInfo.id
        })
      )
      if (err) return this.$message.error(err.msg)
      if (res.code === 0) {
        this.$emit('uploadSuccess')
        this.closeDialog()
        this.$message.success(res.msg)
      } else {
        return this.$message.error(res.msg)
      }
    },
    // 取消
    cancelBtn() {
      this.deleteBehavior = ''
      this.closeDialog()
    },
    // 删除人脸
    deleteFace() {
      this.$refs.fileUpload.clearFiles()
      this.formData.imageList = []
      if (this.faceDetailInfo.face_url) {
        this.currentState = 'upload_delete_save'
      } else {
        this.currentState = 'upload_delete'
      }
      // if (this.deleteBehavior !== 'locality' && this.deleteBehavior !== '') {
      //   this.currentState = 'upload_delete_save'
      // } else {
      //   this.currentState = 'upload_delete'
      // }
    },
    // 切换图片时直接删除掉本地人脸信息
    changeFace() {
      this.$refs.fileUpload.clearFiles()
      this.formData.imageList = []
      this.deleteBehavior = 'locality'
      this.currentState = 'normal'
    }
  },
  mounted() {}
}
</script>

<style lang="scss" scoped>
// 底部按钮居中展示
.footer-btn {
  display: flex;
  justify-content: center;
}
// 按钮公共样式一
.unify-btn {
  width: 120px;
  height: 40px;
  margin: 0;
}
// 文字统一
.unify-font {
  color: black;
  font-size: 14px;
}
// 标题文字加粗
::v-deep .el-dialog__header {
  font-weight: bold;
  font-size: 18px;
  color: black;
}
// 删除按钮
.delete-btn {
  background-color: #ffebed;
  border-color: #ffebed;
}
// 保存按钮
.save-btn {
  margin-left: 40px;
  margin-right: 40px;
}
// 对话框主要内容
.dialog-content {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 50px;
  .left {
    margin-left: 30px;
    margin-right: 30px;
    ::v-deep .el-upload-dragger {
      width: 230px;
      height: 230px;
    }
    ::v-deep .upload-img {
      cursor: default;
    }
  }
  .right {
    text-align: start;
    .upload-state {
      height: 50px;
      line-height: 20px;
      .upload-crossing {
        .icon-loading {
          font-size: 20px;
          margin-right: 12px;
          color: black;
          vertical-align: text-bottom;
        }
        span {
          font-size: 16px;
          color: black;
        }
      }
      .upload-success {
        .icon-check {
          width: 15px;
          height: 15px;
          background-color: #ff9b45;
          border-radius: 50%;
          text-align: center;
          color: #fff;
          font-weight: bold;
          font-size: 15px;
          line-height: 15px;
          margin-right: 12px;
        }
        span {
          font-size: 16px;
          color: #ff9b45;
        }
      }
      .upload-fail {
        .icon-close {
          width: 15px;
          height: 15px;
          background-color: red;
          border-radius: 50%;
          text-align: center;
          color: #fff;
          font-weight: bold;
          font-size: 15px;
          line-height: 15px;
          margin-right: 12px;
        }
        span {
          font-size: 16px;
          color: red;
        }
      }
    }
    .format-tips {
      margin-bottom: 30px;
    }
  }
}
.el-icon-upload {
  margin-top: 70px;
}
</style>
