<template>
  <div class="CardSubsidy container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddSubsidy('add')" v-permission="['card_service.card_subsidy.add']">新建补贴</button-icon>
          <button-icon color="plain" type="mul" @click="grantSubsidy('clearMul')" v-permission="['card_service.card_subsidy.bulk_clear_subsidy']">批量清零</button-icon>
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['card_service.card_subsidy.list_export']">导出补贴</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          @selection-change="handleSelectionChange"
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column type="selection" class-name="ps-checkbox" width="50" align="center" :selectable="selectDisabled"></el-table-column>
          <el-table-column prop="subsidy_id" label="补贴编号" align="center" width="120" show-overflow-tooltip=""></el-table-column>
          <el-table-column prop="name" label="补贴名称" align="center"></el-table-column>
          <el-table-column prop="card_counts" label="补贴人数" align="center"></el-table-column>
          <el-table-column prop="cur_cycle_release_total" label="本轮发放总金额" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.cur_cycle_release_total | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="max_release_count" label="发放总轮次" align="center"></el-table-column>
          <el-table-column prop="subsidy_type_alias" label="补贴类型" align="center"></el-table-column>
          <el-table-column prop="is_refresh" label="是否清零" align="center">
            <template slot-scope="scope">
              {{scope.row.is_refresh?'清零':'不清零'}}
            </template>
          </el-table-column>
          <el-table-column prop="subsidy_status_alais" label="状态" align="center">
            <template slot-scope="scope">
              <div :class="scope.row.subsidy_status">{{scope.row.subsidy_status_alais}}</div>
              <!-- <img v-if="scope.row.subsidy_status === 'CLEARING'||scope.row.subsidy_status === 'CLEAR'" src="@/assets/img/clear.png" alt="">
              <img v-if="scope.row.subsidy_status === 'RELEASED'" src="@/assets/img/grant.png" alt="">
              <img v-if="scope.row.subsidy_status === 'STARTING'" src="@/assets/img/starting.png" alt="">
              <img v-if="scope.row.subsidy_status === 'NO_START'" src="@/assets/img/unissued.png" alt="">
              <img v-if="scope.row.subsidy_status === 'STOP'" src="@/assets/img/stop.png" alt=""> -->
            </template>
          </el-table-column>
          <el-table-column prop="clear_time_alias" label="自动清零时间" align="center" min-width="110"></el-table-column>
          <el-table-column prop="last_clear_time" label="清零时间" align="center" min-width="110"></el-table-column>
          <el-table-column prop="operate_name" label="操作人员" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center" width="150"></el-table-column>
          <el-table-column prop="effect_time" label="生效时间" align="center" width="150"></el-table-column>
          <el-table-column fixed="right" label="操作" width="190" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="gotoSubsidyDetail(scope.row)" v-permission="['card_service.card_subsidy.info_list']">
                补贴明细
              </el-button>
              <el-button
                type="text"
                class="ps-green"
                size="small"
                v-if="scope.row.subsidy_status !== 'NO_START'"
                @click="gotoReceiveDetail(scope.row)"
                v-permission="['card_service.card_subsidy.get_user_subsidy_details_list']">
                领取明细
              </el-button>
              <el-button
                v-if="scope.row.subsidy_status == 'STARTING'||scope.row.subsidy_status == 'NO_START'"
                type="text"
                size="small"
                class="ps-origin"
                :disabled="!isCurrentOrg(scope.row.organization)"
                @click="gotoAddSubsidy('edit', scope.row.id, scope.row.subsidy_status)"
                v-permission="['card_service.card_subsidy.modify']"
              >
                修改补贴
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-green"
                v-if="scope.row.subsidy_status === 'NO_START' && scope.row.subsidy_type !== 'MONTH_RELEASE' && scope.row.subsidy_type !== 'WEEK_RELEASE'"
                :disabled="!isCurrentOrg(scope.row.organization)"
                @click="grantSubsidy('start', scope.row.id)"
                v-permission="['card_service.card_subsidy.release']"
              >
                发放
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-if="scope.row.subsidy_status == 'STARTING'"
                :disabled="!isCurrentOrg(scope.row.organization)"
                @click="grantSubsidy('stop', scope.row.id)"
                v-permission="['card_service.card_subsidy.stop_release_subsidy']"
              >
                停止发放
              </el-button>
              <el-button
                v-if="scope.row.subsidy_status != 'STARTING' && scope.row.subsidy_status != 'NO_START' && scope.row.subsidy_status != 'CLEAR' && scope.row.subsidy_status != 'CLEARING'"
                type="text"
                size="small"
                class="ps-red"
                :disabled="!isCurrentOrg(scope.row.organization)"
                @click="grantSubsidy('clear', scope.row.id)"
                v-permission="['card_service.card_subsidy.clear_subsidy']"
              >
                清零
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 提示弹窗 -->
    <dialog-message :message="dialogMessage" :show.sync="showDialog" @confirm="confirmGrant" :loading="dialogLoading" />
    <choose-user
      :isshow.sync="userVisible"
      :subsidy-id="subsidyId"
      @confirm="searchHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, parseTime } from '@/utils'
import chooseUser from './components/chooseUser.vue'
import { isCurrentOrg } from './utils'

export default {
  name: 'CardSubsidy',
  components: { chooseUser },
  props: {},
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        years: {
          type: 'select',
          label: '时间',
          multiple: true,
          collapseTags: true,
          value: [new Date().getFullYear()],
          placeholder: '请选择年份',
          dataList: []
        },
        months: {
          type: 'select',
          label: '',
          multiple: true,
          collapseTags: true,
          value: [],
          placeholder: '请选择月份',
          dataList: []
        },
        name: {
          type: 'input',
          label: '补贴名称',
          value: '',
          placeholder: '请输入补贴名称'
        },
        subsidy_id: {
          type: 'input',
          label: '补贴编号',
          value: '',
          placeholder: '请输入补贴编号'
        },
        subsidy_status: {
          type: 'select',
          label: '状态',
          value: '',
          placeholder: '',
          dataList: []
        }
      },
      dialogMessage: '',
      showDialog: false,
      grantType: '',
      selectionVal: [],
      subsidyId: 0,
      userVisible: false, // 选择人员弹窗
      dialogLoading: false,
      isCurrentOrg // 是否是当前组织
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.subsidyList()
      this.statusListInfo()
      this.initYearHandle()
      this.initMonthHandle()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.subsidyList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async subsidyList(isSleep) {
      this.isLoading = true
      // 延时2秒要等后端任务完成，盲猜延时
      if (isSleep) await this.$sleep(2000)
      const res = await this.$apis.apiCardServiceCardSubsidyListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.tableData.map(item => {
          item.effect_time = item.effect_time ? parseTime(new Date(item.effect_time), '{y}-{m}-{d} {h}:{i}:{s}') : ''
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取卡补贴类表
    async statusListInfo() {
      const res = await this.$apis.apiCardServiceCardSubsidySubsidyStatusListPost({})
      if (res.code === 0) {
        this.searchFormSetting.subsidy_status.dataList = [{ label: '全部', value: '' }]
        for (let key in res.data) {
          this.searchFormSetting.subsidy_status.dataList.push({ label: res.data[key], value: key })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    initYearHandle() {
      var myDate, year
      const nowdate = new Date() // 当前时间
      myDate = new Date()
      myDate = new Date(this.defaultdate)
      year = myDate.getFullYear() // 获取当前年
      let len = 35 // 默认生成 30个列表数据
      let mix = nowdate.getFullYear() - year //
      if (mix > 0) {
        len += mix
      }
      for (let i = 0; i < len; i++) {
        this.searchFormSetting.years.dataList.push({
          value: nowdate.getFullYear() - i,
          label: nowdate.getFullYear() - i + '年'
        })
      }
    },
    initMonthHandle() {
      for (let i = 1; i <= 12; i++) {
        let disabled = false
        this.searchFormSetting.months.dataList.push({
          value: i,
          label: i + '月',
          disabled: disabled
        })
      }
    },
    gotoAddSubsidy(type, id, status) {
      this.$router.push({
        name: 'MerchantAddCardSubsidy',
        params: {
          type
        },
        query: {
          type,
          id,
          status
        }
      })
    },
    gotoSubsidyDetail(data) {
      this.$router.push({
        name: 'MerchantCardSubsidyDetail',
        query: {
          id: data.id,
          organization: data.organization,
          subsidy_type: data.subsidy_type
        }
      })
    },
    gotoReceiveDetail(data) {
      this.$router.push({
        name: 'MerchantSubsidyReceiveDetail',
        query: {
          id: data.id,
          organization: data.organization,
          subsidy_type: data.subsidy_type
        }
      })
    },
    grantSubsidy(type, id) {
      this.subsidyId = id
      this.grantType = type
      const clearContent = '清零后，未领取的补贴将失效；如存在退款，其对应金额也将自动清零，是否继续？'
      switch (this.grantType) {
        case 'start':
          this.showDialog = true
          this.dialogMessage = '确定发放补贴吗？'
          break;
        case 'stop':
          this.userVisible = true
          break;
        case 'clear':
          this.showDialog = true
          this.dialogMessage = clearContent
          break;
        case 'clearMul':
          if (this.selectionVal.length > 0) {
            this.dialogMessage = clearContent
            this.showDialog = true
          } else {
            this.showDialog = false
            this.$message.error('请先选择要操作的数据！')
          }
          break;
      }
    },
    confirmGrant() {
      let api
      let params = { id: this.subsidyId }
      switch (this.grantType) {
        case 'start':
          api = this.$apis.apiCardServiceCardSubsidyEffectPost
          break;
        // case 'stop':
        //   api = this.$apis.apiCardServiceCardSubsidyStopReleaseSubsidyPost
        //   break;
        case 'clear':
          api = this.$apis.apiCardServiceCardSubsidyClearSubsidyPost
          break;
        case 'clearMul':
          api = this.$apis.apiCardServiceCardSubsidyBulkClearSubsidyPost
          params = { ids: this.selectionVal }
          break;
      }
      this.operationSubsidy(api, params)
    },
    async operationSubsidy(api, params) {
      this.isLoading = true
      this.dialogLoading = true
      const res = await api(params)
      this.isLoading = false
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success('成功')
        this.subsidyList(true)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleSelectionChange(selection) {
      this.selectionVal = []
      selection.map(item => {
        let flag = true
        this.selectionVal.map(v => {
          if (item.id === v) {
            flag = false
          }
        })
        if (flag) {
          this.selectionVal.push(item.id)
        }
      })
    },
    // 导出
    gotoExport() {
      const option = {
        type: "CardSubsidy",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.subsidyList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.subsidyList()
    },
    selectDisabled(row, index) {
      return this.isCurrentOrg(row.organization)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.CLEARING{
  background-color: #8f8f8f;
  color: #ffffff;
  border-radius: 15px;
}
.CLEAR{
  color: #8f8f8f;
  background-color: #eaeaea;
  border-radius: 15px;
}
.RELEASED{
  color: #1fc364;
  background-color: #daf2e4;
  border-radius: 15px;
}
.STARTING{
  color: #ffffff;
  background-color: #1fc364;
  border-radius: 15px;
}
.NO_START{
  color: #ffffff;
  background-color: #fd9639;
  border-radius: 15px;
}
.STOP{
  color: #FD594E;
  background-color: #FFE6E5;
  border-radius: 15px;
}
</style>
