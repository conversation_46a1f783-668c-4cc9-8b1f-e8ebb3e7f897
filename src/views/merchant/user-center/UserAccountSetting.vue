<template>
  <div class="UserAccountSetting container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper" style="margin-top: 0px;" v-permission="['card_service.card_user_group.get_org_flat_and_patch_cost_settings']">
      <div class="table-header">
        <div class="table-title">工本费设置</div>
        <div style="padding-right:20px;">
          <!-- <el-button size="small" type="primary" class="ps-origin-btn" @click="saveCostSettings">保存</el-button> -->
          <!-- <span style="padding-right:10px;font-size: 15px;font-weight: 600;">系统自动扣除工本费(押金)</span>
          <el-switch v-model="flatCostForm.isAutoDeduction" @change="saveCostSettings" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch> -->
        </div>
      </div>
      <el-form
        v-loading="isLoading"
        :model="flatCostForm"
        :rules="flatCostRules"
        inline
        ref="ruleForm"
        class="ruleForm"
      >
        <el-form-item label-width="120px" label="工本费(押金)/元" prop="flatCost">
          <el-input v-model="flatCostForm.flatCost" @change="saveCostSettings" class="ps-input"></el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="补卡费/元" prop="repairCost">
          <el-input v-model="flatCostForm.repairCost" @change="saveCostSettings" class="ps-input"></el-input>
        </el-form-item>
        <el-form-item label-width="160px" label="工本费(押金)是否退还">
          <el-switch v-model="flatCostForm.isRetreat" @change="saveCostSettings" active-color="#ff9b45" inactive-color="#ffcda2"> </el-switch>
        </el-form-item>
        <el-form-item label-width="185px" label="系统自动扣除工本费(押金)">
         <el-switch v-model="flatCostForm.isAutoDeduction" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <div class="tips">
          <div style="width:40px;">
            <p>注：</p>
          </div>
          <div style="margin-bottom:10px;">
            <p>1.如押金、补卡费输入0元，则表示不收取工本费</p>
            <p>2.工本费是消费者在系统中首次发卡时需要记录扣除的费用，可设置是否退还，退还即为押金形式（押金可退还）</p>
            <p>3.补卡费是消费者在进行卡片挂失后，进行了重新发卡后，需要记录扣除的费用</p>
            <p>4. 扣除工本费时，需储值钱包余额大于等于工本费金额</p>
            <p>5.选择分组设置，可对指定分组人员设置工本费、补卡费、退还设置</p>
          </div>
        </div>
      </el-form>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title" style="width: 600px;">特殊分组设置：
          <tree-select
            :multiple="true"
            :options="groupList"
            :flat="true"
            :limit="1"
            :limitText="count => '+' + count"
            :default-expand-level="1"
            :normalizer="groupNode"
            :clearable="false"
            placeholder="请选择"
            v-model="specialGroup"
            :appendToBody="true"
            @select="selectGroup"
            @deselect="deselectGroup"
          >
          </tree-select>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'has_children' }"
        >
          <el-table-column prop="group_id" label="分组编号" align="center"></el-table-column>
          <el-table-column prop="group_name" label="分组名称" align="center"></el-table-column>
          <el-table-column prop="card_counts" label="用户人数" align="center"></el-table-column>
          <el-table-column prop="flat_cost" label="工本费（押金）/元" align="center">
            <template slot-scope="scope">
              <el-input style="width:100%" class="ps-input" v-model="scope.row.flat_cost" @change="changeSpecialGroup(scope.row)"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="patch_cost" label="补卡费/元" align="center">
            <template slot-scope="scope">
              <el-input style="width:100%" class="ps-input" v-model="scope.row.patch_cost" @change="changeSpecialGroup(scope.row)"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="isRetreat" label="工本费（押金）退还" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.is_flat_return" active-color="#ff9b45" inactive-color="#ffcda2" @change="changeSpecialGroup(scope.row)"> </el-switch>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { validataPlateAmountPrice } from '@/assets/js/validata'
import { times, divide } from '@/utils'

export default {
  name: 'UserAccountSetting',
  components: {},
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      flatCostForm: {
        flatCost: '',
        repairCost: '',
        isRetreat: false,
        isAutoDeduction: true
      },
      flatCostRules: {
        flatCost: [{ required: true, validator: validataPlateAmountPrice, trigger: "blur" }],
        repairCost: [{ required: true, validator: validataPlateAmountPrice, trigger: "blur" }]
      },
      tableData: [],
      groupList: [],
      specialGroup: null
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getCostSettings()
      this.getGroupList()
    },
    refreshHandle() {
      this.initLoad()
    },
    // 获取组织以及特殊分组开卡补卡
    async getCostSettings() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupGetOrgFlatAndPatchCostSettingsPost()
      this.isLoading = false
      if (res.code === 0) {
        this.flatCostForm.flatCost = divide(res.data.flat_cost)
        this.flatCostForm.repairCost = divide(res.data.patch_cost)
        this.flatCostForm.isRetreat = res.data.is_flat_return
        this.flatCostForm.isAutoDeduction = res.data.is_open_flat
        this.tableData = res.data.open_flat_group_list
        this.tableData.length ? this.specialGroup = [] : this.specialGroup = null
        this.tableData.map(item => {
          item.flat_cost = divide(item.flat_cost)
          item.patch_cost = divide(item.patch_cost)
          this.specialGroup.push(item.id)
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 保存组织开卡补卡
    async saveCostSettings() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupModifyOrgFlatAndPatchCostPost({
        flat_cost: times(this.flatCostForm.flatCost),
        patch_cost: times(this.flatCostForm.repairCost),
        is_flat_return: this.flatCostForm.isRetreat,
        is_open_flat: this.flatCostForm.isAutoDeduction
      })
      this.isLoading = false
      if (res.code === 0) {
        this.getCostSettings()
        this.$message.success('修改成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 保存分组开卡补卡
    async changeSpecialGroup(data) {
      let params = {
        id: data.id,
        flat_cost: times(data.flat_cost),
        patch_cost: times(data.patch_cost),
        is_flat_return: data.is_flat_return,
        is_open_flat: data.is_open_flat
      }
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupModifyFlatAndPatchCostPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.getCostSettings()
        this.$message.success('修改成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    selectGroup(value) {
      console.log(value)
      value.is_open_flat = true
      value.flat_cost = 0
      value.patch_cost = 0
      this.changeSpecialGroup(value)
    },
    deselectGroup(value) {
      value.is_open_flat = false
      value.flat_cost = 0
      value.patch_cost = 0
      this.changeSpecialGroup(value)
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      // this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      // this.getAccountList()
    },
    // 获取分组信息
    async getGroupList() {
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.groupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    groupNode(node) {
      return {
        id: node.id,
        label: node.group_name,
        children: node.children_list
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  .UserAccountSetting{
    .ruleForm{
      padding: 0 25px;
      .el-form-item{
        margin-bottom: 15px;
      }
      .tips{
        display: flex;
        font-size: 14px;
        font-weight: 600;
        color: #b1b1b1;
      }
    }
  }
  .vue-treeselect{
    display: inline-block;
    width: 250px;
    height: 24px;
    font-size: 16px;
  }
</style>
