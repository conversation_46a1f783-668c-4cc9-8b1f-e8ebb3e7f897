<template>
  <div class="UserGroup container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon :buttonData="buttonData" @openDialogHaldler="openDialogHaldler('add')" @importHaldler="importHaldler"></button-icon>
        </div>
      </div>
      <div class="table-content">
      <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'has_children' }"
        >
          <el-table-column class-name="th-row" prop="group_id" label="分组编号" ></el-table-column>
          <el-table-column prop="group_name" label="分组名称" ></el-table-column>
          <el-table-column prop="card_counts" label="用户人数" ></el-table-column>
          <el-table-column prop="update_time" label="创建时间" sortable="custom"></el-table-column>
          <el-table-column
            class-name="tools-row"
            align="right"
            width="180"
            label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                v-if="isCurrentOrg(scope.row.organization)"
                @click="goToConsumption(scope.row)"
                v-permission="['background_marketing.consume.add']"
              >消费类型</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="isCurrentOrg(scope.row.organization)"
                @click="openDialogHaldler('modify', scope.row)"
                v-permission="['card_service.card_user_group.modify']"
              >编辑</el-button>
              <el-button
                :disabled="scope.row.type==0"
                type="text"
                size="small"
                class="ps-warn"
                v-if="isCurrentOrg(scope.row.organization)"
                @click="deleteHaldler('one', scope.row)"
                v-permission="['card_service.card_user_group.delete']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <!-- 分页 end -->
    <!-- 编辑/添加弹窗 start -->
    <!-- <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="350px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @close="dialogHandleClose">
      <el-form ref="formData" v-loading="formLoading" :rules="formDataRuls" :model="formData" class="dialog-form">
        <el-form-item prop="groupNo" label="分组编号：">
          <el-input class="ps-input" v-model="formData.groupNo" placeholder="请输入分组编号，不填则随机生成" :disabled="dialogType=='modify'?true:false"></el-input>
        </el-form-item>
        <el-form-item prop="groupName" label="分组名称：">
          <el-input class="ps-input" v-model="formData.groupName"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" :disabled="formLoading" @click="dialogVisible = false">取 消</el-button>
        <el-button class="ps-btn" :disabled="formLoading" type="primary" @click="submitDialogHandler">确 定</el-button>
      </span>
    </el-dialog> -->
    <add-user-group-drawer
      v-if="userGroupDrawerrVisible"
      :isshow.sync="userGroupDrawerrVisible"
      :drawerType="dialogType"
      :drawerModifyData="drawerModifyData"
      :updateInterFace="UserGroupList"
    ></add-user-group-drawer>
    <!-- 弹窗 end -->
    <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="tableSetting"
      :show.sync="departmentDialog"
      title="导入分组"
      openExcelType="UserGroup"
    ></import-dialog-drawer>
  </div>
</template>

<script>
import { to, deepClone } from '@/utils'
import { isCurrentOrg } from './utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import AddUserGroupDrawer from './components/AddUserGroupDrawer.vue'

export default {
  name: 'UserGroup',
  components: { AddUserGroupDrawer },
  // mixins: [activatedLoadData],
  data() {
    // var groupNoValidator = (rule, value, callback) => {
    //   if (value !== "") {
    //     if (!/^[0-9]{1,6}$/.test(value)) {
    //       callback(new Error("请输入由数字组成的编号，长度不超过六位"));
    //     } else {
    //       callback();
    //     }
    //   } else {
    //     callback();
    //   }
    // };
    return {
      // dialogTitle: '',
      dialogType: '',
      userGroupDrawerrVisible: false,
      drawerModifyData: {},
      // dialogVisible: false,
      searchForm: {
        name: ''
      },
      // formData: {
      //   id: '',
      //   groupNo: '',
      //   groupName: '',
      //   parentId: ''
      // },
      tableData: [],
      // formDataRuls: {
      //   groupNo: [{ validator: groupNoValidator, trigger: "blur" }],
      //   groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
      // },
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      pageCount: 0,
      formLoading: false,
      type: '',
      isLoading: false, // 刷新数据
      buttonData: [
        {
          name: '新建分组',
          click: 'openDialogHaldler',
          type: 'add',
          color: 'origin',
          permission: ['card_service.card_user_group.add']
        },
        {
          name: '导入分组',
          click: 'importHaldler',
          type: 'Import',
          color: 'plain',
          permission: ['card_service.card_user_group.batch_import']
        }
      ],
      templateUrl:
        location.origin + '/api/temporary/template_excel/卡务模板/导入用户分组.xls',
      tableSetting: [
        { key: 'group_no', label: '分组编号' },
        { key: 'group_name', label: '分组名称' }
      ],
      departmentDialog: false
    }
  },
  created () {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
      this.UserGroupList();
    },
    // 刷新页面
    refreshHandle() {
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async UserGroupList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiCardServiceCardUserGroupListPost({
        is_show_other: true,
        status: 'enable',
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // dialogHandleClose() {
    //   this.resetFormData()
    //   // this.dialogVisible = false
    //   this.dialogType = ''
    // },
    // resetFormData() {
    //   this.formData.parentId = ''
    //   this.formData.groupName = ''
    //   this.formData.groupNo = ''
    //   this.formData.id = ''
    //   this.$refs.formData.clearValidate()
    // },
    openDialogHaldler(type, row) {
      this.dialogType = type
      this.userGroupDrawerrVisible = true
      if (type === 'modify') {
        this.drawerModifyData = deepClone(row)
      }
      // if (type === 'add') {
      //   this.dialogTitle = '新建分组'
      // } else {
      //   this.formData.id = row.id
      //   this.dialogTitle = '编辑分组'
      //   this.formData.groupName = row.group_name
      //   this.formData.groupNo = row.group_id
      // }
      // this.dialogVisible = true
    },
    // submitDialogHandler() {
    //   this.$refs.formData.validate(valid => {
    //     if (valid) {
    //       if (!this.formData.groupNo) {
    //         this.formData.groupNo = this.mathRand()
    //       }
    //       if (this.dialogType === 'add') {
    //         this.addHandler({
    //           group_id: this.formData.groupNo,
    //           group_name: this.formData.groupName
    //         })
    //       } else {
    //         this.modifyHandler({
    //           id: this.formData.id,
    //           parent: this.formData.parentId,
    //           group_name: this.formData.groupName
    //         })
    //       }
    //     } else {
    //       // console.log('error submit!!');
    //     }
    //   })
    // },
    // mathRand() {
    //   let Num = "";
    //   for (var i = 0; i < 6; i++) {
    //     Num += Math.floor(Math.random() * 10);
    //   }
    //   return Num
    // },
    // async addHandler(params) {
    //   this.formLoading = true
    //   const [err, res] = await to(this.$apis.apiCardServiceCardUserGroupAddPost(params))
    //   this.formLoading = false
    //   if (err) {
    //     this.$message.error(err.message)
    //     return
    //   }
    //   if (res.code === 0) {
    //     this.dialogVisible = false
    //     this.$message.success('添加成功')
    //     this.UserGroupList()
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    // async modifyHandler(params) {
    //   this.formLoading = true
    //   const [err, res] = await to(this.$apis.apiCardServiceCardUserGroupModifyPost(params))
    //   this.formLoading = false
    //   if (err) {
    //     this.$message.error(err.message)
    //     return
    //   }
    //   if (res.code === 0) {
    //     this.dialogVisible = false
    //     this.$message.success('修改成功')
    //     this.UserGroupList()
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    deleteHaldler(type, data) {
      let delId = ''
      if (type === 'one') {
        delId = data.id
      }
      this.$confirm('是否删除该分组？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiCardServiceCardUserGroupDeletePost({
                ids: [delId]
              })
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success('删除成功')
              this.UserGroupList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    importHaldler() {
      this.departmentDialog = true
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.UserGroupList()
    },
    // 跳转消费规则页面
    goToConsumption(row) {
      this.$router.push({
        name: 'MerchantConsumptionRulesForm',
        params: {
          type: 'add'
        },
        query: {
          group: row.id
        }
      })
    },
    isCurrentOrg
  }
}
</script>

<style lang="scss">

</style>
