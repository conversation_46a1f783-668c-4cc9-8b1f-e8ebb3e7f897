<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :width="width"
    top="10vh"
    class="ChooseListDialog"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <slot name="header">
      <div class="search-wrapper">
        <slot name="search">
          <el-form :model="searchFormSetting" inline ref="searchFormRef" :size="size">
            <el-form-item
              v-for="(item, key) in searchFormSetting"
              :key="key"
              :label="item.label"
              :prop="key + '.value'"
              :label-width="item.labelWidth"
            >
              <el-date-picker
                v-if="item.type === 'daterange'"
                v-model="item.value"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                unlink-panels
                clearable
                class="ps-picker"
                popper-class="ps-poper-picker"
                @change="searchHandle"
              ></el-date-picker>
              <el-input
                v-if="item.type === 'input'"
                v-model="item.value"
                class="ps-input w-220"
                @input="searchHandle"
              ></el-input>
              <el-select v-if="item.type==='select'" class="search-item-w ps-select" popper-class="ps-popper-select" v-model="item.value" :placeholder="item.placeholder" :multiple="item.multiple" :collapse-tags="item.collapseTags" :clearable="item.clearable" :filterable="item.filterable" :style="{'width':item.maxWidth}" @change="searchHandle">
                <el-option v-for="(option, i) in item.dataList" :key="i" :label="item.listNameKey?option[item.listNameKey]:option.label" :value="item.listValueKey?option[item.listValueKey]:option.value" :disabled="option.disabled"></el-option>
              </el-select>
              <lazy-select v-if="item.type==='lazySelect'" class="search-item-w" v-model="item.value" :placeholder="item.placeholder" :multiple="item.multiple" :collapse-tags="item.collapseTags" :clearable="item.clearable" :filterable="item.filterable" :style="{'width':item.maxWidth}" :extra-opttions="item.extraOpttions" :params="item.params" :filter-key="item.filterKey" :is-lazy="item.isLazy" :api-url="item.apiUrl" :result-key="item.resultKey"  @change="searchHandle"></lazy-select>
            </el-form-item>
          </el-form>
        </slot>
      </div>
      <div v-if="showSelectLen" class="">当前选择数量：{{ selectLenght }}</div>
      <slot name="tip" :select="selectList"></slot>
    </slot>
    <div class="m-t-10">
      <el-form
        v-loading="isLoading"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="0"
        size="small"
        class="dialogForm"
      >
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="formData.tableData"
          ref="tableData"
          style="width: 100%"
          size="small"
          stripe
          header-row-class-name="ps-table-header-row"
          max-height="600"
          :row-key="rowKey"
          reserve-selection
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item" :width="item.width">
            <template #radio="{ row }">
              <el-radio
                :label="row[rowKey]"
                v-model="checkRadio">{{ ' ' }}
              </el-radio>
            </template>
            <template #count="{ row, index }">
              <!-- 为什么要给个key呢，大概是使用了v-if,v-show,三元表达式等,对校验的el-form-item做了动态的判定，需要让变化后的key也发生改变,才能重新渲染,让校验恢复正常 -->
              <!-- 简单来说就是rule这些属性只使用了第一次的渲染结果，后续不会根据row.select的值进行变化。所以需要给个变化的key让其根据值变化时重新渲染节点 -->
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData.' + index + '.count'"
                :rules="row.select ? formRules.count : []"
                :width="item.width"
                class="m-b-0 max-w-220"
              >
                <el-input v-model="row.count" :disabled="!row.select" class="" @input="changeCountHandle($event, row, index)"></el-input>
              </el-form-item>
            </template>
            <template #oneDecimalCount="{ row, index }">
              <!-- 为什么要给个key呢，大概是使用了v-if,v-show,三元表达式等,对校验的el-form-item做了动态的判定，需要让变化后的key也发生改变,才能重新渲染,让校验恢复正常 -->
              <!-- 简单来说就是rule这些属性只使用了第一次的渲染结果，后续不会根据row.select的值进行变化。所以需要给个变化的key让其根据值变化时重新渲染节点 -->
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData.' + index + '.count'"
                :rules="row.select ? formRules.oneDecimalCount : []"
                :width="item.width"
                class="m-b-0 max-w-220"
              >
                <div class="flex">
                  <el-input style="width: 138px;" v-model="row.count" :disabled="!row.select" class="" @input="changeCountHandle($event, row, index)"></el-input>
                  <div v-if="row.specs_item" style="width: 50px; margin-left: 2px;">{{ row.specs_item.unit_management_name }}</div>
                </div>
              </el-form-item>
            </template>
            <!-- inventory用于出库单或调拨单，输入数据需要<=当前库存 -->
            <template #inventory="{ row, index }">
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData.' + index + '.changeCount'"
                :rules="row.select ? formRules.changeEmptyCount : []"
                :width="item.width"
                class="m-b-0 max-w-220"
              >
                <el-input v-model="row.changeCount" :disabled="!row.select" class=""></el-input>
              </el-form-item>
            </template>
            <template #date="{ row, index }">
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData[' + index + '].date'"
                :rules="row.select ? formRules.date : []"
                class="m-b-0"
              >
                <span class="inline-block ps-btn-span pointer">
                  {{ row.date.length > 1 ? `${row.date[0]}至${row.date[1]}` : '请选择' }}
                  <el-date-picker
                    v-model="row.date"
                    type="daterange"
                    placeholder="选择日期"
                    class="ps-picker w-auto"
                    popper-class="ps-poper-picker"
                    value-format="yyyy-MM-dd"
                    append-to-body
                  ></el-date-picker>
                </span>
              </el-form-item>
            </template>
            <template #supplier="{ row, index }">
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData[' + index + '].supplier'"
                :rules="row.select ? formRules.supplier : []"
                class="m-b-0 max-w-220"
              >
                <el-select v-model="row.supplier" :disabled="!row.select" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSupplier($event, row, index)">
                  <el-option v-for="option in row.price_info" :key="option.supplier_manage_id" :label="option.supplier_manage_name" :value="option.supplier_manage_id" ></el-option>
                </el-select>
              </el-form-item>
            </template>
            <template #specs="{ row, index }">
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData[' + index + '].specs'"
                :rules="row.select ? formRules.specs : []"
                class="m-b-0 max-w-220"
              >
                <el-select v-model="row.specs" :disabled="!row.select || !row.supplier" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSpecs($event, row, index)">
                  <el-option v-for="option in row.specification_list" :key="option.id" :label="`1${option.unit_management_name}*${option.count}${option.limit_unit_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" ></el-option>
                </el-select>
              </el-form-item>
            </template>
            <template #unitPrice="{ row }">
              <div v-if="row.specs_item">
                <span>￥{{ row.specs_item.unit_price | formatMoney }}</span>
                <span>/{{ row.specs_item.unit_management_name }}</span>
              </div>
            </template>
            <template #limitUnit="{ row, index }">
              <el-form-item
                :key="item.key + index + row.select"
                label=""
                :prop="'tableData[' + index + '].limit_unit_id'"
                :rules="row.select ? formRules.specs : []"
                class="m-b-0 max-w-220"
              >
                <el-select v-model="row.limit_unit_id" :disabled="!row.select" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeLimitUnit($event, row, index)">
                  <el-option v-for="option in row.limit_unit_management" :key="option.id" :label="`${option.unit_management_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" ></el-option>
                </el-select>
              </el-form-item>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showViewHandle(row)">查看</el-button>
              <el-button v-if="type === 'template'" type="text" size="small" class="ps-warn" @click="deleteHandle('single', row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </el-form>
    </div>
    <!-- 分页 start -->
    <div v-if="totalCount && type !== 'associated_purchase'" class="block" style="text-align: right">
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
    </div>
    <view-dialog :showview.sync="showViewDialog" type="static" :tableSettings="viewTableSettings" :staticList="viewStaticList">
      <div v-if="showViewDialogTitle" slot="header" class="m-b-20">当前模板：<span style="font-weight: 600;">{{ viewDialogInfo.name }}</span></div>
    </view-dialog>
  </dialog-message>
  <!-- end -->
</template>

<script>
// 草稿箱
import { debounce, getSevenDateRange, deepClone } from '@/utils'
import { integer, oneDecimal } from '@/utils/validata'
import { validateOneDecimalCount } from '@/utils/form-validata'
import ViewDialog from './ViewDialog'
import NP from 'number-precision'
import { type } from '@/utils/type'

export default {
  name: 'ChooseListDialog',
  components: {
    ViewDialog
  },
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'material' // material：添加物资，purchase: 采购单
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '860px'
    },
    // 获取列表数据接口
    api: {
      type: String,
      required: true
    },
    // 查看详情接口，目前只有模板有详情
    detailApi: {
      type: String,
      default: 'apiBackgroundDrpInquiryTempDetailsListPost'
    },
    // 数据
    InfoData: {
      type: Object,
      default() {
        return {}
      }
    },
    // size
    size: {
      type: String,
      default: 'small'
    },
    // 是否显示选中的数量
    showSelectLen: {
      type: Boolean,
      default: false
    },
    // 搜索条件配置
    searchSetting: {
      type: Object,
      default: () => {
        return {
          //   aa: {
          //     type: 'input',
          //     value: '',
          //     label: '',
          //     labelWidth: ''
          //   }
        }
      }
    },
    // 调用接口的默认参数
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    tableSettings: {
      type: Array,
      default() {
        return [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'name' },
          { label: '当前库存', key: 'current_num', type: 'count' },
          { label: '入库数量', key: 'count', type: 'slot', slotName: 'count' },
          { label: '有效期', key: 'date', type: 'slot', slotName: 'date' }
          // { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right" }
        ]
      }
    },
    // rowKey
    rowKey: {
      type: String,
      default: 'materials_id'
    },
    // 默认选择的数据
    defaultSelect: {
      type: [Array, Number],
      default() {
        return []
      }
    },
    // 格式化请求回来的数据
    formatResult: Function
    // modifyfun: Function,
    // deletefun: Function
  },
  // mixins: [activatedLoadData],
  data() {
    const validateCount = (rule, value, callback) => {
      if (value) {
        if (!integer(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入必填项'))
      }
    }
    // 允许为空
    // const validateEmptyCount = (rule, value, callback) => {
    //   if (value) {
    //     if (!integer(value)) {
    //       callback(new Error('格式错误'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // }
    const validateChangeCount = (rule, value, callback) => {
      if (value) {
        if (!integer(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入数量'))
      }
    }
    return {
      isLoading: false,
      searchFormSetting: {},
      dateRange: getSevenDateRange(7),
      searchName: '', // 物资名称
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      selectList: [],
      formData: {
        tableData: []
      },
      formRules: {
        count: [{ validator: validateCount, trigger: 'change' }],
        oneDecimalCount: [{ validator: validateOneDecimalCount, trigger: 'change' }],
        // date: [{ required: true, message: '请选择有效期', trigger: 'change' }],
        supplier: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        specs: [{ required: true, message: '请选择规格', trigger: 'change' }],
        changeCount: [{ validator: validateChangeCount, trigger: 'change' }],
        changeEmptyCount: [{ validator: this.validateEmptyCount, trigger: 'change' }]
      },
      // 查看弹窗
      showViewDialog: false,
      viewDialogInfo: {},
      showViewDialogTitle: false,
      viewTableSettings: [
        { label: '名称', key: 'name' }
      ],
      viewStaticList: [],
      checkRadio: '', // 单选数据
      inventoryKey: 'current_num' // 需要校验库存的字段，用于调拨或出库的数量输入框校验
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    },
    // 当前选择数量
    selectLenght() {
      return this.selectList.length
    }
  },
  watch: {
    // 为什么监听searchSetting，因为通过object类型进行赋值的会直接更改源数据的，当前需要一个新的数据，而非显示上次的输入结果
    searchSetting: {
      handler(newVal) {
        this.searchFormSetting = deepClone(newVal)
      },
      immediate: true
    },
    // 监听下弹窗是否打开再请求相应的数据
    showdialog(val) {
      if (val) {
        this.initHandle()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    initHandle() {
      // 重置页码
      this.currentPage = 1
      this.gettableDataList()
      // 设置选中table状态
      // this.$refs.tableData.toggleRowSelection(row, true)
      // 初始化需要校验库存的字段
      if (this.type === 'associated_purchase') {
        this.inventoryKey = 'current_count'
      } else {
        this.inventoryKey = 'limit_unit_stock'
      }
    },
    // 获取列表数据
    async gettableDataList() {
      if (!this.api || !this.$apis[this.api]) {
        return this.$message.error('获取接口地址失败！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis[this.api]({
          ...this.formatQueryParams(this.searchFormSetting),
          ...this.params,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '出错了')
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        let results = (res.data.results ? res.data.results : res.data) || []
        // 格式化数据
        if (this.formatResult && typeof this.formatResult === 'function') {
          results = this.formatResult(results)
        }
        if (results.length > 0) {
          results = results.map((v, index) => {
            // 采购数量
            v.count = ''
            // 调拨或出库数量
            v.changeCount = ''
            // 调拨或出库数量输入校验结果
            v.error = ''
            // 有效期
            v.date = []
            // 这个自定义字段标识下当前数据被选中
            v.select = false
            // 选中的规格
            v.specs = ''
            // 选中的规格数据
            v.specs_item = null
            // 规格列表
            v.specification_list = []
            // 单价
            v.ref_unit_price = ''
            // 最小单位
            v.limit_unit = ''
            // 合计金额
            v.total_price = ''
            // 采购单才需要供应商
            if (this.type === 'purchase') {
              v.supplier = ''
            }
            // 入库单
            if (this.type === 'entry_info') {
              v.current_num = 0
              v.limit_unit_management.map(item => {
                v.current_num = NP.plus(v.current_num, item.current_num)
              })
            }
            // 关联入库单
            if (this.type === 'associated_purchase') {
              v.custom_id = v.trade_no + '_' + v.supplier_manage_id
              v.current_count = v.current_count
              v.limit_unit_id = this.params.limit_unit_id
              v.limit_unit_item = this.params.limit_unit_item
              v.materials_id = this.params.materials_id
              v.materials_name = this.params.materials_name
              v.total_limit_unit_stock = this.params.total_limit_unit_stock
            }
            // 出库、调拨需要最小单位列表，和最小单位库存
            if (this.type === 'exit_info' || this.type === 'transfer_order') {
              v.limit_unit_id = ''
              v.limit_unit_item = null
              v.limit_unit_stock = '' // 最小单位库存
            }
            return v
          })
        }
        this.$set(this.formData, 'tableData', results)
        this.setDefaultSelectHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化下参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.gettableDataList()
    }, 300),
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.gettableDataList()
    },
    // 设置table 反选
    setDefaultSelectHandle() {
      if (this.type === 'associated_purchase') {
        if (type(this.defaultSelect) === 'array' && this.defaultSelect.length > 0) {
          this.$nextTick(_ => {
            // 关联入库单时需要反选
            let selectObj = {}
            this.defaultSelect.forEach(v => {
              let customId = v.trade_no + '_' + v.supplier_manage_id
              selectObj[customId] = v
            })
            for (let index = 0; index < this.formData.tableData.length; index++) {
              const item = this.formData.tableData[index];
              let key = item.trade_no + '_' + item.supplier_manage_id
              if (selectObj[key]) {
                // 有两种情况，一种是手动关联的，一种是自动关联的，字段不同
                if (selectObj[key].changeCount) {
                  this.$set(item, 'changeCount', selectObj[key].changeCount)
                } else if (selectObj[key].use_count) {
                  this.$set(item, 'changeCount', selectObj[key].use_count)
                } else {
                  this.$set(item, 'changeCount', '')
                }

                this.$refs.tableData.toggleRowSelection(item, true)
              }
            }
          })
        }
        return
      }
      // 当存在反选数据时
      // 多选
      if (type(this.defaultSelect) === 'array' && this.defaultSelect.length > 0) {
        this.$nextTick(_ => {
          for (let index = 0; index < this.formData.tableData.length; index++) {
            const item = this.formData.tableData[index];
            if (this.defaultSelect.includes(item[this.rowKey])) {
              this.$refs.tableData.toggleRowSelection(item, true)
            }
          }
        })
      }
      // 单选
      if (type(this.defaultSelect) === 'number') {
        this.checkRadio = this.defaultSelect
      }

      // 还有一种情况是筛选也要还原select数据，有可能会和defaultSelect重叠
      if (this.selectList.length > 0) {
        const selectIds = this.selectList.map(v => v[this.rowKey])
        this.formData.tableData.forEach(item => {
          if (selectIds.includes(item[this.rowKey])) {
            this.$set(item, 'select', true)
          }
        })
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('select', val, this.rowKey)
      let selectId = []
      if (val) {
        selectId = val.map(v => v[this.rowKey])
      }
      // 设置选中数据的标识，校验时需要先判断当前数据是否选中，选中才校验输入框或日期是否有值
      this.formData.tableData.forEach(v => {
        this.$set(v, 'select', selectId.includes(v[this.rowKey]))
      })
      this.selectList = val
      // 触发下select
      // this.$emit('select', val)
    },
    // 调拨或出库数量输入框校验
    validateEmptyCount(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        // console.log(rule, value, fieldArr)
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        // console.log(111, row)
        if (value) {
          if (!oneDecimal(value)) {
            callback(new Error('格式错误'))
          } else {
            if (row && Number(row[this.inventoryKey]) < Number(value)) {
              callback(new Error('不能大于当前库存'))
            } else {
              callback()
            }
          }
        } else {
          callback()
        }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 确定事件
    clickConfirmHandle() {
      // 替换物资
      if (this.type === 'replace_materials') {
        this.selectList = this.formData.tableData.filter(v => v[this.rowKey] === this.checkRadio)
      }
      if (!this.selectList.length) return this.$message.error('请先选择数据！')

      this.$refs.formRef.validate(valid => {
        if (valid) {
          // this.sendDataHandle(this.formatParams())
          // 如果是模板的话需要调详情接口获取数据
          if (this.params.temp_type === 'template') {
            let params = {
              ids: this.selectList.map(v => v.id),
              inventory_info_type: this.params.inventory_info_type
              // warehouse_id: this.params.warehouse_id
            }
            this.getDetails(params)
          } else if (this.params.temp_type === 'inquiry') {
            let params = {
              ids: this.selectList.map(v => v.id),
              inventory_info_type: this.params.inventory_info_type,
              warehouse_id: this.params.warehouse_id
            }
            this.getDetails(params)
          } else if (this.type === 'select_purchase') {
            let params = {
              purchase_ids: this.selectList.map(v => v.id),
              warehouse_id: this.params.warehouse_id
            }
            this.getDetails(params)
          } else {
            console.log(this.selectList)
            // return
            this.$emit('confirmChoose', {
              type: this.type,
              data: deepClone(this.selectList)
            })
          }
        } else {
          this.$message.error('请先检查数据是否有误！')
        }
      })
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handlerClose(e) {
      console.log(11111, 'close')
      this.selectList = []
      this.formData.tableData = []
      this.isLoading = false
      if (this.$refs.tableData) {
        // 请除table选中状态
        this.$refs.tableData.clearSelection()
      }
      // this.visible = false
      this.$emit('dialogClose')
    },
    // 查看
    showViewHandle(data) {
      this.viewDialogInfo = data
      // 先重置，再开启吧，懒得改其它的了
      this.showViewDialogTitle = false
      let params = {}
      if (this.type === 'template') {
        params = {
          ids: [data.id],
          inventory_info_type: this.params.inventory_info_type,
          warehouse_id: this.params.warehouse_id
        }
        this.showViewDialogTitle = true
      }
      if (this.type === 'inquiry') {
        params = {
          ids: [data.id],
          warehouse_id: this.params.warehouse_id
        }
      }
      if (this.type === 'select_purchase') {
        params = {
          purchase_ids: [data.id],
          warehouse_id: this.params.warehouse_id
        }
      }
      this.getDetails(params, true)
    },
    // 获取模板详情
    async getDetails(params, show) {
      if (!this.detailApi) return this.$message.error('获取详情接口不能为空！')
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[this.detailApi](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '出错了')
        return
      }
      if (res.code === 0) {
        if (show) {
          let resultData = res.data.map(item => {
            let currentSupplier = item.supplier_data.find(current => current.supplier_manage_id === item.current_supplier_manage_id)
            item.ref_unit_price = currentSupplier.specification.find(sp => sp.id === item.material_specification_id).unit_price
            item.total_price = item.ref_unit_price * item.purchase_count
            return item
          })
          this.showViewDetail(resultData)
        } else {
          this.$emit('confirmChoose', {
            type: this.type,
            data: res.data,
            select: this.selectList
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理详情数据
    showViewDetail(data) {
      // inventory_info_type 不同的页面对应不同的类型
      // purchase : "采购单"
      // entry_info : "入库单"
      // exit_info : "出库单"
      // return_info : "退货单"
      // inquiry : "询价单"

      // 采购单
      if (this.params.inventory_info_type === 'purchase') {
        this.viewStaticList = []
        if (this.type === 'template') {
          this.viewTableSettings = [
            { label: '物资名称', key: 'materials_name' },
            { label: '数量', key: 'purchase_count' },
            { label: '单位', key: 'material_specification_record' },
            { label: '参考价', key: 'ref_unit_price', type: 'money' },
            { label: '合计', key: 'total_price', type: 'money' },
            { label: '供应商', key: 'supplier_manage_name' }
          ]
          // // eslint-disable-next-line camelcase
          data.forEach(v => {
            let result = {
              ...v
            }
            // v.price_info.forEach(item => {
            //   result.supplier_manage_name = item.supplier_manage_name
            //   result.supplier_manage_id = item.supplier_manage_id
            //   result.ref_unit_price = item.ref_unit_price
            //   result.total_price = NP.times(item.ref_unit_price || 0, v.purchase_count || 0)
            //   this.viewStaticList.push(result)
            // })
            result.supplier_manage_name = v.current_supplier_manage_name
            result.supplier_manage_id = v.current_supplier_manage_id
            // result.ref_unit_price = v.current_ref_unit_price
            // result.total_price = NP.times(v.current_ref_unit_price || 0, v.purchase_count || 0)
            this.viewStaticList.push(result)
          })
        }
        if (this.type === 'inquiry') {
          this.viewTableSettings = [
            { label: '供应商', key: 'supplier_manage_name' },
            { label: '物资名称', key: 'materials_name' },
            { label: '数量', key: 'purchase' },
            { label: '单位', key: 'unit_name' },
            { label: '单价', key: 'ref_unit_price', type: 'money' },
            { label: '合计', key: 'total_price', type: 'money' }
          ]
          // // eslint-disable-next-line camelcase
          data.forEach(v => {
            let result = {
              ...v
            }
            v.price_info.forEach(item => {
              if (item.supplier_manage_id === v.supplier_manage_id) {
                result.weight = item.weight
              }
            })
            result.total_price = NP.times(v.ref_unit_price || 0, v.purchase || 0)
            this.viewStaticList.push(result)
          })
        }
        // this.viewStaticList = data
      }
      // 询价单
      if (this.params.inventory_info_type === 'inquiry') {
        this.viewTableSettings = [
          { label: '物资名称', key: 'materials_name' },
          { label: '预计采购量', key: 'purchase' },
          { label: '单位', key: 'unit_name' }
        ]
        this.viewStaticList = data
      }
      if (this.type === 'select_purchase') {
        this.viewTableSettings = [
          { label: '供应商', key: 'supplier_manage_name' },
          { label: '物资名称', key: 'materials_name' },
          { label: '数量', key: 'purchase_count' },
          { label: '单位', key: 'unit_name' }
        ]
        this.viewStaticList = data
      }
      this.showViewDialog = true
    },
    // 删除
    async deleteHandle(type, data) {
      this.$confirm('确定删除吗？', `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await this.$to(
              this.$apis.apiBackgroundDrpTemplateInfoTempDeletePost({
                id: data.id,
                inventory_info_type: this.params.inventory_info_type
              })
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.gettableDataList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 供应商change事件
    changeSupplier(e, row, index) {
      let specificationList = []
      if (e) {
        let current = row.price_info.find(v => v.supplier_manage_id === e)
        specificationList = current.specification
        // 设置下供应商名称，其它地方用得到
        this.$set(row, 'supplier_manage_name', current.supplier_manage_name)
      }
      this.$set(row, 'specification_list', specificationList)
      // 修改供应商得重置规格和单价、合计等数据
      this.$set(row, 'specs', '')
      this.$set(row, 'specs_item', null)
      this.$set(row, 'total_price', '')
    },
    // 规格change事件
    changeSpecs(e, row, index) {
      if (e) {
        let current = row.specification_list.find(v => v.id === e)
        this.$set(row, 'specs_item', current)
      } else {
        this.$set(row, 'specs_item', null)
      }
      // 修改规格得重新计算合计，不同规格对应的单价不同
      this.changeCountHandle(row.count, row, index)
    },
    // 数量修改事件
    changeCountHandle(e, row, index) {
      if (oneDecimal(e) && row.specs_item) {
        this.$set(row, 'total_price', NP.times(e, row.specs_item.unit_price))
      } else {
        this.$set(row, 'total_price', '')
      }
    },
    changeLimitUnit(e, row, index) {
      if (e) {
        let current = row.limit_unit_management.find(v => v.id === e)
        this.$set(row, 'limit_unit_item', current)
        this.$set(row, 'limit_unit_stock', current.current_num)
      } else {
        this.$set(row, 'limit_unit_item', null)
        this.$set(row, 'limit_unit_stock', '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ChooseListDialog {
  .flex{
    display: flex;
  }
  .w-220 {
    width: 220px;
  }
  .w-136 {
    width: 136px;
  }
  .w-120 {
    width: 120px;
  }
  .w-auto {
    width: auto;
  }
  .max-w-220 {
    max-width: 200px;
    margin: 0 auto;
  }
  .ps-btn-span {
    position: relative;
    color: #FF9B45;
    font-size: 12px;
    text-decoration: underline;
    // text-underline-offset: 5px; /* 设置下划线与文本基线之间的距离 */
    line-height: 1.2;
    vertical-align: middle;
    &:hover{
      color: #e58b3e;
    }
  }
  .dialogForm {
    .el-form-item {
      &.m-b-0 {
        margin-bottom: 0;
      }
      &.is-error {
        margin-bottom: 18px;
      }
    }
    .ps-picker {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      opacity: 0;
    }
    .el-table__header {
      // border-collapse: inherit;
    }
    .el-table__body {
      // width: 100% !important;
    }
  }
}
</style>
