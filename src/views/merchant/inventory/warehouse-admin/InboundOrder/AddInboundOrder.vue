<template>
  <div class="AddInboundOrder">
    <h3 class="m-t-20">仓库管理/入库单/新增入库单</h3>
    <!-- <refresh-tool :showRefresh="false" /> -->
    <div class="form-container">
      <el-form
        v-loading="isLoading"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        size="small"
        class="m-l-20 m-t-10"
        :show-message="false"
      >
        <el-form-item label="当前仓库" prop="">
          {{ warehouseName }}
          <span class="m-l-60">
            <span class="label-text">经手人：</span>
            {{ accountName }}
          </span>
        </el-form-item>
        <el-form-item label="入库类型" prop="entryType">
          <el-select v-model="formData.entryType" class="ps-select" popper-class="ps-popper-select">
            <el-option
              v-for="(option, i) in entryType"
              :key="i"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button class="ps-origin-btn" @click="addMaterials('entry_info')">添加物资</el-button>
          <el-button class="ps-origin-btn" @click="openImport">导入物资</el-button>
          <el-button v-if="formData.entryType === 'PURCHASE_ENTRY'" class="ps-origin-btn" @click="addMaterials('select_purchase')">{{ dialogSelect.length > 0 ? `选择采购单(${dialogSelect.length})` : '选择采购单' }}</el-button>
        </el-form-item>
        <div class="red inbound-tips">相同物资存在多条数据，物资名称、入库价、有效期、供应商名称如一致，将在保存时进行数据合并</div>
        <div class="red inbound-tips">相同物资，入库价和有效期必须一致，否则将无法保存入库</div>
        <el-form-item label="">
          <el-table
            :data="formData.tableData"
            ref="tableRef"
            style="width: 92%"
            stripe
            size="mini"
            border
            header-row-class-name="ps-table-header-row"
            class="inbound-order-form"
          >
            <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item" :width="item.width">
              <template #count="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.number"
                  :prop="'tableData.' + index + '.count'"
                >
                  <div class="flex">
                    <el-input v-model="row.count" placeholder="请输入" :maxlength="6" class="ps-input"></el-input>
                    <span v-if="row.specs_item" style="margin-left: 2px;">{{ row.specs_item.unit_management_name }}</span>
                  </div>
                </el-form-item>
              </template>
              <template #daterange="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.valid_date"
                  :prop="'tableData.' + index + '.valid_date'"
                >
                  <span class="inline-block ps-btn-span pointer">
                    {{ row.valid_date && row.valid_date.length > 1 ? `${row.valid_date[0]}至${row.valid_date[1]}` : '请选择' }}
                    <el-date-picker
                      v-model="row.valid_date"
                      type="daterange"
                      placeholder="选择日期"
                      class="ps-picker"
                      style="width: auto"
                      popper-class="ps-poper-picker"
                      value-format="yyyy-MM-dd"
                      :clearable="false"
                      @change="changeValidateHandle"
                    ></el-date-picker>
                  </span>
                </el-form-item>
              </template>
              <template #supplier="{ row, index }">
                <span v-if="row.select_purchase">{{ row.supplier_manage_name }}</span>
                <el-form-item
                  v-else
                  label=""
                  :prop="'tableData.' + index + '.supplier_manage_id'"
                  :rules="formRules.supplier_manage_id"
                  class="m-b-0"
                >
                  <el-select v-model="row.supplier_manage_id" filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSupplier($event, index)">
                    <el-option v-for="option in row.supplier_list" :key="option.supplier_manage_id" :label="option.supplier_manage_name" :value="option.supplier_manage_id" ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #specs="{ row, index }">
                <el-form-item
                  label=""
                  :prop="'tableData.' + index + '.specs'"
                  :rules="formRules.specs"
                  class="m-b-0"
                >
                  <el-select v-model="row.material_specification_id" :disabled="!row.supplier_manage_id" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSpecs($event, index)">
                    <el-option v-for="option in row.specification_list" :key="option.id" :label="`1${option.unit_management_name}*${option.count}${option.limit_unit_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #entryPrice="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.entry_price"
                  :prop="'tableData.' + index + '.entry_price'"
                >
                  <el-input v-model="row.entry_price" placeholder="请输入" class="ps-input" @change="changeValidateHandle"></el-input>
                </el-form-item>
              </template>
              <template #operation="{ row, index }">
                <el-button type="text" size="small" class="ps-warn" @click.stop="deleteMaterials(index, row)">
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="单据备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            class="ps-textarea w-280"
            :rows="3"
            :maxlength="200"
          ></el-input>
        </el-form-item>
        <el-form-item label="上传附件">
          <file-upload
            ref="fileUploadRef"
            :fileList="formData.fileLists"
            type="enclosure"
            prefix="inventory"
            :show-file-list="false"
            accept=".jpeg,.jpg,.png,.bmp"
            :rename="false"
            :multiple="true"
            :limit="9"
            :before-upload="beforeUpload"
            @fileLists="getFileLists"
          >
            <template v-slot="scope">
              <!-- {{ scope }} -->
              <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">
                上传{{ scope.loading ? '中' : '' }}
              </el-button>
            </template>
          </file-upload>
          <!-- <p style="color:#a5a5a5; line-height: 1.5;">附件不超过20M</p> -->
        </el-form-item>
        <el-form-item v-if="previewList.length">
          <el-collapse v-model="activeCollapse" style="max-width: 60%">
            <el-collapse-item :title="collapseTitle" name="1">
              <div class="img-item" v-for="(img, index) in previewList" :key="img + index">
                <el-image
                  :preview-src-list="previewList"
                  :initial-index="index"
                  class="upload-img m-r-6"
                  :src="img"
                  fit="contain"
                ></el-image>
                <span class="img-tools">
                  <!-- <i class="el-icon-zoom-in m-r-10"></i> -->
                  <i class="el-icon-delete" @click.stop="deleteUploadImg(index)"></i>
                </span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
        <el-form-item>
          <el-button class="ps-origin-btn" size="medium" @click="backHandle">取 消</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="submitHandle(false)">保 存</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="submitHandle(true)">保存并入库</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="openFormDialog('draft')">存为草稿</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="pickingInventory" v-if="type === 'add' || type === 'modify'">领料出入库</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 弹窗 -->
    <form-dialog
      :showdialog.sync="showFormDialog"
      :type="dialogFormType"
      :api="dialogFormApi"
      :title="dialogFormTitle"
      :inputLabel="inputLabel"
      :params="dialogFormParams"
      @confirmForm="confirmFormHandle"
    />

    <choose-list-dialog
      :showdialog.sync="showDialog"
      :title="dialogTitle"
      :type="dialogType"
      :api="dialogApi"
      :detailApi="dialogDetailApi"
      :search-setting="dialogSearchSetting"
      :table-settings="dialogTableSettings"
      :params="dialogParams"
      :defaultSelect="dialogSelect"
      :rowKey="dialogRowKey"
      showSelectLen
      @confirmChoose="confirmChooseHandle"
    ></choose-list-dialog>

    <!-- 导入 start -->
    <import-page-dialog
      ref="importPageRef"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :loading="importLoading"
      :isUpload="false"
      isDeleteFirst
      @confirm="confirmImportHandle"
    ></import-page-dialog>
    <!-- 导入 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { to, getSuffix, getSevenDateRange, times, divide, parseTime, getUrlFilename, deepClone, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import * as dayjs from 'dayjs'
import { validateNumber, validateOneDecimalCount } from '@/utils/form-validata'
import { twoDecimal } from '@/utils/validata'
import ChooseListDialog from '../../components/ChooseListDialog'
import FormDialog from '../../components/FormDialog'
import NP from 'number-precision'
import { downloadJsonExcel } from '@/utils/excel'
export default {
  name: 'AddInboundOrder',
  mixins: [exportExcel],
  components: { ChooseListDialog, FormDialog },
  data() {
    let validataPrice = (rule, value, callback) => {
      if (value !== '') {
        if (!twoDecimal(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      isLoading: false, // 刷新数据
      type: 'add',
      warehouseId: '',
      warehouseName: '',
      accountName: this.$store.getters.userInfo.member_name,
      detailData: {},
      // form表单数据
      formData: {
        entryType: '',
        remark: '',
        fileLists: [],
        tableData: []
      },
      formRules: {
        name: [{ required: true, message: '请选择', trigger: 'change' }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        valid_date: [{ validator: this.validateEntryValidDate, trigger: 'blur' }],
        entryType: [{ required: true, message: '请选择入库类型', trigger: 'change' }],
        supplier_manage_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        number: [{ validator: validateOneDecimalCount, trigger: 'change' }],
        entry_price: [{ validator: this.validateEntryPrice, trigger: 'blur' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      collapseTitle: '查看附件',
      // 入库类型
      entryType: [
        { label: '采购入库', value: 'PURCHASE_ENTRY' },
        // { label: '赠予入库', value: 'BESTOW_ENTRY' }
        {  label: '其他入库', value: 'OTHER_ENTRY' }
      ],
      // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        // { label: '当前库存', key: 'current_num' },
        { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier', width: 200 },
        { label: '规格', key: 'material_specification_id', type: 'slot', slotName: 'specs' },
        { label: '最小单位', key: 'unit_name' },
        { label: '入库数量', key: 'count', type: 'slot', slotName: 'count' },
        { label: '参考单价', key: 'ref_unit_price', type: 'money' },
        { label: '入库价', key: 'entry_price', type: 'slot', slotName: 'entryPrice' },
        { label: '有效期', key: 'valid_date', type: 'slot', slotName: 'daterange', minWidth: '80px' },
        // { label: '期限（天）', key: 'deadline', type: "slot", slotName: "day" },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      activeCollapse: [],
      showDialog: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗loading
      dialogTitle: '选择物资',
      dialogType: '', // 弹窗的状态，add/modify
      dialogData: {}, // 弹窗数据
      remoteLoading: false,
      dialogTableSettings: [],
      dialogSearchSetting: {},
      dialogParams: {
        warehouse_id: this.$route.query.warehouse_id
      },
      dialogApi: '',
      dialogDetailApi: '',
      dialogSelect: [],
      dialogRowKey: 'materials_id',
      // 导入的弹窗数据
      importLoading: false,
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/入库单-导入物资模板.xlsx',
      importHeaderLen: 0,
      importFailTableData: [], // 导入失败的数据
      importTableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '导入失败原因', key: 'result' }
      ],
      // 保存为草稿、模板、选择菜谱等的弹窗
      showFormDialog: false,
      dialogFormTitle: '选择菜谱',
      dialogFormType: '1',
      dialogFormPagetype: '', // 哪个页面的功能
      dialogFormApi: '1',
      inputLabel: '',
      dialogFormParams: {},
      validateFieldList: [] // 要手动触发的校验项
    }
  },
  computed: {
    previewList() {
      const result = this.formData.fileLists.map(v => v.url)
      this.setPreviewListTitle(result)
      return result
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.warehouseId = +this.$route.query.warehouse_id
      this.warehouseName = this.$route.query.warehouse_name
      this.type = this.$route.params.type
      if (this.$route.query.type === 'recovery') {
        this.getdraftDetail(+this.$route.query.id)
      }
      if (this.type === 'modify') {
        this.getInboundOrderDetail()
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取详情数据
    async getInboundOrderDetail() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: +this.$route.query.id,
        warehouse_id: this.warehouseId
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.detailData = res.data
        // this.tableData = res.data.results
        this.formData.entryType = res.data.inventory_entry_type
        this.formData.remark = res.data.remark
        if (res.data.image_json && res.data.image_json.length > 0) {
          let fileImages = []
          res.data.image_json.forEach((v, index) => {
            fileImages.push({
              url: v,
              name: getUrlFilename(v),
              status: "success",
              uid: new Date().getTime() + index
            })
          })
          this.formData.fileLists = fileImages
        } else {
          this.formData.fileLists = []
        }
        if (res.data.entry_data) {
          this.formData.tableData = res.data.entry_data.map(v => {
            if (v.start_valid_date || v.end_valid_date) {
              v.valid_date = [v.start_valid_date, v.end_valid_date]
            } else {
              v.valid_date = []
            }
            v.count = v.expected_entry_count
            v.entry_price = v.entry_price / 100
            v.supplier_list = v.price_info
            const priceInfo = v.price_info || []
            const findItem = priceInfo.find(item => {
              return item.supplier_manage_id === v.supplier_manage_id
            })
            console.log("findItem", findItem)
            if (findItem) {
              v.specification_list = findItem.specification
              v.specs_item = findItem.specification.find(item => {
                return item.id === v.material_specification_id
              })
            }
            v.name = v.materials_name
            return deepClone(v)
          })
        }
        console.log("this.formData.tableData", this.formData.tableData)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 草稿箱详情
    async getdraftDetail(id) {
      if (!id) return this.$message.error('获取id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id,
        inventory_info_type: 'entry_info'
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoDraftDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // let result = res.data.extra
        this.detailData = res.data.extra
        // this.tableData = res.data.results
        this.formData.entryType = res.data.extra.inventory_entry_type
        this.formData.remark = res.data.extra.remark
        if (res.data.extra.image_json && res.data.extra.image_json.length > 0) {
          let fileImages = []
          res.data.extra.image_json.forEach((v, index) => {
            fileImages.push({
              url: v,
              name: getUrlFilename(v),
              status: "success",
              uid: new Date().getTime() + index
            })
          })
          this.formData.fileLists = fileImages
        } else {
          this.formData.fileLists = []
        }
        if (res.data.extra.entry_data) {
          this.formData.tableData = res.data.extra.entry_data.map(v => {
            if (v.start_valid_date && v.end_valid_date) {
              v.valid_date = [v.start_valid_date, v.end_valid_date]
            } else {
              v.valid_date = []
            }
            v.entry_price = v.entry_price / 100
            v.supplier_list = v.price_info
            v.specification_list = v.price_info.find(item => item.supplier_manage_id === v.supplier_manage_id).specification
            v.specs_item = v.specification_list.find(item => item.id === v.material_specification_id)
            return deepClone(v)
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 选择供应商
    changeSupplier(e, currentIndex) {
      let current = this.formData.tableData[currentIndex]
      let currentSupplier = current.supplier_list.find(v => v.supplier_manage_id === e)
      // 选择不同供应商的物资单价是不同的，需要对应切换
      // this.$set(current, 'ref_unit_price', currentSupplier.ref_unit_price)
      this.$set(current, 'supplier_manage_name', currentSupplier.supplier_manage_name)
      // 设置下规格列表数据
      this.$set(current, 'specification_list', currentSupplier.specification || [])
      // 修改供应商得重置规格和单价等数据
      this.$set(current, 'specs_item', '')
      this.$set(current, 'material_specification_id', '')
      this.$set(current, 'unit_name', '')
      this.$set(current, 'ref_unit_price', '')
      // 合并相同物资和相同供应商的数据
      this.formData.tableData = this.uniqueMaterials(this.formData.tableData, true)
    },
    // 修改规格
    changeSpecs(e, currentIndex) {
      let current = this.formData.tableData[currentIndex]
      let currentSpecs = current.specification_list.find(v => v.id === e)
      this.$set(current, 'specs_item', currentSpecs)
      this.$set(current, 'unit_name', `${currentSpecs.limit_unit_name}*${currentSpecs.net_content}${currentSpecs.net_content_unit}`) // 最小单位：例：瓶*330ml
      this.$set(current, 'ref_unit_price', currentSpecs.unit_price)
      // // 合并相同物资和相同供应商的数据
      this.formData.tableData = this.uniqueMaterials(this.formData.tableData, true)
    },
    // 添加物资
    addMaterials(type, data) {
      this.dialogType = type
      if (data) {
        this.dialogData = data
      }
      // 选择采购单
      if (type === 'select_purchase') {
        this.dialogTitle = '选择采购单'
        // this.dialogApi = 'apiBackgroundDrpPurchaseInfoListPost'
        this.dialogApi = 'apiBackgroundDrpPurchaseInfoGetConfirmedPurchaseInfoPost'
        this.dialogDetailApi = 'apiBackgroundDrpPurchaseInfoPurchaseInfoGetMaterialsPost'
        // this.dialogChooseParams = {
        //   inventory_info_type: 'purchase',
        //   warehouse_id: +this.$route.query.warehouse_id
        // }
        this.dialogRowKey = 'id'
        this.dialogSearchSetting = {
          date_type: {
            type: 'select',
            label: '',
            value: 'create_time',
            maxWidth: '100px',
            placeholder: '请选择',
            dataList: [
              {
                label: '创建时间',
                value: 'create_time'
              }
              // {
              //   label: '入库时间',
              //   value: 'entry_time'
              // }
            ]
          },
          select_time: {
            type: 'daterange',
            format: 'yyyy-MM-dd',
            label: '',
            clearable: true,
            value: getSevenDateRange(7)
          }
        }
        this.dialogTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '单据编号', key: 'trade_no' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
        ]
      }
      // 添加物资
      if (type === 'entry_info') {
        this.dialogTitle = '添加物资'
        this.dialogApi = 'apiBackgroundDrpMaterialsGetMaterialsListPost'
        this.dialogSearchSetting = {
          name: {
            type: 'input',
            value: '',
            label: '物资名称',
            placeholder: '请输入'
          }
        }
        this.dialogTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          // { label: '当前库存', key: 'current_num' },
          { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier', width: 180 },
          { label: '规格', key: 'specs', type: 'slot', slotName: 'specs' },
          { label: '单价', key: 'unit_price', type: 'slot', slotName: 'unitPrice' },
          { label: '入库数量', key: 'count', type: 'slot', slotName: 'oneDecimalCount' },
          { label: '有效期', key: 'date', type: 'slot', slotName: 'date' }
        ]
        this.dialogRowKey = 'materials_id'
      }
      this.showDialog = true
    },
    // 弹窗确定事件
    confirmChooseHandle(res) {
      this.showDialog = false
      console.log(11231, res)
      let result = []
      // 选择采购单确定
      if (res.type === 'select_purchase') {
        // 选择采购单需要反选
        this.dialogSelect = res.select.map(v => v[this.dialogRowKey])
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            materials_id: v.materials_id,
            current_num: v.current_num,
            count: v.purchase_count,
            unit_name: v.unit_name,
            // unit_id: v.unit_id,
            // ref_unit_price: v.specs_item.unit_price, // 参考价
            entry_price: '',
            supplier_manage_id: v.supplier_manage_id,
            supplier_manage_name: v.supplier_manage_name,
            supplier_list: v.price_info,
            valid_date: [],
            select_purchase: true, // 用于区分是采购单还是添加物资的，如果合并有重复时以添加物资为主
            //  加了规格以后新增的
            material_specification_id: v.specification_id,
            specs_item: null,
            specification_list: []
          }
          let currentSupplier = v.price_info.find(current => current.supplier_manage_id === v.supplier_manage_id)
          if (currentSupplier) {
            // item.ref_unit_price = currentSupplier.ref_unit_price || 0
            // item.entry_price = currentSupplier.ref_unit_price > 0 ? currentSupplier.ref_unit_price/100 : ''
            item.supplier_manage_name = currentSupplier.supplier_manage_name
            item.specification_list = currentSupplier.specification // 供应商的规格列表
          }
          // 为了反显规格，需要拿到规格的具体数据
          if (item.material_specification_id && item.specification_list) {
            item.specs_item = item.specification_list.find(sp => sp.id === v.specification_id)
            item.ref_unit_price = item.specs_item.unit_price // 参考价
          }
          console.log(item, 'item.material_specification_id')
          result.push(item)
        })
      }

      if (res.type === 'entry_info') {
        res.data.forEach(v => {
          console.log("949494vvv", v)
          let item = {
            materials_name: v.materials_name,
            materials_id: v.materials_id,
            current_num: v.current_num,
            count: v.count,
            unit_name: `${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`, // 最小单位：例：瓶*330ml
            unit_id: v.unit_id,
            ref_unit_price: v.specs_item.unit_price, // 参考价
            entry_price: '',
            supplier_manage_id: v.supplier,
            supplier_manage_name: v.supplier_manage_name,
            supplier_list: v.price_info,
            valid_date: v.date,
            //  加了规格以后新增的
            material_specification_id: v.specs,
            specs_item: v.specs_item,
            specification_list: v.specification_list
          }
          // let currentSupplier = null
          // // 选择物资时如果物资绑定多个供应商的时候，价格取供应商中最低的
          // v.price_info.forEach(supplier => {
          //   if (currentSupplier) {
          //     if (supplier.ref_unit_price < currentSupplier.ref_unit_price) {
          //       currentSupplier = supplier
          //     }
          //   } else {
          //     currentSupplier = supplier
          //   }
          // })
          // if (currentSupplier) {
          //   item.supplier_manage_id = currentSupplier.supplier_manage_id
          //   item.supplier_manage_name = currentSupplier.supplier_manage_name
          //   item.ref_unit_price = currentSupplier.ref_unit_price || 0
          // }
          result.push(item)
        })
      }
      if (this.formData.tableData.length > 0) {
        this.formData.tableData = this.mergeArrays(this.formData.tableData, result)
      } else {
        this.formData.tableData = result
      }
    },
    // 合并新旧数据，以同一供应商、相同物资、规格、相同入库价、相同有效期的数据为前提合并，数量这些需要累加起来
    mergeArrays(tableData, newData) {
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        let key = `${current.materials_id}_${current.supplier_manage_id}_${current.material_specification_id}_${current.entry_price}_${JSON.stringify(current.valid_date)}`
        // 当存在则证明数据重复，需合并
        if (merged[key]) {
          // 合并入库数量
          merged[key].count = NP.plus(merged[key].count || 0, current.count || 0)
        } else {
          merged[key] = current
        }
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = `${item.materials_id}_${item.supplier_manage_id}_${item.material_specification_id}_${item.entry_price}_${JSON.stringify(item.valid_date)}`
        if (merged[key]) {
          // 合并默认以添加物资为主，当采购单和添加物资的物资重复时
          if (merged[key].select_purchase) {
            merged[key].select_purchase = false
          }
          merged[key].count = NP.plus(merged[key].count, item.count)
          // merged[key] = Object.assign({}, merged[key], item)
          // 。。。
          // if (merged[key].valid_date.length === 0) {
          //   merged[key].valid_date = item.valid_date
          // }
        } else {
        // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    // 删除物资
    deleteMaterials(index) {
      this.formData.tableData.splice(index, 1)
      // 手动触发下校验
      this.validateFieldList = []
      this.formData.tableData.forEach((v, index) => {
        this.validateFieldList.push(`tableData.${index}.entry_price`, `tableData.${index}.valid_date`)
      })
      this.changeValidateHandle()
    },
    // 导入弹窗
    openImport(type) {
      if (!this.$route.query.warehouse_id) {
        return this.$message.error('获取仓库数据失败！')
      }
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    // 导入确定事件
    confirmImportHandle(data) {
      let importData = data
      // 删除示例数据
      // importData.splice(1, 1)
      console.log(111, importData)
      // importResult
      if (importData.length > 1) {
        // this.importLoading = true
        // 导入数据对应后端需要的字段
        const purchaseNameObject = {
          '物资名称': 'materials_name',
          '供应商': 'supplier_manage_name',
          '规格': 'spec_data',
          '入库数量': 'entry_count',
          '入库价': 'entry_price',
          '有效期（开始时间）': 'start_valid_date',
          '有效期（到期时间）': 'end_valid_date'
        }
        // 根据purchaseNameObject转换的index key
        let resultKey = {}
        importData[0].forEach((v, index) => {
          resultKey[index] = purchaseNameObject[v]
        })
        let result = []
        importData.forEach((item, index) => {
          if (index > 0) {
            let current = {}
            item.forEach((v, k) => {
              console.log(resultKey, 'resultKey[k]')
              if (resultKey[k].indexOf('valid_date') > -1) {
                current[resultKey[k]] = parseTime(v, '{y}-{m}-{d}')
              } else if (resultKey[k] === 'entry_price') { // 金额要转分
                current[resultKey[k]] = times(v)
              } else {
                current[resultKey[k]] = v
              }
            })
            result.push(current)
          }
        })
        console.log(222, result)
        this.sendImportMaterials(result)
      } else {
        this.$message.error('导入物资为空！')
      }
    },
    // 发送导入物资数据给后端校验
    async sendImportMaterials(data) {
      if (this.importLoading) return
      this.importLoading = true
      let params = {
        warehouse_id: this.$route.query.warehouse_id,
        data: data
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoImportMaterialsPost(params))
      // this.isLoading = false
      this.importLoading = false
      this.$refs.importPageRef.reset()
      this.showImportDialog = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.success && res.data.success.length > 0) {
          let result = []
          res.data.success.forEach(v => {
            let item = {
              materials_name: v.materials_name,
              materials_id: v.materials_id,
              current_num: v.current_num,
              count: v.entry_count,
              unit_name: v.unit_name,
              // unit_id: v.unit_id,
              ref_unit_price: '', // 参考价
              entry_price: v.entry_price > 0 ? v.entry_price / 100 : '',
              supplier_manage_id: v.supplier_id,
              supplier_manage_name: v.supplier_name,
              supplier_list: v.price_info,
              valid_date: v.start_valid_date && v.end_valid_date ? [v.start_valid_date, v.end_valid_date] : [],
              //  加了规格以后新增的
              material_specification_id: v.specification_id,
              specs_item: null,
              specification_list: []
            }
            let currentSupplier = v.price_info.find(current => current.supplier_manage_id === v.supplier_id)
            if (currentSupplier) {
              item.supplier_manage_id = currentSupplier.supplier_manage_id
              item.supplier_manage_name = currentSupplier.supplier_manage_name
              item.specification_list = currentSupplier.specification // 供应商的规格列表
            }
            // 为了反显规格，需要拿到规格的具体数据
            if (item.material_specification_id && item.specification_list) {
              item.specs_item = item.specification_list.find(sp => sp.id === v.specification_id)
              if (item.specs_item) item.ref_unit_price = item.specs_item.unit_price // 参考价
              if (item.specs_item) item.unit_name = `${item.specs_item.limit_unit_name}*${item.specs_item.net_content}${item.specs_item.net_content_unit}`
            } else {
              item.material_specification_id = ""
            }
            result.push(item)
          })
          // 去重下
          result = this.uniqueMaterials(result)
          if (this.formData.tableData.length > 0) {
            this.formData.tableData = this.mergeArrays(this.formData.tableData, result)
          } else {
            this.formData.tableData = result
          }
        }
        if (res.data.failure && res.data.failure.length > 0) {
          let resultData = res.data.failure.map(item => {
            item.entry_price = divide(item.entry_price)
            return item
          })
          this.formatImportFailureResult(resultData)
          this.$message.error('部分物资导入失败，请查看excel!')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 物资去重，根据供应商、物资、规格（新的）、入库价、有效期，都相同的则合并，数量累加
    uniqueMaterials(data) {
      const arr = deepClone(data)
      const newArray = []
      const tmp = {}
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        const key = `${item.materials_id}_${item.supplier_manage_id}_${item.material_specification_id}_${item.entry_price}_${JSON.stringify(item.valid_date)}`
        if (!tmp[key]) {
          tmp[key] = item
        } else { // 数量累加
          tmp[key].count = NP.plus(tmp[key].count, item.count)
        }
      }
      return Object.values(tmp)
    },
    // 当列表已存在相同供应商的物资时，弹窗显示是否合并
    mergeSupplierMaterials() {
      this.$confirm(`同一供应商、相同物资、相同入库价、相同有效期的数据已合并`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        showCancelButton: false,
        center: true,
        beforeClose: (action, instance, done) => {
          done()
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 格式化导入失败的数据，通过xlsx方式下载显示
    formatImportFailureResult(result) {
      const purchaseNameObject = {
        'materials_name': 0,
        'supplier_manage_name': 1,
        'spec_data': 2,
        'entry_count': 3,
        'entry_price': 4,
        'start_valid_date': 5,
        'end_valid_date': 6,
        'result': 7
      }
      let failureJson = [
        ['物资名称', '供应商', '规格', '入库数量', '入库价（元）', '有效期（开始时间）', '有效期（到期时间）', '导入结果']
      ]
      let json = result.map(v => {
        let current = []
        Object.keys(purchaseNameObject).forEach(k => {
          current[purchaseNameObject[k]] = v[k]
        })
        return current
      })
      failureJson = failureJson.concat(json)
      // 下载数据
      downloadJsonExcel(failureJson)
    },
    getFileLists(fileLists) {
      this.formData.fileLists = fileLists
    },
    // 上传图片前钩子
    beforeUpload(file) {
      let uploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      if (!uploadType.includes(getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 <= 2
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 2M')
      }
      return isLt20M
    },
    //
    setPreviewListTitle(result) {
      this.collapseTitle = '查看附件(' + result.length + ')'
    },
    // 删除图片
    deleteUploadImg(index) {
      const fileUploadRef = this.$refs.fileUploadRef
      if (this.formData.fileLists[index]) {
        fileUploadRef && fileUploadRef.spliceFileData(this.formData.fileLists[index].uid)
      }
      this.formData.fileLists.splice(index, 1)
    },
    // 格式化数据
    formatParams(isWarehousing) {
      let params = {
        inventory_entry_type: this.formData.entryType,
        warehouse_id: this.warehouseId,
        remark: this.formData.remark,
        is_warehousing: isWarehousing
      }
      params.entry_data = this.uniqueMaterials(this.formData.tableData).map(v => {
        return {
          materials_id: v.materials_id,
          name: v.name,
          count: v.count*v.specs_item.count, // 要按照最小单位库存传值
          unit_id: v.unit_id,
          unit_name: v.unit_name,
          entry_price: Number((times(v.entry_price)/v.specs_item.count).toFixed(0)), // 入库价要按照最小单位库存传值
          ref_unit_price: Number((v.ref_unit_price/v.specs_item.count).toFixed(0)), // 参考单价要按照最小单位库存传值
          start_valid_date: v.valid_date[0],
          end_valid_date: v.valid_date[1],
          supplier_manage_id: v.supplier_manage_id,
          material_specification_id: v.material_specification_id,
          material_specification_record: `1${v.specs_item.unit_management_name}*${v.specs_item.count}${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`,
          limit_count_record: `${v.count*v.specs_item.count}${v.specs_item.limit_unit_name}`,
          limit_unit_id: v.specs_item.limit_unit_id
        }
      })
      params.image_json = this.previewList
      return params
    },
    // 确定
    submitHandle(is_warehousing = false) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (!this.formData.tableData.length) return this.$message.error('请先选择物资！')
          let api = 'apiBackgroundDrpEntryInfoAddPost'
          let params = this.formatParams(is_warehousing)
          if (this.type === 'modify') {
            // api = 'apiBackgroundDrpEntryInfoModifyPost'
            api = 'apiBackgroundDrpEntryInfoReModifyPost'
            params.id = +this.$route.query.id
            params.old_id = +this.$route.query.id
            this.showConfirmDialog(api, params)
          } else {
            api = 'apiBackgroundDrpEntryInfoAddPost'
            this.showConfirmDialog(api, params)
          }
        } else {
          this.$message.error('请检查表单信息!')
        }
      })
    },
    showConfirmDialog(api, params) {
      this.$confirm(`同一供应商、相同物资、相同入库价、相同有效期的数据将会合并`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          done()
          if (action === 'confirm') {
            this.sendDataHandle(api, params)
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发送
    async sendDataHandle(api, params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis[api](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'InboundOrder')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开存为草稿弹窗
    openFormDialog(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (!this.formData.tableData.length) return this.$message.error('请先选择物资！')
          this.dialogFormType = type
          switch (type) {
            case 'draft':
              this.dialogFormTitle = '存为草稿'
              this.inputLabel = '草稿名称'
              this.dialogFormApi = 'apiBackgroundDrpEntryInfoTempAddPost'
              this.dialogFormParams = this.setDialogFormParams(type)
              break;
          }
          this.showFormDialog = true
        } else {
          this.$message.error('请认真检查表单数据！')
        }
      })
    },
    // 设置存为草稿或者存为模板的数据
    setDialogFormParams(type) {
      // inventory_info_type
      // purchase : "采购单"
      // entry_info : "入库单"
      // exit_info : "出库单"
      // return_info : "退货单"
      // inquiry : "询价单"
      let params = {
        inventory_info_type: 'entry_info',
        warehouse_id: +this.warehouseId,
        temp_type: type,
        remark: this.formData.remark,
        inventory_entry_type: this.formData.entryType,
        image_json: this.previewList,
        entry_data: this.uniqueMaterials(this.formData.tableData).map(v => {
          v.limit_unit_id = v.specs_item.limit_unit_id
          v.start_valid_date = v.valid_date[0]
          v.end_valid_date = v.valid_date[1]
          v.entry_price = times(v.entry_price)
          return v
        })
      }
      return params
    },
    // form弹窗
    confirmFormHandle(e) {
      // 存为草稿需要返回上一页哦
      if (e && e.type === 'draft') {
        this.$backVisitedViewsPath(this.$route.path, 'InboundOrder')
      }
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'InboundOrder')
    },
    // 校验输入入库价，当相同存在相同物质相同供应商相同规格时入库价不同的不允许保存，规避其它地方自动管理入库单重复的问题
    validateEntryPrice(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        if (value) {
          if (!twoDecimal(value)) {
            callback(new Error('格式错误'))
          } else {
            // callback()
            // 是否相同
            let isDifferent = true
            let fieldList = [] // 定义个变量存下需要手动校验的prop
            // 校验通过后再做是否又相同物质和供应商的校验
            for (let index = 0; index < this.formData.tableData.length; index++) {
              const item = this.formData.tableData[index];
              // 当存在物质id和供应商id、规格id相同时，判断入库价是否相同，不同则抛出错误
              if ((row.materials_id === item.materials_id) && (row.supplier_manage_id === item.supplier_manage_id) && (row.material_specification_id === item.material_specification_id) && item.entry_price) {
                // 存下需要校验的字段prop，用于后面手动触发相同物质和供应商的数据校验
                fieldList.push(`tableData.${index}.entry_price`)
                if (Number(value) !== Number(item.entry_price)) {
                  // callback(new Error('入库价不一致'))
                  // this.validateFieldList.push(`tableData.${index}.entry_price`)
                  isDifferent = false
                  // 已存在相关手动的校验规则直接跳出循环
                  if (this.validateFieldList.length > 0) {
                    break;
                  }
                }
              }
            }
            // 当不存在需要手动触发的校验prop才添加
            if (this.validateFieldList.length === 0) {
              this.validateFieldList = fieldList
            }
            if (isDifferent) {
              callback()
            } else {
              callback(new Error('入库价不一致'))
            }
          }
        } else {
          callback(new Error('请输入'))
        }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 校验有效期
    validateEntryValidDate(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        let isDifferent = true
        let fieldList = [] // 定义个变量存下需要手动校验的prop
        // 校验通过后再做是否又相同物质和供应商的校验
        for (let index = 0; index < this.formData.tableData.length; index++) {
          const item = this.formData.tableData[index];
          // 当存在物质id和供应商id相同时，判断有效期是否相同，不同则抛出错误
          if ((row.materials_id === item.materials_id) && (row.supplier_manage_id === item.supplier_manage_id)) {
            // 存下需要校验的字段prop，用于后面手动触发相同物质和供应商的数据校验
            fieldList.push(`tableData.${index}.valid_date`)
            if (JSON.stringify(value) !== JSON.stringify(item.valid_date)) {
              isDifferent = false
              // 已存在相关手动的校验规则直接跳出循环
              if (this.validateFieldList.length > 0) {
                break;
              }
            }
          }
        }
        // 当不存在需要手动触发的校验prop才添加
        if (this.validateFieldList.length === 0) {
          this.validateFieldList = fieldList
        }
        // 返回当前校验的结果
        if (isDifferent) {
          callback()
        } else {
          callback(new Error('有效期不一致'))
        }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 数据修改时校验下其它相同物质相同供应商的数据
    changeValidateHandle: debounce(function(e) {
      if (this.validateFieldList.length > 0) {
        const formRef = this.$refs.formRef
        let validateLen = this.validateFieldList.length
        formRef.validateField(this.validateFieldList.slice(0), (e) => {
          validateLen--
          // 重置，很重要
          if (validateLen === 0) {
            this.validateFieldList = []
          }
        })
      }
    }, 200),
    // 领料出入库 服了产品他们搞得这么复杂
    pickingInventory() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (!this.formData.tableData.length) {
            return this.$message.error('请先选择物资！')
          }
          this.$confirm("领料出入库将对物资进行入库和出库，确认进行出入库？", '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            center: true,
            customClass: 'myPickerDialog',
            beforeClose: async (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                // 先调用入库
                let inFlag = await this.setInBoundOrder()
                if (inFlag) {
                  // 入库成功先查关联单据
                  let getMaterialFlag = await this.getAutoMaterialsTrade()
                  if (getMaterialFlag) {
                    // 关联单据成功并设置好以后就出库拉
                    let outBoundFlag = await this.setOutboundOrder()
                    instance.confirmButtonLoading = false
                    if (outBoundFlag) {
                      this.$backVisitedViewsPath(this.$route.path, 'DocumentManagement')
                    }
                    console.log("outBoundFlag", outBoundFlag)
                    done()
                  } else {
                    instance.confirmButtonLoading = false
                    done()
                  }
                } else {
                  instance.confirmButtonLoading = false
                  done()
                }
              }
              done()
            }
          }).then(() => {
          }).catch(() => {
          })
        }
      })
    },
    // 格式化数据
    formatParamsOutbound() {
      console.log("formatParamsOutbound", this.formData.tableData)
      let params = {
        inventory_exit_type: "RECEIVE_EXIT",
        warehouse_id: this.warehouseId,
        exit_time: parseTime(new Date(), '{y}-{m}-{d} {h}:{i}'),
        remark: this.formData.remark
      }
      if (this.$route.query.type === 'to_exit_info') {
        params.sg_trade_no = this.$route.query.sg_trade_no
      }
      params.exit_data = this.formData.tableData.map(v => {
        return {
          materials_id: v.materials_id,
          name: v.name,
          count: v.count * v.specs_item.count,
          unit_id: v.unit_id,
          // 这个啥？
          current_num: v.current_num,
          limit_unit_id: v.specs_item.limit_unit_id,
          unit_name: v.unit_name,
          entry_fee: Number((times(v.entry_price)/v.specs_item.count).toFixed(0)),
          contact_trade_list: v.contact_trade_list && v.contact_trade_list.map(v => {
            return {
              trade_no: v.trade_no,
              count: v.use_count,
              supplier_manage_id: v.supplier_manage_id
            }
          }),
          key: v.key
        }
      })
      params.image_json = this.previewList
      return params
    },
    // 设置入库
    setInBoundOrder() {
      return new Promise((resolve) => {
        let params = this.formatParams(true)
        params.skip_approve = true
        this.$apis.apiBackgroundDrpEntryInfoAddPost(params).then(res => {
          if (res && res.code === 0) {
            resolve(true)
          } else {
            this.$message.error(res.msg || '入库失败！')
            resolve(false)
          }
        }).catch(error => {
          console.log("error", error.msg);
          this.$message.error(error.msg || '入库失败！')
          resolve(false)
        })
      })
    },
    // 获取自动关联的入库单数据
    getAutoMaterialsTrade() {
      return new Promise((resolve) => {
        let inParams = {
          is_new: true,
          warehouse_id: this.warehouseId,
          materials_data: this.formData.tableData.map(v => {
            return {
              materials_id: v.materials_id,
              count: v.count * v.specs_item.count,
              limit_unit_id: v.specs_item.limit_unit_id
            }
          })
        }
        this.$apis.apiBackgroundDrpEntryInfoAutoGetMaterialsTradePost(inParams).then(res => {
          if (res && res.code === 0) {
            this.setAutoMaterialsTrade(this.formData.tableData, res.data)
            resolve(true)
          } else {
            this.$message.error(res.msg || '关联单据失败！')
            resolve(false)
          }
        }).catch(error => {
          this.$message.error(error.msg || '关联单据失败！')
          resolve(false)
        })
      })
    },
    // 出库
    setOutboundOrder() {
      return new Promise((resolve) => {
        let outParams = this.formatParamsOutbound()
        outParams.skip_approve = true
        this.$apis.apiBackgroundDrpExitInfoAddPost(outParams).then(res => {
          if (res && res.code === 0) {
            this.$message.success('出入库成功！')
            resolve(true)
          } else {
            this.$message.error(res.msg || '入库成功，出库失败！')
            resolve(false)
          }
        }).catch(error => {
          this.$message.error(error.msg || '入库成功，出库失败！')
          resolve(false)
        })
      })
    },
    // 设置自动关联的入库单数据
    setAutoMaterialsTrade(materialsList, autoMaterials) {
      if (!autoMaterials || autoMaterials.length === 0) {
        return
      }
      // 先给后台返回的关联单号生成唯一的id 每条数据的唯一标识，不同物资、供应商、单位、入库价，分为不同数据
      autoMaterials = autoMaterials.map(item => {
        item.key = `${item.materials_id}_${item.supplier_manage_id}_${item.limit_unit_id}_${item.entry_price}`
        return item
      })
      // 给列表每个项，加上匹配的关联单据
      if (materialsList && materialsList.length > 0) {
        materialsList = materialsList.map(item => {
          item.key = `${item.materials_id}_${item.supplier_manage_id}_${item.specs_item.limit_unit_id}_${Number((times(item.entry_price) / item.specs_item.count).toFixed(0))}`
          console.log("item.key", item.key);
          item.contact_trade_list = autoMaterials.filter(subItem => {
            return subItem.key === item.key
          })
          return item
        })
      }
      this.formData.tableData = deepClone(materialsList)
      console.log(this.formData.tableData, 'this.formData.tableData')
    }
  }
}
</script>

<style lang="scss" scope>
.AddInboundOrder {
  .flex{
    display: flex;
  }
  .form-container {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    .inbound-tips {
      margin-left: 100px;
      margin-bottom: 4px;
      font-size: 13px;
    }
    .ps-btn-span {
      position: relative;
      color: #FF9B45;
      font-size: 12px;
      text-decoration: underline;
      // text-underline-offset: 5px; /* 设置下划线与文本基线之间的距离 */
      line-height: 1.2;
      vertical-align: middle;
      &:hover{
        color: #e58b3e;
      }
      .ps-picker {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        opacity: 0;
      }
    }
    .upload-img {
      width: 90px;
      height: 90px;
    }
    .img-item {
      display: inline-block;
      position: relative;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      .img-tools {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ffffff;
        font-size: 20px;
        transition: 0.3s;
        i {
          cursor: pointer;
          color: #ff9b45;
        }
      }
      &:hover {
        .img-tools {
          display: inline-block;
        }
      }
    }
  }
  .label-text {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
  }
  .w-220 {
    width: 220px;
  }
  .w-280 {
    width: 280px;
  }
  .w-auto {
    width: 300px;
  }
  .m-b-0 {
    margin-bottom: 0;
    &.is-error {
      // margin-bottom: 20px;
      .ps-btn-span {
        color: #F56C6C;
      }
    }
  }
  .myPickerDialog {
    width: 420px;
  }
}
</style>
