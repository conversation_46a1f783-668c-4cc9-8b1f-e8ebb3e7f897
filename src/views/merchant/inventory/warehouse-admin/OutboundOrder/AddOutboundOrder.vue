<template>
  <div class="AddOutboundOrder">
    <h3 class="m-t-20">仓库管理/出库单/新增出库单</h3>
    <!-- <refresh-tool :showRefresh="false" /> -->
    <div class="form-container">
      <el-form
        v-loading="isLoading"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        size="small"
        class="m-l-20 m-t-10"
      >
        <el-form-item label="当前仓库" prop="">
          {{ warehouseName }}
          <span class="m-l-60 m-r-30">
            <span class="label-text">经手人：</span>
            {{ accountName }}
          </span>
          <el-form-item label="领用人" class="inline-block m-b-0" prop="recipient">
            <el-input v-model="formData.recipient" placeholder="请输入" class="ps-input"></el-input>
          </el-form-item>
        </el-form-item>
        <div class="">
          <el-form-item label="出库类型" prop="inventoryExitType" required class="inline-block">
            <el-select v-model="formData.inventoryExitType" class="ps-select" popper-class="ps-popper-select">
              <el-option
                v-for="(option, i) in inventoryExitTypeList"
                :key="i"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出库时间" prop="exitTime" class="inline-block">
            <el-date-picker
              v-model="formData.exitTime"
              type="datetime"
              placeholder="选择日期"
              class="ps-picker"
              style="width: auto"
              popper-class="ps-poper-picker"
              value-format="yyyy-MM-dd HH:mm"
              format="yyyy-MM-dd HH:mm"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="autoContactTrade" class="inline-block" label-width="0">
            <el-checkbox v-model="formData.autoContactTrade" class="ps-checkbox m-l-30 m-r-6">自动关联入库单据</el-checkbox>
          </el-form-item>
        </div>
        <el-form-item label="" prop="">
          <el-button class="ps-origin-btn" @click="addMaterials('exit_info')">添加物资</el-button>
          <el-button class="ps-origin-btn" @click="openImport">导入物资</el-button>
          <!-- <el-button class="ps-origin-btn" @click="addMaterials('purchase')">选择采购单</el-button> -->
        </el-form-item>
        <el-form-item label="">
          <el-table
            :data="formData.tableData"
            ref="tableRef"
            style="width: 76%"
            stripe
            size="small"
            border
            max-height="600"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item">
              <template #unitName="{ row }">
                <span>
                  {{ row.limit_unit_item.unit_management_name }}*
                  {{row.limit_unit_item.net_content}}{{row.limit_unit_item.net_content_unit}}
                </span>
              </template>
              <template #count="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.number"
                  :prop="'tableData.' + index + '.count'"
                >
                  <el-input v-model="row.count" placeholder="请输入" :maxlength="6" class="ps-input" @input="changeCountHandle($event, row, index)"></el-input>
                </el-form-item>
              </template>
              <template #entryPrice="{ row }">
                <span>{{row.entry_price | formatMoney}}</span>
              </template>
              <template #totalPrice="{ row }">
                <span>{{(row.entry_price * row.count) | formatMoney}}</span>
              </template>
              <!-- 关联采购单 -->
              <template #associated="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.associated"
                  :prop="'tableData[' + index + '].contact_trade_list'"
                >
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="addMaterials('associated_purchase', row, index)"
                  >
                    {{ row.contact_trade_list && row.contact_trade_list.length > 0 ? `已关联${row.contact_trade_list.length}` : '选择' }}
                  </el-button>
                </el-form-item>
              </template>
              <template #operation="{ row, index }">
                <el-button type="text" size="small" class="ps-warn" @click.stop="deleteMaterials(index, row)">
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="单据备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            class="ps-textarea w-280"
            :rows="3"
            :maxlength="200"
          ></el-input>
        </el-form-item>
        <el-form-item label="上传附件">
          <file-upload
            ref="fileUploadRef"
            :fileList="formData.fileLists"
            type="enclosure"
            prefix="inventory"
            :show-file-list="false"
            accept=".jpeg,.jpg,.png,.bmp"
            :rename="false"
            :multiple="true"
            :limit="9"
            :before-upload="beforeUpload"
            @fileLists="getFileLists"
          >
            <template v-slot="scope">
              <!-- {{ scope }} -->
              <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">
                上传{{ scope.loading ? '中' : '' }}
              </el-button>
            </template>
          </file-upload>
          <!-- <p style="color:#a5a5a5; line-height: 1.5;">附件不超过20M</p> -->
        </el-form-item>
        <el-form-item v-if="previewList.length">
          <el-collapse v-model="activeCollapse" style="max-width: 60%">
            <el-collapse-item :title="collapseTitle" name="1">
              <div class="img-item" v-for="(img, index) in previewList" :key="img + index">
                <el-image
                  :preview-src-list="previewList"
                  :initial-index="index"
                  class="upload-img m-r-6"
                  :src="img"
                  fit="contain"
                ></el-image>
                <span class="img-tools">
                  <!-- <i class="el-icon-zoom-in m-r-10"></i> -->
                  <i class="el-icon-delete" @click.stop="deleteUploadImg(index)"></i>
                </span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
        <el-form-item>
          <el-button class="ps-origin-btn" size="medium" @click="backHandle">取 消</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="submitHandle">保 存</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="openFormDialog('draft')">存为草稿</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 弹窗 -->
    <form-dialog
      :showdialog.sync="showFormDialog"
      :type="dialogFormType"
      :api="dialogFormApi"
      :title="dialogFormTitle"
      :inputLabel="inputLabel"
      :params="dialogFormParams"
      @confirmForm="confirmFormHandle"
    />

    <choose-list-dialog
      :showdialog.sync="showDialog"
      :title="dialogTitle"
      :type="dialogType"
      :api="dialogApi"
      :detailApi="dialogDetailApi"
      :search-setting="dialogSearchSetting"
      :table-settings="dialogTableSettings"
      :params="dialogParams"
      :defaultSelect="dialogSelect"
      :rowKey="dialogRowKey"
      :showSelectLen="showSelectLen"
      @confirmChoose="confirmChooseHandle"
    >
      <template v-if="dialogType === 'associated_purchase'" v-slot:tip="slotProps">
        <div class="">
          <span class="m-r-20" style="color: #333333; font-weight: 600;">当前物资：{{ dialogData.materials_name }}，出库数量：{{ dialogData.count }}</span>
          <span class="red">当前输入数量：{{ getChangeCountSum(slotProps.select) }}</span>
        </div>
        <div class="font-size-12 m-t-6">如库存不满足出库数量，可多选入库单据。</div>
      </template>
    </choose-list-dialog>

    <!-- 导入 start -->
    <import-page-dialog
      ref="importPageRef"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :loading="importLoading"
      :isUpload="false"
      isDeleteFirst
      @confirm="confirmImportHandle"
    ></import-page-dialog>
    <!-- 导入 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getUrlFilename, getSuffix, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import * as dayjs from 'dayjs'
import { integer, oneDecimal } from '@/utils/validata'
import { validateNumber, validateOneDecimalCount } from '@/utils/form-validata'
import ChooseListDialog from '../../components/ChooseListDialog'
import FormDialog from '../../components/FormDialog'
import NP from 'number-precision'
import { downloadJsonExcel } from '@/utils/excel'
import watch from '@/mixins/watch'

export default {
  name: 'AddOutboundOrder',
  mixins: [exportExcel, watch],
  components: { ChooseListDialog, FormDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add',
      warehouseId: '',
      warehouseName: '',
      accountName: this.$store.getters.userInfo.member_name,
      detailData: {},
      // form表单数据
      formData: {
        recipient: '', // 领用人
        inventoryExitType: '', // 出库类型
        exitTime: '', // 出库时间
        autoContactTrade: true, // 自动关联入库单据
        remark: '', // 备注
        fileLists: [],
        tableData: []
      },
      formRules: {
        exitTime: [{ required: true, message: '请选择', trigger: 'change' }],
        name: [{ required: true, message: '请选择', trigger: 'change' }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        production_date: [{ required: true, message: '请选择日期', trigger: 'change' }],
        inventoryExitType: [{ required: true, message: '请选择出库类型', trigger: 'change' }],
        // recipient: [{ required: true, message: '请输入领用人', trigger: 'change' }],
        associated: [{ required: true, message: '请先关联', trigger: 'change' }],
        number: [{ validator: this.validateEmptyCount, trigger: 'change' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      collapseTitle: '查看附件',
      // 出库类型
      inventoryExitTypeList: [
        // { label: '全部', value: '' },
        { label: '损耗出库', value: 'EXPEND_EXIT' },
        { label: '调拨出库', value: 'BORROW_EXIT' },
        { label: '领料出库', value: 'RECEIVE_EXIT' },
        { label: '退货出库', value: 'REFUND_EXIT' },
        { label: '其他出库', value: 'OTHER_EXIT' }
      ],
      // 出库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '当前库存', key: 'limit_unit_stock' },
        { label: '最小单位', key: 'unit_name', type: 'slot', slotName: 'unitName' },
        { label: '出库数量', key: 'count', type: 'slot', slotName: 'count' },
        { label: '成本价', key: 'entry_price', type: 'slot', slotName: 'entryPrice'  },
        { label: '合计', key: 'total_price', type: 'slot', slotName: 'totalPrice'  },
        { label: '关联入库单据', key: 'contact_trades', type: 'slot', slotName: 'associated' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      materialsTableData: [],
      activeCollapse: [],
      showDialog: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗loading
      dialogTitle: '选择物资',
      dialogType: '', // 弹窗的状态，add/modify
      dialogData: {}, // 弹窗数据
      remoteLoading: false,
      dialogTableSettings: [],
      dialogSearchSetting: {},
      dialogParams: {
        warehouse_id: this.$route.query.warehouse_id
      },
      dialogApi: '',
      dialogDetailApi: '',
      dialogSelect: [],
      showSelectLen: false,
      dialogRowKey: 'materials_id',
      // 导入的弹窗数据
      importLoading: false,
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/出库单-导入物资模板.xlsx',
      importHeaderLen: 0,
      importFailTableData: [], // 导入失败的数据
      importTableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '导入失败原因', key: 'result' }
      ],
      // 保存为草稿、模板、选择菜谱等的弹窗
      showFormDialog: false,
      dialogFormTitle: '',
      dialogFormType: '1',
      dialogFormApi: '1',
      inputLabel: '',
      dialogFormParams: {}
    }
  },
  computed: {
    previewList() {
      const result = this.formData.fileLists.map(v => v.url)
      this.setPreviewListTitle(result)
      return result
    }
  },
  watch: {
  },
  created() {
    this.initLoad()
  },
  mounted() {
    // 监听事件变化
    this.watchOneHandle('formData')
  },
  methods: {
    initLoad() {
      this.warehouseId = +this.$route.query.warehouse_id
      this.warehouseName = this.$route.query.warehouse_name
      this.type = this.$route.params.type
      if (this.$route.query.type === 'recovery') {
        this.getdraftDetail(+this.$route.query.id)
      }
      if (this.type === 'modify') {
        this.getOutboundOrder()
      }
      if (this.$route.query.type === 'to_exit_info') {
        this.getSubscriptionMaterials(this.$route.query.sg_trade_no)
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取详情数据
    async getOutboundOrder() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: +this.$route.query.id,
        warehouse_id: this.warehouseId
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpExitInfoDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.detailData = res.data
        // this.tableData = res.data.results
        this.formData.inventoryExitType = res.data.inventory_exit_type
        this.formData.recipient = res.data.recipient
        this.formData.exitTime = res.data.exit_time
        this.formData.remark = res.data.remark
        if (res.data.image_json && res.data.image_json.length > 0) {
          let fileImages = []
          res.data.image_json.forEach((v, index) => {
            fileImages.push({
              url: v,
              name: getUrlFilename(v),
              status: "success",
              uid: new Date().getTime() + index
            })
          })
          this.formData.fileLists = fileImages
        } else {
          this.formData.fileLists = []
        }
        if (res.data.exit_data) {
          this.formData.tableData = res.data.exit_data.map(v => {
            return {
              materials_id: v.materials_id,
              materials_name: v.materials_name,
              count: v.exit_count,
              unit_id: v.unit_id,
              unit_name: v.unit_name,
              current_num: v.current_num,
              contact_trade_list: v.contact_trade_list.map(v => {
                return {
                  trade_no: v.trade_no,
                  count: v.count,
                  changeCount: v.count,
                  supplier_manage_id: v.supplier_manage_id
                }
              })
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 草稿箱详情
    async getdraftDetail(id) {
      if (!id) return this.$message.error('获取id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id,
        inventory_info_type: 'exit_info'
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpExitInfoDraftDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // let result = res.data.extra
        this.detailData = res.data.extra
        // this.tableData = res.data.results
        this.formData.inventoryExitType = res.data.extra.inventory_exit_type
        this.formData.recipient = res.data.extra.recipient
        this.formData.remark = res.data.extra.remark
        if (res.data.extra.image_json && res.data.extra.image_json.length > 0) {
          let fileImages = []
          res.data.extra.image_json.forEach((v, index) => {
            fileImages.push({
              url: v,
              name: getUrlFilename(v),
              status: "success",
              uid: new Date().getTime() + index
            })
          })
          this.formData.fileLists = fileImages
        } else {
          this.formData.fileLists = []
        }
        if (res.data.extra.exit_data) {
          let result = res.data.extra.exit_data.map(v => {
            let current = deepClone(v)
            current.entry_price = v.entry_fee
            current.limit_unit_item = v.limit_unit_management.find(current => current.id === v.limit_unit_id)
            current.limit_unit_stock = v.current_num
            current.contact_trade_list = current.contact_trade_list.map(trade => {
              trade.changeCount = trade.count
              return trade
            })
            return current
          })
          this.getAutoMaterialsTrade(result)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取申购单物资
    async getSubscriptionMaterials(tradeNo) {
      if (!tradeNo) return this.$message.error('获取申购单单号失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        trade_no: tradeNo,
        warehouse_id: +this.$route.query.warehouse_id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoSubscribeInfoGetMaterialsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 自动关联
        if (this.formData.autoContactTrade) {
          let result = []
          res.data.forEach(v => {
            let item = {
              materials_name: v.materials_name,
              materials_id: v.materials_id,
              current_num: v.current_num,
              count: v.count,
              unit_name: v.unit_name,
              unit_id: v.unit_id,
              contact_trade_list: [],
              limit_unit_management: v.limit_unit_management // 出库需要最小单位列表等
            }
            // 申购单转采购单
            // 加了规格以后新增的,最小单位、最小单位库存
            let detailJson = JSON.parse(v.detail_json)
            item.limit_unit_id = detailJson.limit_unit_id
            item.limit_unit_item = item.limit_unit_management.find(current => current.supplier_manage_id === item.supplier_manage_id)
            item.limit_unit_stock = item.limit_unit_item.current_num
            result.push(item)
          })
          // this.confirmChooseHandle({
          //   type: 'to_exit_info',
          //   data: res.data
          // })
          this.getAutoMaterialsTrade(result)
        } else {
          this.confirmChooseHandle({
            type: 'to_exit_info',
            data: res.data
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 校验输入
    validateEmptyCount(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        console.log(rule, value, fieldArr)
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        // console.log(111, row)
          if (value) {
            if (!oneDecimal(value)) {
              callback(new Error('格式错误'))
            } else {
              if (row && Number(row.limit_unit_stock) < Number(value)) {
                callback(new Error('不能大于当前库存'))
              } else {
                callback()
              }
            }
          } else {
            callback(new Error('请检查数量'))
          }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 添加物资
    addMaterials(type, data, index) {
      this.dialogType = type
      if (data) {
        this.dialogData = data
        this.dialogData.index = index
      }
      this.dialogSelect = []
      // 选择关联入库单据
      if (type === 'associated_purchase') {
        this.dialogTitle = '关联入库单据'
        this.showSelectLen = false
        this.dialogApi = 'apiBackgroundDrpEntryInfoGetMaterialsTradePost'
        this.dialogParams = {
          warehouse_id: +this.$route.query.warehouse_id,
          materials_id: data.materials_id,
          materials_name: data.materials_name,  //这个是字段是为了传过去，再设置到返回的数据里面，其实接口不需要
          limit_unit_id: data.limit_unit_id,
          limit_unit_item: data.limit_unit_item,  //这个是字段是为了传过去，再设置到返回的数据里面，其实接口不需要
          total_limit_unit_stock: data.limit_unit_stock,  //这个是字段是为了传过去，再设置到返回的数据里面，拿到当前最小单位的总库存，其实接口不需要
        }
        // 自定义id{trade_no_物资id_供应商id}，物资id可能会重复，因为同一个物资可能关联多个不同的供应商
        this.dialogRowKey = 'custom_id'
        this.dialogSearchSetting = {}
        this.dialogTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '单据编号', key: 'trade_no' },
          { label: '供应商名称', key: 'supplier_manage_name' },
          { label: '入库时间', key: 'entry_time' },
          { label: '当前库存', key: 'current_count' },
          { label: '输入库存', key: 'changeCount', type: 'slot', slotName: 'inventory' }
        ]
        // 还原下已选的入库单数据吧
        if (data.contact_trade_list.length > 0) {
          this.dialogSelect = data.contact_trade_list
        }
      }
      // 添加物资
      if (type === 'exit_info') {
        this.dialogTitle = '添加物资'
        this.showSelectLen = true
        this.dialogApi = 'apiBackgroundDrpMaterialsGetMaterialsListPost'
        this.dialogSearchSetting = {
          name: {
            type: 'input',
            value: '',
            label: '物资名称',
            placeholder: '请输入'
          }
        }
        // 加个type拿本仓库的数据
        this.dialogParams = {
          warehouse_id: +this.$route.query.warehouse_id,
          type
        }
        this.dialogTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          { label: '最小单位', key: 'limit_unit', type: 'slot', slotName: 'limitUnit' },
          { label: '当前库存', key: 'limit_unit_stock' },
          { label: '出库数量', key: 'changeCount', type: 'slot', slotName: 'inventory' }
        ]
        this.dialogRowKey = 'materials_id'
      }
      this.showDialog = true
    },
    getChangeCountSum(list) {
      return list.reduce((prev, next) => {
        return NP.plus(prev || 0, next.changeCount || 0)
      }, 0)
    },
    // 弹窗确定事件
    async confirmChooseHandle(res) {
      this.showDialog = false
      console.log(11231, res)
      let result = []
      // 选择关联入库单据确定
      if (res.type === 'associated_purchase') {
        // 选择关联入库单据需要反选
        // this.dialogSelect = res.select.map(v => v[this.dialogRowKey])
        // this.formData.tableData.forEach(v => {
        //   if (v.materials_id === this.dialogData.materials_id) {
        //     this.$set(v, 'contact_trade_list', res.data)
        //     let changeCountSum = res.data.reduce((prev, next) => {
        //       return NP.plus(prev, next.changeCount||0)
        //     }, 0)
        //     this.$set(v, 'count', changeCountSum)
        //   }
        // })
        // 处理关联单据合并到表格
        // 同数据同单号直接覆盖/同数据不同单号新增/不同的数据直接push
        res.data.map(v => {
          v.key = `${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}` // 每条数据的唯一标识，不同物资、供应商、单位、入库价，分为不同数据
          let tableindex = this.formData.tableData.findIndex(tableitem => tableitem.key === v.key)
          if (tableindex !== -1) { // 同类型数据
            let tradeindex = this.formData.tableData[tableindex].contact_trade_list.findIndex(trade => trade.trade_no === v.trade_no)
            if (tradeindex !== -1) { // 同数据同单号直接覆盖
              this.formData.tableData[tableindex].contact_trade_list[tradeindex].changeCount = v.changeCount
              this.formData.tableData[tableindex].contact_trade_list[tradeindex].count = v.changeCount
            } else { // 同数据不同单号新增
              v.count = v.changeCount
              this.formData.tableData[tableindex].contact_trade_list.push(v)
            }
          } else { // 不同的数据直接push
            v.limit_unit_stock = v.total_limit_unit_stock // 当前单位最小库存=当前最小单位的总库存
            v.count = v.changeCount // 出库数量
            v.contact_trade_list = [v] // 关联单据
            this.formData.tableData.push(v)
          }
        })
        // 不自动关联的时候，点击确定关联，要把原来的那条数据删掉
        if (!this.formData.autoContactTrade && !this.dialogData.contact_trade_list.length) {
          let index = this.formData.tableData.findIndex(tableitem => tableitem === this.dialogData)
          this.formData.tableData.splice(index, 1)
        }
        this.formData.tableData.map(item => {
          if (item.contact_trade_list.length) { // 关联了单据
            item.count = item.contact_trade_list.reduce((prev, next) => {
              return NP.plus(prev, next.count||0)
            }, 0)
          }
        })
      }
      if (res.type === 'exit_info') {
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            materials_id: v.materials_id,
            current_num: v.current_num,
            count: v.changeCount,
            unit_name: v.unit_name,
            unit_id: v.unit_id,
            contact_trade_list: [],
            // 出库需要最小单位列表
            limit_unit_management: v.limit_unit_management,
            limit_unit_id: v.limit_unit_id,
            limit_unit_item: v.limit_unit_item,
            limit_unit_stock: v.limit_unit_stock
          }
          result.push(item)
        })
        // 自动关联物资直接调用接口
        if (this.formData.autoContactTrade) {
          await this.getAutoMaterialsTrade(result)
        } else {
          if (this.formData.tableData.length > 0) {
            this.formData.tableData = this.mergeArrays(this.formData.tableData, result)
          } else {
            this.formData.tableData = result
          }
        }
      }
      if (res.type === 'to_exit_info') {
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            materials_id: v.materials_id,
            current_num: v.current_num,
            count: v.count,
            unit_name: v.unit_name,
            unit_id: v.unit_id,
            contact_trade_list: []
          }
          result.push(item)
        })
        // 自动关联物资直接调用接口
        if (this.formData.tableData.length > 0) {
          this.formData.tableData = this.mergeArrays(this.formData.tableData, result)
        } else {
          this.formData.tableData = result
        }
      }
      this.$refs.formRef.clearValidate('tableData[' + this.dialogData.index + '].contact_trade_list')
    },
    // 自动关联入库单
    async getAutoMaterialsTrade(materials) {
      console.log('materials', materials)
      let params = {
        warehouse_id: this.warehouseId,
        materials_data: materials.map(v => {
          return {
            materials_id: v.materials_id,
            count: +v.count,
            limit_unit_id: v.limit_unit_id
          }
        })
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoAutoGetMaterialsTradePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showImportDialog = false
        this.setAutoMaterialsTrade(materials, res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置自动关联的入库单数据
    setAutoMaterialsTrade(materialsList, autoMaterials) {
      // 相同的物资处理
      let autoMaterialsObj = {}
      if (autoMaterials && autoMaterials.length > 0) {
        autoMaterials.forEach(v => {
          // 同步下数据字段
          v.count = v.use_count
          // 这个要同步下，因为提交时会用到，这是关联物资里面的数量
          v.changeCount = v.use_count
          let current = v
          // if (autoMaterialsObj[v.materials_id]) {
          //   autoMaterialsObj[v.materials_id].push(current)
          // } else {
          //   autoMaterialsObj[v.materials_id] = [current]
          // }
          // 区分不同单价/供应商/单位的物资
          // 每条数据的唯一标识，不同物资、供应商、单位、入库价，分为不同数据
          if (autoMaterialsObj[`${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`]) {
            autoMaterialsObj[`${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`].push(current)
          } else {
            autoMaterialsObj[`${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`] = [current]
          }
        })
      }
      let materials_list = [] // 不同单价/供应商/单位的物资，要分成多条，重新搞个数组保存
      for (let key in autoMaterialsObj) {
        let keyList =  key.split('_')
        // 从弹窗来的物资信息
        let materials = materialsList.find(current => current.materials_id === Number(keyList[0]))
        // 库存数量
        let count = autoMaterialsObj[key].reduce((prev, next) => {
          return NP.plus(prev, next.count||0)
        }, 0)
        // 成本价
        let entry_price = autoMaterialsObj[key].length ? autoMaterialsObj[key][0].entry_price : 0
        materials_list.push({
          ...materials,
          key: key,
          contact_trade_list: autoMaterialsObj[key],
          count: count,
          entry_price: entry_price
        })
      }
      // materialsList.forEach(v => {
      //   // 往添加的物资里添加关联的入库单数据
      //   if (autoMaterialsObj[v.materials_id]) {
      //     // 关联入库单据
      //     v.contact_trade_list = autoMaterialsObj[v.materials_id]
      //     // 同步下自动关联的数量，因为有库存限制
      //     v.count = autoMaterialsObj[v.materials_id].reduce((prev, next) => {
      //       return NP.plus(prev, next.count||0)
      //     }, 0)
      //   }
      // })
      if (this.formData.tableData.length > 0) {
        this.formData.tableData = this.mergeArrays(this.formData.tableData, materials_list, true)
      } else {
        // 自动关联的话如果
        this.formData.tableData = materials_list
      }
      console.log(this.formData.tableData, 'this.formData.tableData')
    },
    // 合并新旧数据，不同单价/供应商/单位的物资，要分成多条，相同的数据需要合并
    // 出库数量与关联的入库单输入库存相关联
    // 当已添加的物资列表中存在关联过入库单数据的，但新增增物资时又是选了同一个物资，则需要清除旧的物资数据中的关联入库单数据
    mergeArrays(tableData, newData, notAccumulation) {
      console.log('mergeArrays', tableData, newData)
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        merged[current.key] = current
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = item.key
        if (merged[key] === item.key) {
          // 关联入库单据，新旧数据合并，新数据需要替换旧数据（物资id和供应商id相同的情况）
          // 当旧数据有关联，以新数据的为准
          merged[key].contact_trade_list = item.contact_trade_list
          // notAccumulation 是否不需要累加出库数量
          // 自动关联是不做累加的
          // 非自动关联新增物资时如果是同一物资时需要累加
          if (notAccumulation) {
            merged[key].count = item.count
          } else {
            merged[key].count = NP.plus(merged[key].count, item.count)
          }
        } else {
        // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    // 修改数量时如果是自动关联的需要调接口更新关联单号数据
    changeCountHandle: debounce(function(e, row, index) {
      this.getAutoMaterialsTrade([row])
    }, 300),
    // 删除物资
    deleteMaterials(index) {
      this.formData.tableData.splice(index, 1)
    },
    // 导入弹窗
    openImport(type) {
      if (!this.$route.query.warehouse_id) {
        return this.$message.error('获取仓库数据失败！')
      }
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    // 导入确定事件
    confirmImportHandle(data) {
      let importData = data
      // 删除示例数据
      // importData.splice(1, 1)
      console.log(111, importData)
      // importResult
      if (importData.length > 1) {
        // this.importLoading = true
        // 导入数据对应后端需要的字段
        const purchaseNameObject = {
          '物资名称': 'materials_name',
          '最小单位': 'limut_unit',
          '出库数量': 'exit_count'
        }
        // 根据purchaseNameObject转换的index key
        let resultKey = {}
        importData[0].forEach((v, index) => {
          resultKey[index] = purchaseNameObject[v]
        })
        let result = []
        importData.forEach((item, index) => {
          if (index > 0) {
            let current = {}
            item.forEach((v, k) => {
              current[resultKey[k]] = v
            })
            result.push(current)
          }
        })
        console.log(222, result)
        this.sendImportMaterials(result)
      } else {
        this.$message.error('导入物资为空！')
      }
    },
    // 发送导入物资数据给后端校验
    async sendImportMaterials(data) {
      if (this.importLoading) return
      this.importLoading = true
      let params = {
        warehouse_id: this.$route.query.warehouse_id,
        data: data
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpExitInfoImportMaterialsPost(params))
      // this.isLoading = false
      if (err) {
        this.importLoading = false
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.success && res.data.success.length > 0) {
          let result = []
          res.data.success.forEach(v => {
            let item = {
              materials_name: v.materials_name,
              materials_id: v.materials_id,
              count: v.exit_count,
              unit_name: v.unit_name,
              unit_id: v.unit_id,
              contact_trade_list: [],
              // 出库需要最小单位列表
              limit_unit_management: v.limit_unit_management,
              limit_unit_id: v.limit_unid_id,
              limit_unit_item: null,
              limit_unit_stock: v.current_num,
              // key: `${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`
            }
            item.limit_unit_item = v.limit_unit_management.find(current => current.id === v.limit_unid_id)
            result.push(item)
          })
          // 开启自动关联
          if (this.formData.autoContactTrade) {
            await this.getAutoMaterialsTrade(result)
          } else { // 不开启自动关联
            if (this.formData.tableData.length > 0) {
              this.formData.tableData = this.mergeArrays(this.formData.tableData, result)
            } else {
              this.formData.tableData = result
            }
          }
        }
        if (res.data.failure && res.data.failure.length > 0) {
          this.formatImportFailureResult(res.data.failure)
          this.$message.error('部分物资导入失败，请查看excel!')
        }
        this.$refs.importPageRef.reset()
        this.showImportDialog = false
      } else {
        this.$message.error(res.msg)
      }
      this.importLoading = false
    },
    // 格式化导入失败的数据，通过xlsx方式下载显示
    formatImportFailureResult(result) {
      const purchaseNameObject = {
        'materials_name': 0,
        'exit_count': 1,
        'result': 2
      }
      let failureJson = [
        ['物资名称', '出库数量', '导入结果']
      ]
      let json = result.map(v => {
        let current = []
        Object.keys(purchaseNameObject).forEach(k => {
          current[purchaseNameObject[k]] = v[k]
        })
        return current
      })
      failureJson = failureJson.concat(json)
      // 下载数据
      downloadJsonExcel(failureJson)
    },
    getFileLists(fileLists) {
      this.formData.fileLists = fileLists
    },
    // 上传图片前钩子
    beforeUpload(file) {
      let uploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      if (!uploadType.includes(getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 <= 2
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 2M')
      }
      return isLt20M
    },
    //
    setPreviewListTitle(result) {
      this.collapseTitle = '查看附件(' + result.length + ')'
    },
    // 删除图片
    deleteUploadImg(index) {
      const fileUploadRef = this.$refs.fileUploadRef
      if (this.formData.fileLists[index]) {
        fileUploadRef && fileUploadRef.spliceFileData(this.formData.fileLists[index].uid)
      }
      this.formData.fileLists.splice(index, 1)
    },
    // 格式化数据
    formatParams() {
      let params = {
        inventory_exit_type: this.formData.inventoryExitType,
        warehouse_id: this.warehouseId,
        exit_time: this.formData.exitTime,
        remark: this.formData.remark
      }
      if (this.formData.recipient) {
        params.recipient = this.formData.recipient
      }
      if (this.$route.query.type === 'to_exit_info') {
        params.sg_trade_no = this.$route.query.sg_trade_no
      }
      params.exit_data = this.formData.tableData.map(v => {
        return {
          materials_id: v.materials_id,
          name: v.materials_name,
          count: v.count,
          unit_id: v.unit_id,
          current_num: v.current_num,
          limit_unit_id: v.limit_unit_id,
          unit_name: `${v.limit_unit_item.unit_management_name}*${v.limit_unit_item.net_content}${v.limit_unit_item.net_content_unit}`,
          entry_fee: v.entry_price,
          contact_trade_list: v.contact_trade_list.map(v => {
            return {
              trade_no: v.trade_no,
              count: v.changeCount,
              supplier_manage_id: v.supplier_manage_id
            }
          }),
          key: v.key
        }
      })
      params.image_json = this.previewList
      return params
    },
    // 确定
    submitHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (!this.formData.tableData.length) return this.$message.error('请先选择物资！')
          let api = ''
          let params = this.formatParams()
          if (this.type === 'modify') {
            api = 'apiBackgroundDrpExitInfoModifyPost'
            params.id = +this.$route.query.id
            this.showConfirm(api, params)
          } else {
            api = 'apiBackgroundDrpExitInfoAddPost'
            this.showConfirm(api, params)
          }
        }
      })
    },
    showConfirm(api, params) {
      this.$confirm(`请确定编辑的内容无误，将进行保存出库`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async(action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            await this.sendDataHandle(api, params, () => {
              done()
              this.$backVisitedViewsPath(this.$route.path, 'OutboundOrder')
            })
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
            
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 发送
    async sendDataHandle(api, params, callback) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis[api](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        if (callback) {
          callback()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开存为草稿弹窗
    openFormDialog(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (!this.formData.tableData.length) return this.$message.error('请先选择物资！')
          this.dialogFormType = type
          switch (type) {
            case 'draft':
              this.dialogFormTitle = '存为草稿'
              this.inputLabel = '草稿名称'
              this.dialogFormApi = 'apiBackgroundDrpExitInfoTempAddPost'
              this.dialogFormParams = this.setDialogFormParams(type)
              break;
          }
          this.showFormDialog = true
        } else {
          this.$message.error('请认真检查表单数据！')
        }
      })
    },
    // 设置存为草稿或者存为模板的数据
    setDialogFormParams(type) {
      // inventory_info_type
      // purchase : "采购单"
      // entry_info : "入库单"
      // exit_info : "出库单"
      // return_info : "退货单"
      // inquiry : "询价单"
      let params = {
        inventory_info_type: 'exit_info',
        warehouse_id: this.warehouseId,
        temp_type: type,
        remark: this.formData.remark,
        recipient: this.formData.recipient,
        exit_time: this.formData.exitTime,
        inventory_exit_type: this.formData.inventoryExitType,
        image_json: this.previewList,
        exit_data: this.formData.tableData.map(v => {
          v.entry_fee = v.entry_price,
          v.contact_trade_list = v.contact_trade_list.map(trade => {
            return {
              inventory_no: trade.inventory_no,
              trade_no: trade.trade_no,
              count: trade.changeCount,
              current_count: trade.current_count,
              entry_time: trade.entry_time,
              supplier_manage_name: trade.supplier_manage_name,
              start_valid_date: trade.start_valid_date,
              end_valid_date: trade.end_valid_date,
              supplier_manage_id: trade.supplier_manage_id
            }
          })
          return v
        })
      }
      return params
    },
    // form弹窗
    confirmFormHandle(e) {
      if (e && e.type === 'draft') {
        this.$backVisitedViewsPath(this.$route.path, 'OutboundOrder')
      }
    },
    backHandle() {
      if (this.isUpdateForm) {
        this.$confirm(`当前内容暂未保存，确定取消？`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              // instance.confirmButtonLoading = true
              done()
              this.$backVisitedViewsPath(this.$route.path, 'OutboundOrder')
              // instance.confirmButtonLoading = false
            } else {
              // if (!instance.confirmButtonLoading) {
              //   this.$closeCurrentTab(this.$route.path)
              // }
              done()
            }
          }
        })
          .then(e => {
          })
          .catch(e => {})
      } else {
        this.$backVisitedViewsPath(this.$route.path, 'OutboundOrder')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.AddOutboundOrder {
  .form-container {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
  }
  .label-text {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
  }
  .w-220 {
    width: 220px;
  }
  .w-280 {
    width: 280px;
  }
  .w-auto {
    width: 300px;
  }
  .m-b-0 {
    margin-bottom: 0;
    &.is-error {
      margin-bottom: 20px;
    }
  }
  .form-container {
    .upload-img {
      width: 90px;
      height: 90px;
    }
    .img-item {
      display: inline-block;
      position: relative;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      .img-tools {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ffffff;
        font-size: 20px;
        transition: 0.3s;
        i {
          cursor: pointer;
          color: #ff9b45;
        }
      }
      &:hover {
        .img-tools {
          display: inline-block;
        }
      }
    }
  }
}
</style>
