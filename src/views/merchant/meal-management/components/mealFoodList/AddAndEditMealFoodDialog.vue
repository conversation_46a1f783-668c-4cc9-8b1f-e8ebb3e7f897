<template>
  <div>
    <!-- 新增或者编辑 -->
    <el-drawer
      :title="foodDialogTitle"
      :visible.sync="showDialog"
      size="1000px"
      custom-class="ps-el-drawer"
      :show-close="false"
      :wrapperClosable="false"
    >
      <div class="msg-tips m-l-20 p-t-20 p-b-10">添加菜品后可在菜品库查看，缺失的食材会自动同步</div>
      <div v-loading="formFoodLoading">
        <el-form
          ref="formFoodData"
          :rules="formFoodDataRuls"
          :model="formFoodData"
          label-width="80px"
          size="small"
          class="dialog-form add-and-edit-meal-food m-l-20 m-r-20 m-t-20 m-b-50"
        >
          <div class="add-form-wrapper" v-if="showDialogMealFoodType != 'batchAdd'">
            <el-form-item label="菜品名称" prop="foodName" label-width="100px">
              <!-- <el-input
                class="ps-input"
                style="width: 190px"
                placeholder="请输入菜品名称"
                v-model="formFoodData.foodName"
              ></el-input> -->
              <el-select
                v-model="formFoodData.foodName"
                clearable
                filterable
                remote
                :loading="remoteLoading"
                :remote-method="getSystemFoodList"
                placeholder=""
                ref="foodNameRef"
                @visible-change="visibleChangeFoodHandle"
                @focus="focusFoodHandle"
                @clear="clearFoodHandle"
                @change="changeFoodHandle"
                style="width: 400px"
              >
                <el-option
                  v-for="item in foodList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :text-light="true"
                >
                <div v-html="item.highlight_text"></div>
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-tooltip effect="dark" content="增加菜品别名" placement="top">
              <img class="add-btn-img" @click="addFoodAliasName" src="@/assets/img/plus.png" alt="">
            </el-tooltip> -->

          </div>
          <div v-if="formFoodData.aliasName.length">
            <el-form-item label="菜品别名" label-width="100px">
              <el-form-item
                :class="[index>0?'m-t-10':'','food-alias-name-form']"
                v-for="(item,index) in formFoodData.aliasName"
                :key="index"
                :rules="formFoodDataRuls.aliasName"
                :prop="`aliasName[${index}]`"
                >
                <el-input maxlength="20" v-model="formFoodData.aliasName[index]" placeholder="请输入菜品别名" class="ps-input" style="width:400px;"></el-input>
                <img src="@/assets/img/plus.png" @click="addFoodAliasName" alt="">
                <img src="@/assets/img/reduce_red.png" @click="delFoodAliasName(index)" alt="" v-if="index>0" />
              </el-form-item>
            </el-form-item>
          </div>
          <el-form-item label="属性" prop="attributes" label-width="100px">
              <el-radio-group v-model="formFoodData.attributes" class="ps-radio">
                <el-radio label="foods">菜品</el-radio>
                <el-radio label="goods">商品</el-radio>
              </el-radio-group>
            </el-form-item>

          <el-form-item v-if="formFoodData.attributes === 'goods'" label="条形码" prop="barcode" label-width="100px">
              <el-input
                class="ps-input"
                style="width: 190px"
                placeholder="请输入条形码"
                v-model="formFoodData.barcode"
                maxlength="13"
              ></el-input>
            </el-form-item>
          <!-- <el-form-item label-width="100px" label="口味">
            <div v-for="(item, index) in formFoodData.tasteList" :key="index">
              <el-input v-model="item.name" class="fun-box-weight"></el-input>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                v-if="formFoodData.tasteList.length != 1"
                @click.prevent="deleteFunList('taste', index, item)"
              >
                删除
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="index == formFoodData.tasteList.length - 1"
                @click.prevent="addFunList('taste', item)"
              >
                新增
              </el-button>
            </div>
          </el-form-item> -->
          <!-- <el-form-item label-width="100px" label="规格">
            <el-select
              v-model="formFoodData.specList"
              filterable
              allow-create
              style="width: 190px;"
              placeholder="请选择规格">
              <el-option
                v-for="item in specLists"
                :key="item.name"
                :label="item.name"
                :value="item.name">
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item :label="`${groupKey}:`" prop="" class="" v-for="(labelGroupItem,groupKey,labelGroupIndex) in formFoodData.labelGroupInfoList" :key="labelGroupIndex">
            <el-tag
              class="m-r-5 collapse-data"
              v-for="(item, index) in labelGroupItem"
              :key="index"
              size="medium"
              effect="plain"
              type="info"
              color="#fff"
              closable
              @close="closeTag(groupKey,index,item)"
            >
              {{item.name}}
            </el-tag>
          </el-form-item> -->
          <!-- <el-form-item
            label="新品上新"
            prop=""
            label-width="100px"
          >
            <div>
              <el-date-picker
                v-model="formFoodData.newProductDate"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :popper-append-to-body="false"
              ></el-date-picker>
            </div>
          </el-form-item> -->
          <div class="add-form-wrapper">
            <!-- <el-form-item label="支持预约" label-width="100px" class="add-form-item-wrapper">
              <el-radio-group v-model="formFoodData.isSupportReservation">
                <el-radio :label="false" class="ps-radio">否</el-radio>
                <el-radio :label="true" class="ps-radio">是</el-radio>
              </el-radio-group>
            </el-form-item> -->
            <!-- <el-form-item label="状态" prop="" label-width="100px">
              <el-radio-group v-model="formFoodData.saleStatus" text-color="#ff8f44">
                <el-radio :label="0" class="ps-radio">下架</el-radio>
                <el-radio :label="1" class="ps-radio">上架</el-radio>
              </el-radio-group>
            </el-form-item> -->
          </div>

          <el-form-item
            label="图片"
            label-width="100px"
            v-if="showDialogMealFoodType != 'batchAdd'"
          >
            <div class="ps-red">仅支持jpg、png、bmp格式，大小不超过5M</div>
            <el-upload
              ref="uploadFoodImage"
              :class="{'upload-food': true, 'hide-upload':formFoodData.foodImagesList.length>0}"
              drag
              :data="uploadParams"
              :action="actionUrl"
              :multiple="false"
              :file-list="formFoodData.foodImagesList"
              list-type="picture-card"
              :on-change="handelChange"
              :on-success="handleFoodImgSuccess"
              :before-upload="beforeFoodImgUpload"
              :limit="1"
              :headers="headersOpts"
            >
            <div v-if="formFoodData.foodImagesList.length<1" class="upload-placeholder">
                  <div class="upload-icon"><i class="el-icon-circle-plus"></i></div>
                  <div class="upload-text">上传图片</div>
              </div>
              <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
                <div class="upload-food-img"><el-image :src="file.url" alt="" fit="cover" class="img-tag"></el-image></div>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleFoodImgRemove(file, 'foodImages')">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="识别图片"
            label-width="100px"
          >
            <div class="ps-red">仅支持jpg、png、bmp格式，大小不超过5M，最多上传25张</div>
            <el-upload
              ref="uploadExtraImage"
              class="upload-food"
              drag
              :data="uploadParams"
              :action="actionUrl"
              :multiple="true"
              :file-list="formFoodData.extraImagesList"
              list-type="picture-card"
              :on-change="handelChange"
              :on-success="handleExtraImgSuccess"
              :before-upload="beforeFoodImgUpload"
              :limit="25"
              :headers="headersOpts"
            >
              <div v-if="formFoodData.extraImages.length<25" class="upload-placeholder">
                <div class="upload-icon"><i class="el-icon-circle-plus"></i></div>
                <div class="upload-text ">上传识别图片</div>
              </div>
              <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
                <div class="upload-food-img"><el-image :src="file.url" alt="" fit="cover" class="img-tag"></el-image></div>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleFoodImgRemove(file, 'extraImages')">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <!-- <div style="font-weight: bold" v-if="showDialogMealFoodType==='edit'">
            设备上传的菜品图片
            <el-form-item label="" prop="" label-width="0px" class="equipment-upload">
              <ul class="list__item">
                <li v-for="(fit, index) in formFoodData.fileListFood" :key="index">
                  <div class="img-box">
                    <img style="" class="img" :src="fit" />
                    <div class="delete-icon" @click="foodEquipmentOnRemove(index)">
                      <i class="el-icon-circle-close"></i>
                    </div>
                  </div>
                </li>
              </ul>
            </el-form-item>
          </div> -->
          <el-form-item label="分类" prop="categoryId" label-width="100px">
            <el-cascader v-if="showDialog" v-model="formFoodData.categoryId" :options="foodCategoryList" :props="{
                multiple: false,
                checkStrictly: false,
                value: 'id',
                label: 'name',
                children: 'children',
                emitPath: false
              }" ></el-cascader>
          </el-form-item>
          <el-form-item label="烹饪方式" prop="cookingType" label-width="100px" v-if="formFoodData.attributes === 'foods'">
              <div class="ps-flex">
                <div v-for="(item, index) in cookingTypeList" :key="item.value"
                  :class="['cooking-type-item', formFoodData.cookingType == item.value ? 'active' : '', index > 0 ? 'm-l-10' : '']"
                  @click="handleCookingTypeChange(item)">{{ item.label }}</div>
              </div>
            </el-form-item>
          <el-form-item label="价格信息" prop="" label-width="100px">
            <el-radio-group
              v-model="formFoodData.countType"
              text-color="#ff8f44"
              @change="changeCount"
            >
              <el-radio :label="1" class="ps-radio">菜品 / 商品价格</el-radio>
              <el-radio :label="2" class="ps-radio">称重价格</el-radio>
              <el-radio :label="3" class="ps-radio">菜品 / 商品价格 + 称重价格</el-radio>
            </el-radio-group>
            <div v-if="formFoodData.countType == 1">
              <!-- <el-form-item label="菜品 / 商品价格" prop="foodPrice" label-width="130px">
                <el-input
                  class="ps-input input"
                  placeholder="请输入菜品价格"
                  v-model="formFoodData.foodPrice"
                ></el-input>
                <span>&nbsp;元</span>
              </el-form-item> -->
              <ul class="form-ul">
                <div class="clearfix form-li-box">
                  <li class="form-li float-l from-bg-gray">
                    规格
                  </li>
                  <li class="form-li float-l li-center from-bg-gray">
                    价格（￥）
                  </li>
                  <li class="form-li float-l li-center from-bg-gray">
                    重量（g）
                  </li>
                  <li class="form-li float-l from-bg-gray">
                    操作
                  </li>
                </div>
                <div class="clearfix form-li-box" v-for="(item, index) in formFoodData.foodPriceJson" :key="index">
                  <li class="form-li float-l">
                    <el-form-item  class="no-label" label="" :show-message="false" :rules="formFoodDataRuls.foodSpec" :prop="'foodPriceJson['+index+'].spec'">
                      <el-input :disabled="!index" v-model="item.spec"></el-input>
                    </el-form-item>
                  </li>
                  <li class="form-li float-l li-center">
                    <el-form-item  class="no-label" label="" :show-message="false" :rules="formFoodDataRuls.foodPriceJson" :prop="'foodPriceJson['+index+'].price'">
                      <el-input v-model="item.price"></el-input>
                    </el-form-item>
                  </li>
                  <li class="form-li float-l li-center">
                    <!-- :rules="formFoodDataRuls.foodWeight"  -->
                    <el-form-item  class="no-label" label="" :show-message="false" :rules="formFoodDataRuls.foodPriceJson" :prop="'foodPriceJson['+index+'].weight'">
                      <el-input v-model="item.weight"></el-input>
                    </el-form-item>
                  </li>
                  <li class="form-li float-l">
                    <div class="ps-flex row-center">
                      <el-button type="text" @click="addFoodPriceJson" v-if="index === formFoodData.foodPriceJson.length-1">添加</el-button>
                      <el-button type="text" @click="deleteFoodPriceJson(index)" v-if="index">删除</el-button>
                    </div>
                  </li>
                </div>
              </ul>
            </div>
            <div v-else-if="formFoodData.countType == 2 || formFoodData.countType == 3">
              <el-radio-group
                v-model="formFoodData.weightType"
                text-color="#ff8f44"
                @change="changeWeightType"
                class="m-t-20 m-b-20"
              >
                <el-radio :label="1" class="ps-radio">按克</el-radio>
                <el-radio :label="2" class="ps-radio">按份</el-radio>
              </el-radio-group>
              <div v-if="formFoodData.weightType == 1">
                <el-form-item label="称重价格" prop="weightPrice" label-width="80px" class="align-left">
                  <el-input
                    class="ps-input"
                    style="width: 130px; padding-bottom: 10px"
                    v-model="formFoodData.weightPrice"
                  ></el-input>
                  <span>&nbsp; 元/&nbsp;</span>
                  <el-input class="ps-input input" v-model="formFoodData.weight"></el-input>
                  <span>&nbsp; 克</span>
                </el-form-item>
                <el-form-item
                  label="菜品 / 商品价格"
                  prop="foodPrice"
                  label-width="120px"
                  v-if="formFoodData.countType == 3"
                >
                  <el-input class="ps-input input" v-model="formFoodData.foodPrice"></el-input>
                  <span>&nbsp; 元</span>
                </el-form-item>
              </div>
              <div v-if="formFoodData.weightType == 2" class="add-form-item-wrapper">
                <div class="add-form-wrapper">
                  <el-form-item label="称重价格" prop="weightPrice" label-width="80px" class="align-left">
                    <el-input class="ps-input input" v-model="formFoodData.weightPrice"></el-input>
                    <span>&nbsp; 元/份</span>
                  </el-form-item>
                  <el-form-item
                    label="单份重量"
                    prop="weight"
                    label-width="110px"
                    class="add-form-item-wrapper"
                  >
                    <el-input class="ps-input input" v-model="formFoodData.weight"></el-input>
                    <span>&nbsp; 克</span>
                  </el-form-item>
                </div>
                <div class="add-form-wrapper">
                  <el-form-item label="起始计价重量" prop="startGram" label-width="110px" class="align-left">
                    <el-input class="ps-input input" v-model="formFoodData.startGram"></el-input>
                    <span>&nbsp; 克</span>
                  </el-form-item>
                  <el-form-item
                    label="误差率"
                    prop="faultRate"
                    label-width="100px"
                    class="add-form-item-wrapper"
                  >
                    <el-input class="ps-input input" v-model="formFoodData.faultRate"></el-input>
                    <div class="text-tips">误差率指每份菜品的重量在±误差率内都算1份</div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item
                  label="菜品 / 商品价格"
                  prop="foodPrice"
                  label-width="120px"
                  v-if="formFoodData.countType == 3"
                  class="align-left"
                >
                  <el-input class="ps-input input" v-model="formFoodData.foodPrice"></el-input>
                  <span>&nbsp; 元</span>
                </el-form-item>
                </div>
              </div>
            </div>
            <el-form-item label="成本价" prop="originPrice" label-width="80px" class="align-left">
              <el-input
                class="ps-input input"
                placeholder="请输入成本价"
                v-model="formFoodData.originPrice"
              ></el-input>
              <span>&nbsp;元</span>
            </el-form-item>
            <el-form-item label="打包费" prop="packPrice" label-width="80px" class="align-left">
              <el-input
                class="ps-input input"
                placeholder="请输入打包费"
                v-model="formFoodData.packPrice"
              ></el-input>
              <span>&nbsp; 元</span>
            </el-form-item>
          </el-form-item>
          <el-form-item label="标签" prop="" class="" label-width="100px">
              <el-cascader v-if="showDialog" v-model="formFoodData.selectLabelIdList" :options="labelList" :props="{
                multiple: true,
                checkStrictly: false,
                value: 'id',
                label: 'name',
                children: 'label_list',
                emitPath: false
              }" placeholder="请选择标签" clearable :collapse-tags="true" :show-all-levels="false"
                @change="handleLabelChange" />
              <div class="selected-tags" v-if="selectedTags.length">
                <el-tag v-for="tag in selectedTags" :key="tag.id" closable class="tag-item"
                  @close="handleRemoveTag(tag)">
                  {{ tag.name }}
                </el-tag>
              </div>
            </el-form-item>
          <div v-if="showDialogMealFoodType != 'batchAdd'">
            <!--<span class="origin-text">相同的食材会自动覆盖，缺失的食材会自动同步。如无填写食材信息，系统将自动关联食材</span>-->
            <el-form-item label-width="100px" label="食材占比">
              <div class="ps-flex row-between w-600">
                <div class="ps-red">如无填写食材信息，系统将自动关联食材</div>
                <div class="ps-text pointer" @click="addFunList('food')">添加</div>
              </div>
              <div class="food-proportion-box">
                <div class="w-200 food wh t-a-c  border-top border-left border-right border-bottom">食材</div>
                <div class="w-200 proportion t-a-c  border-top border-right border-bottom">占比（{{ percentageCount  }}%）</div>
                <div class="w-200 tools t-a-c  border-top  border-right border-bottom">操作</div>
              </div>
              <div :class="['food-proportion-wrapper', errorMsg.percentageError?'error-border':'']">
                <div
                  v-for="(item, index) in formFoodData.ingredientList"
                  :key="index"
                  class="food-proportion-box"
                >
                  <div class="w-200 border-left border-right border-bottom t-a-c content-tag">
                    <virtual-select
                      v-model="item.ingredient_id"
                      :width="180"
                      :popover-width="200"
                      placeholder="请下拉选择"
                      class="ps-select"
                      filterable
                      :data-list="item.selectFoodIngredient"
                      @change="changeIngredient"
                      :option="{
                        label: 'name',
                        value: 'id'
                      }"
                    >
                    </virtual-select>
                  </div>
                  <!-- <el-progress
                    :percentage="30"
                    color="#0ab7b7"
                    class="cantent"
                  ></el-progress> -->
                  <div class="w-200 t-a-c  border-right border-bottom content-tag">
                    <div style="width: 200px;">
                      <el-input-number class="cantent" v-model="item.percentage" @change="changePercentage" ></el-input-number>
                    </div>
                  </div>
                  <div class="w-200 t-a-c border-right border-bottom content-tag">
                    <el-button
                      type="text"
                      size="small"
                      class="ps-warn"
                      v-if="formFoodData.ingredientList.length !== 0"
                      @click.prevent="deleteFunList('food', index, item)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
              <div style="color: red;" v-if="errorMsg.percentageError">{{errorMsg.percentageError}}</div>
            </el-form-item>
            <el-form-item label="营养信息" prop="" class="" label-width="100px">
              <div class="ps-flex flex-wrap m-l-40">
                <div class="nutrition-item" :key="nutrition.key" v-for="(nutrition) in nutritionList">
                  <div class="nutrition-label">{{ nutrition.name + '（' + nutrition.unit + '）' + '：' }}</div>
                  <div class="nutrition-value">{{ tableDataNutrition[nutrition.key] }}</div>
                </div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <span  class="ps-meal-footer m-l-20 p-t-10">
        <el-button class="ps-cancel-btn" @click="cancel">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :disabled="formFoodLoading"
          :loading="formFoodLoading"
          @click="determineDialogFood"
        >
          保 存
        </el-button>
      </span>
    </el-drawer>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
      ref="selectLaber"
      >
        <div slot="append">
          <div class="tab">
            <div
              :class="['tab-item', ruleSingleInfo.isAdmin ? 'active' : '']"
              @click="tabClick"
            >
              平台标签
            </div>
            <div
              :class="['tab-item', ruleSingleInfo.isAdmin === false ? 'active' : '']"
              @click="tabClick"
            >
              自有标签
            </div>
          </div>
        </div>
    </select-laber>
  </div>
</template>

<script>
import { to, deepClone, times, divide, replaceSingleQuote, getToken, getSuffix, getSessionStorage } from '@/utils'
import NP from 'number-precision'
// import nutritionData from '../mealFoodList/NutritionTable.vue'
import { validataPlateAmount, validataPlateAmountPrice } from '@/assets/js/validata'
import { NUTRITION_LIST, SPEC_LIST } from '../../food-admin/constants'
import { COOKING_TYPE_LIST } from '@/views/super/health-system/health-nutrition/constants'
import selectLaber from '../../components/selectLaber.vue'
import VirtualSelect from "@/components/VirtualSelect/index.vue";

export default {
  props: {
    showDialogMealFood: Boolean,
    showDialogMealFoodType: String,
    foodDialogTitle: String,
    formFoodDataDialog: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function,
    foodCategoryList: Array,
    selectListId: Array
  },
  components: { selectLaber, VirtualSelect },
  data() {
    let valiPrice = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('格式有误'))
      } else {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('格式有误'))
        } else {
          callback()
        }
      }
    }
    let valiWeight = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('格式有误'))
      } else {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('格式有误'))
        } else {
          callback()
        }
      }
    }
    let validatorImage = (rule, value, callback) => {
      if (!this.formFoodData.foodImages.length) {
        return callback(new Error('请上传菜品图片'))
      } else {
        callback()
      }
    }
    return {
      formFoodData: {
        foodName: '',
        aliasName: [], // 菜品别名
        categoryId: '',
        // tasteList: [{ name: '' }],
        specList: '',
        attributes: 'foods',
        barcode: '',
        // isNewProduct: false,
        // newProductDate: [],
        isSupportReservation: false,
        saleStatus: 0,
        weightType: 1,
        foodImagesList: [],
        foodImages: [],
        extraImages: [],
        extraImagesList: [],
        countType: 1,
        foodPriceJson: [{
          spec: '默认',
          price: '',
          weight: ''
        }], //
        foodPrice: '',
        weightPrice: '',
        weight: '',
        startGram: '',
        faultRate: '',
        originPrice: '',
        packPrice: '',
        ingredientList: [
          {
            // weight: '',
            id: '',
            ingredient_id: '',
            // supplier_name: '',
            name: '',
            nutrition: {},
            selectFoodIngredient: [],
            percentage: 0 // 百分比
          }
        ],
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {}, // 带标签组名字的数据
        cookingType: '' // 烹饪方式
      },
      formFoodDataRuls: {
        foodName: [{ required: true, message: '请输入菜品名称', trigger: ['blur', 'change'] }],
        aliasName: [{ required: false, message: '请输入菜品别名', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
        attributes: [{ required: true, message: '请选择属性', trigger: 'blur' }],
        foodPrice: [
          {
            required: true,
            validator: validataPlateAmount,
            message: '请输入菜品菜品 / 商品价格',
            trigger: ['blur', 'change']
          }
        ],
        weightPrice: [
          {
            required: true,
            validator: validataPlateAmount,
            message: '请输入称重价格',
            trigger: ['blur', 'change']
          }
        ],
        weight: [{ required: true, message: '请输入称重重量', trigger: ['blur', 'change'] }],
        startGram: [{ required: true, message: '请输入起始计价重量', trigger: ['blur', 'change'] }],
        faultRate: [{ required: true, message: '请输入误差率', trigger: ['blur', 'change'] }],
        // originPrice: [
        //   {
        //     required: true,
        //     validator: validataPlateAmount,
        //     message: '请输入成本价格',
        //     trigger: 'blur'
        //   }
        // ],
        packPrice: [
          {
            required: false,
            validator: validataPlateAmountPrice,
            message: '请输入打包费',
            trigger: ['blur', 'change']
          }
        ],
        foodSpec: [{ required: true, message: '请输入', trigger: 'blur' }],
        foodPriceJson: [{ required: true, validator: valiPrice, trigger: 'blur' }],
        foodWeight: [{ validator: valiWeight, trigger: 'blur' }],
        foodImages: [{ required: true, validator: validatorImage, trigger: 'blur' }],
        cookingType: [{ required: true, message: '请选择烹饪方式', trigger: 'blur' }]
      },
      formFoodLoading: false,
      foodIngredientList: [],
      allSelectIngredient: [],
      // proportionIngredientList: [],
      tableDataNutrition: {},
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      uploadParams: {
        prefix: 'foodImage'
      },
      rulsNumder: /^\d+$/,
      deepFormIngredients: [],
      specLists: SPEC_LIST,
      errorMsg: { // 独立的form表单错误提示
        percentageError: '',
        nutritionError: ''
      },
      dialogImageUrl: '',
      dialogVisible: false,
      selectLaberDialogVisible: false,
      ruleSingleInfo: {
        isAdmin: true, // 是否获取平台标签
        labelType: 'food'
      },
      foodList: [], // 菜品列表
      filterFoodName: '', // 菜品筛选的输入文字
      isSelectFood: false, // 是否通过select选择了菜
      remoteLoading: false, // 远程筛选菜品的状态
      selectFood: null, // 下拉选中的菜品数据，记录下用于还原显示
      systemIngredientsList: [],
      systemIngredientsIdsObj: {},
      allIngredientsList: {},
      cookingTypeList: deepClone(COOKING_TYPE_LIST), // 烹饪方式列表
      labelList: [
        {
          id: -1,
          name: '平台标签',
          label_list: []
        },
        {
          id: -2,
          name: '自有标签',
          label_list: []
        }
      ], // 标签列表
      selectedTags: [], // 选中的标签
      nutritionList: deepClone(NUTRITION_LIST),
      percentageCount: 0 // 占比
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.showDialogMealFood
      },
      set(val) {
        this.$emit('update:showDialogMealFood', val)
      }
    }
  },
  async created() {
    await this.initLoad()
    // 原数据的食材
    if (this.showDialogMealFoodType === 'add') { // 新增的时候把别名放出来一个
      this.formFoodData.aliasName = ['']
    }
    this.deepFormIngredients = this.formFoodDataDialog.ingredients
  },
  mounted() {
    this.$nextTick(_ => {
      // document.querySelector('.dialog-form').addEventListener('scroll', this.scrollHandle)
    })
  },
  methods: {
    async initLoad() {
      // await this.getSystemIngredientList()
      // this.getFoodIngredientList()
      let systemIngredientsList = getSessionStorage('systemIngredientsList')
      let foodIngredientList = getSessionStorage('foodIngredientList')
      if (!systemIngredientsList || !foodIngredientList) {
        await this.getSystemIngredientList()
        this.getFoodIngredientList()
      } else {
        systemIngredientsList = JSON.parse(systemIngredientsList)
        foodIngredientList = JSON.parse(foodIngredientList)
        this.setSystemIngrendients(systemIngredientsList)
        this.setMerchantIngrendients(foodIngredientList)
      }
      this.getLabelGroupList(true)
      this.getLabelGroupList(false)
    },
    initFormFoodDataDialog(type, row) {
      let rowDeep = deepClone(row)
      this.filterFoodName = rowDeep.name
      this.formFoodData = {
        foodName: rowDeep.name,
        aliasName: rowDeep.alias_name && rowDeep.alias_name.length > 0 ? rowDeep.alias_name : [''],
        categoryId: rowDeep.category,
        attributes: rowDeep.attributes,
        barcode: rowDeep.barcode,
        // tasteList: rowDeep.taste_list.length ? rowDeep.taste_list : [{ name: '' }],
        specList: rowDeep.spec_list && rowDeep.spec_list.length ? rowDeep.spec_list[0].name : '',
        countType: 1,
        weightType: 1,
        foodPrice: '',
        weightPrice: '',
        weight: '',
        startGram: '',
        faultRate: '',
        originPrice: '',
        packPrice: '',
        foodImages: [],
        foodImagesList: [],
        extraImages: [],
        extraImagesList: [],
        nutrition_info: rowDeep.nutrition_info,
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {}, // 带标签组名字的数据
        ingredientList: [
          {
            weight: '',
            id: '',
            ingredient_id: '',
            // supplier_name: '',
            name: '',
            nutrition: {},
            selectFoodIngredient: deepClone(this.foodIngredientList),
            percentage: 0
          }
        ],
        foodPriceJson: [{
          spec: '默认',
          price: '',
          weight: ''
        }]
      }
      let keys = {
        weight_type: 'weightType',
        food_price: 'foodPrice',
        weight_price: 'weightPrice',
        weight: 'weight',
        start_gram: 'startGram',
        fault_rate: 'faultRate',
        origin_price: 'originPrice',
        pack_price: 'packPrice'
      }
      if (rowDeep.price_info) {
        // countType: rowDeep.price_info.count_type ? rowDeep.price_info.count_type : 1,
        // foodPrice: rowDeep.price_info.food_price,
        // weightPrice: rowDeep.price_info.weight_price,
        // weight: rowDeep.price_info.weight,
        // startGram: rowDeep.price_info.start_gram,
        // faultRate: rowDeep.price_info.fault_rate,
        // originPrice: rowDeep.price_info.origin_price,
        // packPrice: rowDeep.price_info.pack_price,
        this.setFormData(rowDeep.price_info, keys, this.formFoodData)
        this.$set(this.formFoodData, 'countType', rowDeep.price_info.count_type ? rowDeep.price_info.count_type : 1)
      } else {
        this.setFormData(rowDeep.price_info, keys, this.formFoodData)
        this.$set(this.formFoodData, 'countType', 1)
      }
      // 规格
      let specList = []
      if (rowDeep.spec_list && rowDeep.spec_list.length) {
        rowDeep.spec_list.forEach(v => {
          specList.push({
            spec: v.name,
            price: divide(v.food_price),
            weight: v.weight
          })
        })
      }
      if (specList.length > 0) {
        this.formFoodData.foodPriceJson = specList
      }
      // 展示图片
      if (rowDeep.image) {
        this.formFoodData.foodImagesList.push({
          url: rowDeep.image,
          name: rowDeep.image,
          status: "success",
          uid: rowDeep.image
        })
        this.formFoodData.foodImages = [rowDeep.image]
      }
      // 识别图片
      if (rowDeep.extra_image) {
        rowDeep.extra_image.forEach(item => {
          this.formFoodData.extraImagesList.push({
            url: item,
            name: item,
            status: "success",
            uid: item
          })
        })
        this.formFoodData.extraImages = rowDeep.extra_image
      }
      if (rowDeep.ingredients_list && rowDeep.ingredients_list.length) {
        this.formFoodData.ingredientList = rowDeep.ingredients_list.map(v => {
          let obj = {
            id: v.ingredient,
            ingredient_id: v.ingredient,
            name: v.ingredient_name,
            nutrition: this.foodIngredientNutrition(v),
            selectFoodIngredient: deepClone(this.foodIngredientList),
            percentage: v.ingredient_scale,
            ingredient_type: this.systemIngredientsIdsObj[v.ingredient] ? 'super' : '' // 加这个是为了后端去同步超管食材
          }
          return obj
        })
        this.isDisabledOtherMeal()
      }
      this.tableDataNutrition = {}
      if (!rowDeep.nutrition_info) rowDeep.nutrition_info = {}
      let element = rowDeep.nutrition_info.element ? JSON.parse(replaceSingleQuote(rowDeep.nutrition_info.element)) : {}
      let vitamin = rowDeep.nutrition_info.vitamin ? JSON.parse(replaceSingleQuote(rowDeep.nutrition_info.vitamin)) : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.tableDataNutrition, nutrition.key, rowDeep.nutrition_info[nutrition.key] ? rowDeep.nutrition_info[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.tableDataNutrition, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.tableDataNutrition, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
      //  回显示标签组名字 {'aa':[{xx:xx}]}
      if (rowDeep.label && rowDeep.label.length) {
        // 格式化标签
        this.initLabelGroup(rowDeep.label)
        this.formFoodData.selectLabelListData = rowDeep.label
        this.formFoodData.selectLabelIdList = rowDeep.label.map(v => { return v.id })
        this.selectedTags = rowDeep.label
      }
      // 标签
      // 烹饪方式
      this.$set(this.formFoodData, 'cookingType', rowDeep.cooking_manner)
      // return params
    },
    // 设置formData的数据
    setFormData(data, keyObj, form) {
      // keyObj 对应的字段 { count_type: 'countType' }
      if (data) {
        Object.keys(keyObj).forEach(key => {
          // 有数据
          if (data[key]) {
            this.$set(form, keyObj[key], data[key])
          } else {
            this.$set(form, keyObj[key], '')
          }
        })
      }
    },
    foodIngredientNutrition(v) {
      let obj = {}
      // this.foodIngredientList.map(item => {
      //   if (item.id === v.ingredient) {
      //     obj = item.nutrition
      //   }
      // })
      if (this.allIngredientsList[v.ingredient]) {
        obj = this.allIngredientsList[v.ingredient].nutrition
      }
      return obj
    },
    cancel() {
      this.showDialog = false
    },
    deleteFunList(type, index, row) {
      if (type === 'taste') {
        // if (this.showDialogMealFoodType === 'edit' && row.id) {
        //   // 没有这接口了啊，很奇怪
        //   this.setFoodDeleteTaste(row)
        // } else {
        this.formFoodData.tasteList.splice(index, 1)
        // }
      } else if (type === 'food') {
        // if (this.showDialogMealFoodType === 'edit' && row.id) {
        //   this.setFoodDeleteIngredient(row)
        // } else {
        this.formFoodData.ingredientList.splice(index, 1)
        this.isDisabledOtherMeal()
        this.computedNutritionAndPercentage()
        this.changePercentage()
        // }
      } else if (type === 'spec') {
        if (this.showDialogMealFoodType === 'edit' && row.id) {
          this.setFoodDeleteSpec(row)
        } else {
          this.formFoodData.specList.splice(index, 1)
        }
      }
    },
    addFunList(type, row) {
      if (type === 'taste') {
        this.formFoodData.tasteList.push({
          name: ''
        })
      } else if (type === 'food') {
        this.formFoodData.ingredientList.push({
          weight: '',
          id: '',
          ingredient_id: '',
          // supplier_name: '',
          name: '',
          nutrition: {},
          selectFoodIngredient: deepClone(this.foodIngredientList),
          percentage: 0
        })
        this.isDisabledOtherMeal()
      } else if (type === 'spec') {
        this.formFoodData.specList.push({
          name: ''
        })
      }
    },
    // 新增口味
    async setFoodAddTaste(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodAddTastePost({
          food_id: this.formFoodDataDialog.id,
          name: row.name
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success(res.msg)
        // this.confirm()
        this.$emit('confirm', 'search')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除口味
    async setFoodDeleteTaste(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodDeleteTastePost({
          ids: [row.id]
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增规格
    async setFoodAddSpec(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodAddSpecPost({
          food_id: this.formFoodDataDialog.id,
          name: row.name
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除规格
    async setFoodDeleteSpec(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodDeleteSpecPost({
          ids: [row.id]
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    labelClick() {
      this.ruleSingleInfo = {
        isAdmin: true,
        labelType: 'food',
        selectLabelIdList: this.formFoodData.selectLabelIdList,
        selectLabelListData: this.formFoodData.selectLabelListData
      }
      this.selectLaberDialogVisible = true
    },
    closeTag(key, index, item) {
      // 删除
      let idx = this.formFoodData.selectLabelIdList.indexOf(item.id)
      let ids = this.formFoodData.selectLabelListData.indexOf(item)
      this.formFoodData.selectLabelIdList.splice(idx, 1)
      this.formFoodData.selectLabelListData.splice(ids, 1)
      // 重置数据
      this.formFoodData.labelGroupInfoList = {}
      this.initLabelGroup(this.formFoodData.selectLabelListData)
    },
    // 选择标签
    selectLaberData(params) {
      this.formFoodData.selectLabelIdList = params.selectLabelIdList
      this.formFoodData.selectLabelListData = params.selectLabelListData
      this.formFoodData.labelGroupInfoList = {}
      this.initLabelGroup(this.formFoodData.selectLabelListData)
    },
    // 新增食材
    async setFoodAddIngredient(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodAddIngredientPost({
          food_id: this.formFoodDataDialog.id,
          ingredient_id: row.ingredient_id,
          weight: row.weight
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除食材
    async setFoodDeleteIngredient(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodDeleteIngredientPost({
          ids: [row.id],
          food_id: this.formFoodDataDialog.id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 系统食材库列表
    async getSystemIngredientList() {
      this.foodIngredientList = []
      this.systemIngredientsIdsObj = {}
      this.formFoodLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSystemIngredientPost({
          is_copy: 'all',
          page: 1,
          page_size: 99999
        })
      )
      this.formFoodLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        let results = data.results || []
        sessionStorage.setItem('systemIngredientsList', results ? JSON.stringify(results) : '[]')
        this.setSystemIngrendients(results)
      } else {
        // this.$message.error(res.msg)
      }
    },
    setSystemIngrendients(data) {
      console.log("setSystemIngrendients", data)
      if (data) {
        this.foodIngredientList = []
        this.systemIngredientsIdsObj = {}
        this.systemIngredientsList = data.map(v => {
          let item = {
            ingredient_type: 'system',
            ingredient_type_alias: '系统',
            id: v.id,
            name: v.name
          }
          this.systemIngredientsIdsObj[v.id] = true
          v.ingredient_type = 'system'
          v.ingredient_type_alias = '系统'
          this.allIngredientsList[v.id] = v
          return item
        })
      }
    },
    // 食材管理列表
    async getFoodIngredientList() {
      this.formFoodLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientIngredientNamePost({
          page: 1,
          page_size: 99999
        })
      )
      this.formFoodLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        sessionStorage.setItem('foodIngredientList', res.data ? JSON.stringify(res.data) : '[]')
        this.setMerchantIngrendients(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    setMerchantIngrendients(data) {
      if (data) {
        let result = data.map(v => {
          this.allIngredientsList[v.id] = v
          return {
            id: v.id,
            name: v.name
          }
        })
        Object.freeze(this.allIngredientsList)
        this.foodIngredientList = result.concat(this.systemIngredientsList).map((v, index) => {
          return {
            ...v,
            index: index + 1
          }
        })
        this.systemIngredientsList = [] // 清下不需要的数据
        // this.foodIngredientList = res.data.concat(this.systemIngredientsList)
        this.formFoodData.ingredientList[0].selectFoodIngredient = deepClone(this.foodIngredientList)
        if (this.showDialogMealFoodType === 'edit') {
          this.initFormFoodDataDialog(
            this.showDialogMealFoodType,
            this.formFoodDataDialog
          )
        }
        if (this.type === 'edit') {
          this.changePercentage()
        }
        this.isDisabledOtherMeal()
      }
    },
    // 新增菜品
    async setFoodAdd(params) {
      this.formFoodLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodFoodAddPost(params))
      this.formFoodLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.foodCategoryList = res.data.results
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改菜品
    async setFoodEdit(params) {
      this.formFoodLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodFoodModifyPost(params))
      this.formFoodLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.foodCategoryList = res.data.results
        this.showDialog = false
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm(this.showDialogMealFoodType)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量修改菜品
    async setFoodBatchEdit(params) {
      this.formFoodLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodFoodBatchModifyPost(params))
      this.formFoodLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.foodCategoryList = res.data.results
        this.showDialog = false
        this.$emit('confirm', 'search')
        // this.confirm(this.showDialogMealFoodType)
      } else {
        this.$message.error(res.msg)
      }
    },
    initAddAndEditFood() {
      let params = {
        name: this.formFoodData.foodName, // 菜品名字
        alias_name: this.formFoodData.aliasName, // 菜品别名
        category_id: this.formFoodData.categoryId, // 二级分类id
        taste_list: [], // 口味['name','xxx']
        spec_list: [this.formFoodData.specList], // 规格 ['name','xxx']
        attributes: this.formFoodData.attributes, // 属性
        // is_new_product: this.formFoodData.isNewProduct, // 是否上新品
        // is_support_reservation: this.formFoodData.isSupportReservation, // 是否支持预约
        // sale_status: this.formFoodData.saleStatus, // 上下架状态
        // formFoodData.imageList的第一张图片传给image，剩余的传给extra_image，因为图片列表是分两个字段上传的，为了兼容原来只有一张图片的情况
        image: this.formFoodData.foodImages[0], // 菜品主图
        extra_image: this.formFoodData.extraImages, // 菜品其他图片
        count_type: this.formFoodData.countType, // 价格信息 计费方式
        weight_type: this.formFoodData.weightType, // 称重方式 按克 按份  因为下拉选择菜品 weight_type是空 不能传空字符串 加多个判断
        food_price: this.formFoodData.foodPrice ? times(this.formFoodData.foodPrice) : 0, // 菜品价格
        weight_price: this.formFoodData.weightPrice ? times(this.formFoodData.weightPrice) : 0, // 称重价格
        weight: this.formFoodData.weight ? this.formFoodData.weight : 0, // 称重重量
        start_gram: this.formFoodData.startGram ? this.formFoodData.startGram : 0, // 起始计价重量(克)
        fault_rate: this.formFoodData.faultRate ? this.formFoodData.faultRate : 0, // 误差率
        origin_price: this.formFoodData.originPrice ? times(this.formFoodData.originPrice) : 0, // 成本价格
        pack_price: this.formFoodData.packPrice ? times(this.formFoodData.packPrice) : 0, // 打包费
        device_picture_list: [], // 暂无
        ingredient_list: [], // 食材组成
        nutrition_info: {}, // 营养
        label_list: this.formFoodData.selectLabelIdList,
        cooking_manner: this.formFoodData.cookingType
      }
      if (this.formFoodData.aliasName && this.formFoodData.aliasName.toString() !== '') {
        params.alias_name = this.formFoodData.aliasName.filter(name => name.trim())
      } else {
        delete params.alias_name
      }
      if (this.formFoodData.countType === 1) {
        let list = deepClone(this.formFoodData.foodPriceJson)
        params.food_price_json = list.map(v => {
          v.price = times(v.price)
          return v
        })
      }
      if (this.formFoodData.attributes === 'goods') {
        params.barcode = this.formFoodData.barcode // 条形码
      }
      // if (this.formFoodData.newProductDate.length > 0) {
      //   params.new_start_date = this.formFoodData.newProductDate[0] // 新品开始时间
      //   params.new_end_date = this.formFoodData.newProductDate[1] // 新品结束时间
      // }
      // if (this.formFoodData.tasteList[0].name) {
      //   params.taste_list = this.formFoodData.tasteList.map(v => {
      //     return v.name
      //   })
      // } else {
      //   params.taste_list = []
      // }
      if (this.formFoodData.ingredientList && this.formFoodData.ingredientList.length) {
        this.formFoodData.ingredientList.map(v => {
          if (v.id) {
            let obj = {
              ingredient_id: v.id,
              // name: v.name,
              ingredient_scale: v.percentage,
              ingredient_type: v.ingredient_type
            }
            params.ingredient_list.push(obj)
          }
        })
      }
      // 营养
      let element = {}
      let vitamin = {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          params.nutrition_info[nutrition.key] = this.tableDataNutrition[nutrition.key]
        }
        if (nutrition.type === 'element') {
          element[nutrition.key] = this.tableDataNutrition[nutrition.key]
        }
        if (nutrition.type === 'vitamin') {
          vitamin[nutrition.key] = this.tableDataNutrition[nutrition.key]
        }
      })
      params.nutrition_info.element = JSON.stringify(element)
      params.nutrition_info.vitamin = JSON.stringify(vitamin)
      return params
    },
    determineDialogFood() {
      this.$refs.formFoodData.validate(valid => {
        // 校验营养的格式
        // let validateNuturition = false
        // this.$refs.nutritionRef.$refs.formIngredients.validate(va => {
        //   validateNuturition = va
        // })
        if (valid && !this.errorMsg.percentageError) {
          // 添加判断，食材占比中食材不能为空
          if (this.checkIngredientList(this.formFoodData.ingredientList)) {
            return
          }
          if (this.showDialogMealFoodType === 'add') {
            // 添加
            this.setFoodAdd(this.initAddAndEditFood())
            // this.initAddAndEditFood()
          } else if (this.showDialogMealFoodType === 'edit') {
            // 编辑
            this.setFoodEdit({
              id: this.formFoodDataDialog.id,
              no: this.formFoodDataDialog.no,
              ...this.initAddAndEditFood()
            })
          } else if (this.showDialogMealFoodType === 'batchAdd') {
            this.setFoodBatchEdit({
              ids: this.selectListId,
              no: this.formFoodDataDialog.no,
              ...this.initAddAndEditFood()
            })
          }
          // this.setFoodAdd()
        } else {
          console.log('error submit!!', this.$refs.formFoodData)
          this.$message.error('请认真检查表单数据！')
          return false
        }
      })
    },
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    handleFoodImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formFoodData.foodImagesList = fileList
        this.formFoodData.foodImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExtraImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formFoodData.extraImagesList = fileList
        this.formFoodData.extraImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleFoodImgRemove(file, type) {
      let index = this.formFoodData[type + 'List'].findIndex(item => item.url === file.url)
      this.formFoodData[type].splice(index, 1)
      this.formFoodData[type + 'List'].splice(index, 1)
    },
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是JPG/BMP/PNG格式!')
        return false
      }

      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    },
    // foodEquipmentOnRemove(index) {
    //   this.formFoodData.fileListFood.splice(index, 1)
    // }
    changeIngredient(val) {
      let obj = this.allIngredientsList[val.id]
      // this.foodIngredientList.map(item => {
      //   if (item.id === val) {
      //     obj = item
      //   }
      // })
      this.formFoodData.ingredientList.forEach(item => {
        if (item.ingredient_id === obj.id) {
          if (obj.id) {
            // item.supplier_name = obj.supplier_name
            item.name = obj.name
            item.nutrition = obj.nutrition
            item.id = obj.id
            item.ingredient_type = obj.ingredient_type
          } else {
            // item.supplier_name = ''
            item.name = ''
          }
        }
      })
      this.isDisabledOtherMeal()
      this.computedNutritionAndPercentage(true)
      this.changePercentage()
    },
    // 计算营养和食材占比
    computedNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      let percentageCount = 0
      this.formFoodData.ingredientList.map((v, index) => {
        let nutrition = {}
        if (v.ingredient_id) {
          // 计算食材占比 按100克计算
          // 计算食材占比 按100克计算
          if (index < this.formFoodData.ingredientList.length - 1) {
            v.percentage = parseInt(NP.divide(100, this.formFoodData.ingredientList.length))
            percentageCount = NP.plus(v.percentage, percentageCount)
          } else {
            v.percentage = parseInt(NP.minus(100, percentageCount))
          }
          const percentage = v.percentage / 100
          if (this.allIngredientsList[v.ingredient_id] && this.allIngredientsList[v.ingredient_id].nutrition) {
            nutrition = this.allIngredientsList[v.ingredient_id].nutrition
          }
          // objNutrition.energy_mj = +nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +nutrition.protein ? NP.plus(objNutrition.protein, nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +nutrition.axunge ? NP.plus(objNutrition.axunge, nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          objNutrition.cholesterol = +nutrition.cholesterol ? NP.plus(objNutrition.cholesterol, nutrition.cholesterol * percentage) : objNutrition.cholesterol ? objNutrition.cholesterol : 0
          objNutrition.dietary_fiber = +nutrition.dietary_fiber ? NP.plus(objNutrition.dietary_fiber, nutrition.dietary_fiber * percentage) : objNutrition.dietary_fiber ? objNutrition.dietary_fiber : 0
          if (nutrition.element && nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
          if (this.deepFormIngredients && this.deepFormIngredients.length) {
            this.deepFormIngredients.forEach(item => {
              if (item.id === v.id) {
                v.status = true
              }
            })
          }
        }
      })
      // this.tableDataNutrition = objNutrition
      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.tableDataNutrition, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.tableDataNutrition, item.key, objNutrition[item.key])
        }
      })
    },
    // 计算营养和食材占比
    setNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      this.formFoodData.ingredientList.map((v, index) => {
        if (v.ingredient_id) {
          let nutrition = {}
          // 计算食材占比 按100克计算
          if (this.allIngredientsList[v.ingredient_id] && this.allIngredientsList[v.ingredient_id].nutrition) {
            nutrition = this.allIngredientsList[v.ingredient_id].nutrition
          }
          const percentage = v.percentage / 100
          // objNutrition.energy_mj = +nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = nutrition.protein ? NP.plus(objNutrition.protein, nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = nutrition.axunge ? NP.plus(objNutrition.axunge, nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          objNutrition.cholesterol = nutrition.cholesterol ? NP.plus(objNutrition.cholesterol, nutrition.cholesterol * percentage) : objNutrition.cholesterol ? objNutrition.cholesterol : 0
          objNutrition.dietary_fiber = nutrition.dietary_fiber ? NP.plus(objNutrition.dietary_fiber, nutrition.dietary_fiber * percentage) : objNutrition.dietary_fiber ? objNutrition.dietary_fiber : 0
          if (nutrition.element && nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
        }
      })

      NUTRITION_LIST.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.tableDataNutrition, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.tableDataNutrition, item.key, objNutrition[item.key])
        }
      })
    },
    // 食材占比
    changePercentage(e) {
      this.setNutritionAndPercentage()
      if (!this.formFoodData.ingredientList || this.formFoodData.ingredientList.length === 0) {
        this.errorMsg.percentageError = ''
        this.percentageCount = 0
        return
      }
      let countList = this.formFoodData.ingredientList.filter(item => {
        return item.percentage !== 0
      })
      if (countList.length === 0) {
        this.errorMsg.percentageError = ''
        this.percentageCount = 0
        return
      }
      let percentageCount = this.formFoodData.ingredientList.reduce((total, current) => {
        return NP.plus(current.percentage, total)
      }, 0)
      this.percentageCount = percentageCount
      if (percentageCount > 100 || percentageCount < 100) {
        this.errorMsg.percentageError = '菜品每100g所含食材占比，相加必须等于100%'
      } else {
        this.errorMsg.percentageError = ''
      }
    },
    // 设置餐段disabled
    isDisabledOtherMeal() {
      this.allSelectIngredient = []
      if (this.formFoodData.ingredientList && this.formFoodData.ingredientList.length) {
        this.formFoodData.ingredientList.map((item, k) => {
          if (item.ingredient_id) {
            this.allSelectIngredient.push(item.ingredient_id)
          }
        })
        this.formFoodData.ingredientList.forEach((v, k) => {
          v.selectFoodIngredient.forEach(item => {
            if (this.allSelectIngredient.includes(item.id) && v.ingredient_id !== item.id) {
              item.disabled = true
            } else {
              item.disabled = false
            }
          })
        })
      }
    },
    // 清空价格信息
    initCountData() {
      this.formFoodData.foodPrice = ''
      this.formFoodData.weightPrice = ''
      this.formFoodData.weight = ''
      this.formFoodData.startGram = ''
      this.formFoodData.faultRate = ''
      this.formFoodData.originPrice = ''
      this.formFoodData.packPrice = ''
    },
    changeCount() {
      this.initCountData()
    },
    changeWeightType() {
      this.initCountData()
    },
    changeNewProduct() {
      // 初始化日期
      // if (!this.formFoodData.isNewProduct) {
      //   this.formFoodData.newProductDate = []
      // }
    },
    // 规格添加
    addFoodPriceJson() {
      this.formFoodData.foodPriceJson.push({
        spec: '',
        price: '',
        weight: ''
      })
    },
    // 规格删除
    deleteFoodPriceJson(i) {
      this.formFoodData.foodPriceJson.splice(i, 1)
    },
    // 添加菜品别名
    addFoodAliasName() {
      this.formFoodData.aliasName.push('')
    },
    delFoodAliasName(index) {
      this.formFoodData.aliasName.splice(index, 1);
    },
    tabClick() {
      this.ruleSingleInfo.isAdmin = !this.ruleSingleInfo.isAdmin
      this.$refs.selectLaber.currentPage = 1
      this.$refs.selectLaber.getLabelGroupList(true)
      this.$refs.selectLaber.getLabelGroupList(false)
    },
    // 格式化标签
    initLabelGroup(data) {
      data.forEach(v => {
        if (!this.formFoodData.labelGroupInfoList[v.label_group_name]) {
          this.formFoodData.labelGroupInfoList[v.label_group_name] = []
        }
        if (this.formFoodData.labelGroupInfoList[v.label_group_name] && !this.formFoodData.labelGroupInfoList[v.label_group_name].includes(v)) {
          this.formFoodData.labelGroupInfoList[v.label_group_name].push(v)
        }
      })
    },
    // 选中菜品
    changeFoodHandle(e) {
      this.isSelectFood = true
      this.selectFood = this.foodList.find(item => item.id === e)
      this.filterFoodName = this.selectFood.name
      this.initFormFoodDataDialog(
        this.showDialogMealFoodType,
        this.selectFood
      )
    },
    // 请除菜品输入/选中
    clearFoodHandle(e) {
      this.foodList = []
      this.isSelectFood = false
    },
    // select下拉框显示or隐藏事件
    visibleChangeFoodHandle(e) {
      if (!e) {
        if (!this.isSelectFood) {
          this.formFoodData.foodName = this.filterFoodName
        }
        return
      }
      // 初始化filter的数据，设置高亮需要
      this.$nextTick(_ => {
        if (this.filterFoodName && !this.isSelectFood) {
          this.$refs.foodNameRef.query = this.filterFoodName
          this.$refs.foodNameRef.selectedLabel = this.filterFoodName
          // 显示时需要手动触发下filter
          this.$refs.foodNameRef.handleQueryChange(this.filterFoodName)
          // this.$refs.foodNameRef.remoteMethod(this.filterFoodName)
        }
      })
    },
    // 获取焦点时
    focusFoodHandle(e) {
      this.$nextTick(_ => {
        setTimeout(() => {
          this.$refs.foodNameRef.query = this.filterFoodName
          this.$refs.foodNameRef.selectedLabel = this.filterFoodName
        }, 70)
      })
    },
    // 菜品远程搜索
    async getSystemFoodList(e) {
      // 将搜索的值赋给foodName
      // 当上一次搜索的关键字和本次的相同时跳过重新请求数据
      if (e && (e === this.filterFoodName)) return
      this.filterFoodName = e
      if (!e) {
        this.$refs.foodNameRef.query = ''
        this.$refs.foodNameRef.selectedLabel = ''
        if (!this.isSelectFood) {
          this.formFoodData.foodName = e
        }
        this.foodList = []
        return
      } else {
        this.formFoodData.foodName = e
      }
      this.remoteLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockListPost({
          food_name: e
        })
      )
      this.remoteLoading = false
      if (err) {
        this.$message.error(err.message)
        this.foodList = []
        return
      }
      if (res.code === 0) {
        // this.foodList = res.data.results.map(item => {
        //   // 新开个字段给需要高亮的处理，防止污染旧数据
        //   item.highlight_text = item.name
        //   return this.setHightLight(item, e)
        // })
        if (res.data.results.length !== 0) {
          this.foodList = []
          res.data.results.map(v => {
            v.highlight_text = v.name
            this.foodList.push(this.setHightLight(v, e))
            // 有别名
            if (v.alias_name && v.alias_name.length) {
              v.alias_name.map((alias, index) => {
                let item = deepClone(v)
                item.id = item.id + new Date().getTime() + index // 防止id重复
                item.original_id = item.id
                item.name = alias
                item.alias_name = [] // 有别名的要清空掉
                item.highlight_text = item.name
                this.foodList.push(this.setHightLight(item, e))
              })
            }
          })
        } else {
          this.getSystemFoodListAgain(e)
        }
      } else {
        this.$message.error(res.msg)
        this.foodList = []
      }
    },
    // 设置文字高亮
    setHightLight(item, keyword) {
      let reg = new RegExp(keyword, "g");
      let replaceString = `<span style="color: #FF9B45;">${keyword}</span>`;
      if (item.highlight_text.match(reg)) {
        item.highlight_text = item.highlight_text.replace(reg, replaceString);
        return item;
      }
      return item;
    },
    async getSystemFoodListAgain(e) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockListPost({
          food_name: e,
          is_pair: true
        })
      )
      if (err) {
        this.$message.error(err.message)
        this.foodList = []
        return
      }
      if (res.code === 0) {
        this.foodList = []
        if (res.data.results.length) {
          res.data.results.map(v => {
            this.formFoodData.ingredientList = res.data.results.map(v => {
              let obj = {
                id: v.ingredient_id,
                ingredient_id: v.ingredient_id,
                name: v.ingredient_name,
                nutrition: this.foodIngredientNutrition(v),
                selectFoodIngredient: deepClone(this.foodIngredientList),
                percentage: v.ingredient_scale,
                ingredient_type: this.systemIngredientsIdsObj[v.ingredient] ? 'super' : ''
              }
              return obj
            })
          })
          // 计算营养信息
          this.formFoodData.ingredientList.forEach(v => {
            let obj = this.allIngredientsList[v.id]
            this.formFoodData.ingredientList.forEach(item => {
              if (item.ingredient_id === obj.id) {
                if (obj.id) {
                  // item.supplier_name = obj.supplier_name
                  item.name = obj.name
                  item.nutrition = obj.nutrition
                  item.id = obj.id
                  item.ingredient_type = obj.ingredient_type
                } else {
                  // item.supplier_name = ''
                  item.name = ''
                }
              }
            })
            this.computedNutritionAndPercentage(true)
          })
        }
      } else {
        this.$message.error(res.msg)
        this.foodList = []
      }
    },
    // 烹饪方式选择
    handleCookingTypeChange(item) {
      this.$set(this.formFoodData, 'cookingType', item.value)
      // 手动触发校验
      this.$nextTick(() => {
        this.$refs.formFoodData && this.$refs.formFoodData.validateField('cookingType')
      })
      console.log('item', item, this.formFoodData)
    },
    // 处理标签选择变化
    handleLabelChange(values) {
      // 清空已选标签
      this.selectedTags = []

      // 递归查找第三级标签
      const findThirdLevelLabel = (labelId) => {
        for (const group of this.labelList) {
          if (group.label_list && group.label_list.length > 0) {
            for (const second of group.label_list) {
              if (second.label_list && second.label_list.length > 0) {
                for (const third of second.label_list) {
                  if (third.id === labelId) {
                    return {
                      id: third.id,
                      name: third.name,
                      parentId: second.id,
                      parentName: second.name,
                      grandParentId: group.id,
                      grandParentName: group.name
                    }
                  }
                }
              }
            }
          }
        }
        return null
      }

      // 处理选中的值
      values.forEach(labelId => {
        const foundLabel = findThirdLevelLabel(labelId)
        if (foundLabel && !this.selectedTags.some(tag => tag.id === foundLabel.id)) {
          this.selectedTags.push(foundLabel)
        }
      })

      // 更新表单数据
      this.formFoodData.selectLabelListData = this.selectedTags
      console.log('this.selectedTags', this.selectedTags)
    },
    // 移除标签
    handleRemoveTag(tag) {
      this.selectedTags = this.selectedTags.filter(item => item.id !== tag.id)
      this.formFoodData.selectLabelIdList = this.formFoodData.selectLabelIdList.filter(value => value !== tag.id)
      this.formFoodData.selectLabelListData = this.selectedTags
    },
    // 获取标签
    async getLabelGroupList(isAdmin) {
      this.isLoading = true
      let params = {
        is_admin: isAdmin || false,
        type: 'food',
        page: 1,
        page_size: 9999
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        if (isAdmin) {
          this.labelList[0].label_list = deepClone(results || [])
        } else {
          this.labelList[1].label_list = deepClone(results || [])
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 检测食材列表
    checkIngredientList(list) {
      if (!list || list.length === 0) {
        return false
      }
      let flag = false
      for (let i = 0; i < list.length; i++) {
        if (list[i].percentage !== 0 && !list[i].ingredient_id) {
          flag = true
          this.$message.error('食材占比，请选择食材！')
          break
        }
      }
      return flag
    }
  }
}
</script>

<style lang='scss'>
.ps-el-drawer {
  .msg-tips{
    font-size: 12px;
    color: #999999;
  }
  .title-tips{
    position: absolute;
    top: 26px;
    left: 75px;
  }
  .add-and-edit-meal-food {

    .white{
      color: white;
    }
    .origin-text{
      color: #FF9B45;
    }
    .align-left{
      .el-form-item__label{
        text-align: left;
      }
    }
    .text-tips {
      line-height: 1.2;
      position: absolute;
      bottom: -50px;
      font-size: 10px;
    }
    .el-form-item .el-form-item {
      margin-bottom: 22px;
    }
    .input {
      width: 130px;
      padding-bottom: 10px;
    }
    .padding-right {
      padding-right: 20px;
    }
    .margin-right {
      margin-right: 10px;
    }
    .food-make-box {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;
    }
    .food-proportion-wrapper{
      width: 600px;
      // margin-top: 5px;
    }
    .food-proportion-box {
      display: flex;
      align-items: center;
      .content-tag {
          padding: 10px 0;
        }
      .cantent {
        flex: 1;
        text-align: center;
        // color: #fff;
      }
      .border-top {
        border-top: 1px solid #DCDFE6;
      }
      .border-bottom {
        border-bottom: 1px solid #DCDFE6;
      }
      .border-left {
        border-left: 1px solid #DCDFE6;
      }
      .border-right {
        border-right: 1px solid #DCDFE6;
      }
      .food {
        background-color: #DCDFE6;
        padding: 10px 0;
      }
      .proportion {
        background-color: #DCDFE6;
        padding: 10px 0;
      }
      .tools{
        background-color: #DCDFE6;
        padding: 10px 0;
      }
    }
    .form-ul{
      // margin-left: 65px;
      margin-top: 15px;
      margin-bottom: 15px;
      display: inline-block;
      border: 1px solid #DCDFE6;
      vertical-align: top;
      .form-li-box{
        position: relative;
        &:not(:last-child) {
          border-bottom: 1px solid #DCDFE6;
        }
        .fix-icon{
          display: inline-block;
          width: 42px;
          // position: absolute;
          // text-align: left;
          // right: -50px;
          // top: 10px;
        }
      }
      .form-li{
        width: 200px;
        text-align: center;
      }
      .from-bg-gray {
        background-color: #DCDFE6;
      }
      .li-center{
        border-left: 1px solid #DCDFE6;
        border-right: 1px solid #DCDFE6;
      }
      .el-form-item{
        margin-bottom: 0;
        .el-input__inner{
          border: none;
        }
        &.is-error {
          .el-input__inner{
            border: 1px solid #F56C6C;
          }
        }
      }
    }
    .li-icon{
      // margin-right: 20px;
      font-size: 20px;
      cursor: pointer;
      color: #fda04d;
      vertical-align: middle;
    }
    .add-btn-img{
      width:25px;
      height:25px;
      margin:3px 0 0 10px;
    }
    .food-alias-name-form{
      // display: block!important;
      margin-bottom: 0px!important;
      .el-form-item__content{
        display: flex;
        width: 500px;
        img{
          width: 25px;
          height: 25px;
          margin: 5px 0 5px 10px;
        }
      }
    }
  }
  .fun-box-weight {
    width: 190px;
    margin: 0 10px 10px 0;
  }
  .food-img-uploader {
    cursor: pointer;
    position: relative;
    .food-img-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 140px;
      height: 140px;
      line-height: 140px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
    }
    .food-img-wrap{
      display: flex;
      flex-wrap: wrap;
      .food-img {
        width: 140px;
        height: 140px;
        display: block;
        // border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin: 0 5px 10px 0;
      }
    }
  }
  .food-img-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .add-form-wrapper {
    display: flex;
    .add-form-item-wrapper {
      width: 300px;
      .form-item-wrapper {
        display: flex;
        color: #606266;
        position: relative;
      }
      .stockInput .el-input__suffix .el-input__validateIcon {
        display: none !important;
      }
    }
  }
  .upload-food{
    overflow: hidden;
    max-height: 830px;
    // .el-upload--picture-card {
    //   width: 224px !important;
    //   height: 142px !important;
    // }
    // .el-upload-list--picture-card .el-upload-list__item{
    //   width: 224px !important;
    //   height: 142px !important;
    // }
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
    }
    .el-upload-dragger{
      width: 146px;
      height: 146px;
    }
    .upload-food-img{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 146px;
      height: 146px;
      img{
        width: 146px;
        height: 146px;
        object-fit: cover;
      }
    }
  }

  .equipment-upload {
    .list__item {
      display: flex;
      flex-wrap: wrap;
      margin: 20px;
    }

    .img-box {
      position: relative;
      .img {
        width: 130px;
        height: 100px;
        margin: 0 10px;
      }
    }
    .delete-icon {
      position: absolute;
      top: -15px;
      right: 0px;
      font-size: 20px;
      color: red;
    }
  }
  .ps-meal-footer {
    position: absolute;
    right: 0;
    left: 0;
    height: 60px;
    bottom: 0;
    background-color: #fff;
  }
  .upload-placeholder {
    width: 146px;
    height: 146px !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f5f7fa;
    .el-icon-circle-plus {
      font-size: 30px;
      color: #ff9b45;
    }
    .upload-icon {
      line-height: 30px;
      height: 30px;
    }

    .upload-text {
      margin-top: 8px;
      font-size: 12px;
      color: #8c939d;
      line-height: 12px;
      height: 12px;
    }
    .dis-content{
      display: contents;
    }
  }
  .cooking-type-item {
    min-width: 60px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: solid 1px #DCDFE6;
    border-radius: 4px;
    color: #54585c;
    cursor: pointer;

    &.active {
      background-color: #ff9b45;
      color: #fff;
      border: none;
    }
  }
  .selected-tags {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 800px;

    .tag-item {
      background-color: #ff9b51;
      color: #fff;
      border: none;
      margin-left: 0;

      .el-tag__close {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
        }
      }
    }
  }
  .row-between {
    justify-content: space-between;
  }
  .row-center {
    justify-content: center;
  }
  .w-200 {
    width: 200px;
  }
  .w-600 {
    width: 600px;
  }
  .nutrition-item {
    flex: 0 0 33.33%;
    max-width: 33.33%;
    box-sizing: border-box;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
  }
  .nutrition-label {
    white-space: nowrap;
    margin-right: 4px;
  }
  .nutrition-value {
    word-break: break-all;
  }
}
</style>
