<template>
  <div class="group-summary container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="export" @click="handleExport" v-permission="['background_order.order_report_meal.group_collect_list_export']">导出Excel</button-icon>
          <!-- <button-icon color="plain">打印</button-icon> -->
          <!-- <button-icon color="plain">报表设置</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
          :span-method="objectSpanMethod"
        >
          <table-column  v-for="item in tableSetting" :key="item.key" :col="item">
          </table-column>
          <!-- <template v-for="item in tableSetting">
            <el-table-column :key="item.key" :label="item.label" :prop="item.key">
              <template v-if="item.children">
                <template v-for="child in item.children">
                  <el-table-column
                    :key="child.key"
                    :label="child.label"
                    :prop="child.key"
                  ></el-table-column>
                </template>
              </template>
            </el-table-column>
          </template> -->
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, getSevenDateRange, divide } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'DepMealReport',
  mixins: [exportExcel],
  // mixins: [activatedLoadData],
  data() {
    const defaultdate = getSevenDateRange(7)
    return {
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '时间',
          value: 'create_time',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '报餐时间',
              value: 'report_date'
            },
            {
              label: '用餐时间',
              value: 'dining_time'
            }
          ]
        },
        select_date: {
          clearable: false,
          type: 'daterange',
          value: [defaultdate[0], defaultdate[1]]
        },
        dep_id: {
          type: 'departmentSelect',
          multiple: false,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        },
        name: {
          type: 'input',
          value: '',
          label: '姓名',
          placeholder: '请输入姓名'
        }
      },
      tableData: [],
      tableSetting: [
        { key: 'dep_name', label: '部门' },
        { key: 'name', label: '姓名' },
        {
          key: 'breakfast',
          label: '早餐',
          children: [
            { key: 'breakfast_fee', label: '订单金额' },
            { key: 'breakfast_total_fee', label: '实际支付金额' },
            { key: 'breakfast_count', label: '消费笔数' },
            { key: 'breakfast_report_count', label: '消费份数' }
          ]
        },
        {
          key: 'lunch',
          label: '午餐',
          children: [
            { key: 'lunch_fee', label: '订单金额' },
            { key: 'lunch_total_fee', label: '实际支付金额' },
            { key: 'lunch_count', label: '消费笔数' },
            { key: 'lunch_report_count', label: '消费份数' }
          ]
        },
        {
          key: 'afternoon',
          label: '下午茶',
          children: [
            { key: 'afternoon_fee', label: '订单金额' },
            { key: 'afternoon_total_fee', label: '实际支付金额' },
            { key: 'afternoon_count', label: '消费笔数' },
            { key: 'afternoon_report_count', label: '消费份数' }
          ]
        },
        {
          key: 'dinner',
          label: '晚餐',
          children: [
            { key: 'dinner_fee', label: '订单金额' },
            { key: 'dinner_total_fee', label: '实际支付金额' },
            { key: 'dinner_count', label: '消费笔数' },
            { key: 'dinner_report_count', label: '消费份数' }
          ]
        },
        {
          key: 'supper',
          label: '宵夜',
          children: [
            { key: 'supper_fee', label: '订单金额' },
            { key: 'supper_total_fee', label: '实际支付金额' },
            { key: 'supper_count', label: '消费笔数' },
            { key: 'supper_report_count', label: '消费份数' }
          ]
        },
        {
          key: 'morning',
          label: '凌晨餐',
          children: [
            { key: 'morning_fee', label: '订单金额' },
            { key: 'morning_total_fee', label: '实际支付金额' },
            { key: 'morning_count', label: '消费笔数' },
            { key: 'morning_report_count', label: '消费份数' }
          ]
        },
        {
          key: 'collect',
          label: '合计',
          children: [
            { key: 'collect_fee', label: '订单金额合计' },
            { key: 'collect_total_fee', label: '实付金额合计' },
            { key: 'collect_count', label: '消费笔数合计' },
            { key: 'collect_report_count', label: '消费份数合计' }
          ]
        }
      ],
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getGroupCollectList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getGroupCollectList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getGroupCollectList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReportMealGroupCollectListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        let mealType = ['breakfast', 'lunch', 'afternoon', 'dinner', 'supper', 'morning', 'collect']
        let feeType = ['_fee', '_total_fee']
        this.tableData.push({
          dep_name: '合计',
          ...res.data.collect_data
        })
        this.tableData.map(item => {
          mealType.map(meal => {
            feeType.map(fee => {
              item[meal + fee] = divide(item[meal + fee])
            })
          })
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGroupCollectList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGroupCollectList()
    },
    // 表格总单号垂直合并 // row当前行 rowIndex当前行号 column当前列 columnIndex当前列号
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const dataProvider = this.tableData
        const cellValue = row[column.property]
        if (cellValue) {
          const prevRow = dataProvider[rowIndex - 1]
          let nextRow = dataProvider[rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    handleExport() {
      const option = {
        type: 'ExportDepMealReport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table__footer-wrapper td.el-table__cel {
  text-align: center !important;
}
</style>
