<template>
  <div class="meal-details container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="handlerMealImport" v-if="isShowImportBtn" type="Import" v-permission="['background_order.order_report_meal.batch_import_report_meal']">导入报餐</button-icon>
          <button-icon color="origin" type="export" @click="handleExport" v-permission="['background_order.order_report_meal.info_list_export']">导出Excel</button-icon>
          <!-- <button-icon color="plain">打印</button-icon> -->
          <!-- <button-icon color="plain">报表设置</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            v-for="item in table_column"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            align="center"
          ></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
     <!--导入弹窗-->
     <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="tableSettingImport"
      :show.sync="isShowImportDialog"
      :title="'导入报餐'"
      :openExcelType="openExcelType"
    >
    </import-dialog-drawer>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, getSevenDateRange, divide, deepClone, getSessionStorage, to } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import {
  PICKEROPTIONS,
  MEALDETAILS_TABLE_CLOUMN,
  MEALDETAILS_TABLE_DATE
} from '../meal-report/component/declaration'
import { TABLE_HEAD_DATA_IMPORT_MEAL } from './constant'
export default {
  name: 'MealReportDetail',
  mixins: [exportExcel],
  data() {
    const defaultdate = getSevenDateRange(7);
    return {
      tableData: MEALDETAILS_TABLE_DATE,
      table_column: MEALDETAILS_TABLE_CLOUMN,
      pickerOptions: PICKEROPTIONS,

      isLoading: false, // 刷新数据
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '时间',
          value: 'create_time',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '报餐时间',
              value: 'report_date'
            },
            {
              label: '用餐时间',
              value: 'dining_time'
            }
          ]
        },
        select_date: {
          clearable: false,
          type: 'daterange',
          value: [defaultdate[0], defaultdate[1]]
        },
        card_user_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: '',
          multiple: true,
          placeholder: '请选择分组'
        },
        // meal_type: {
        //   type: 'select',
        //   value: '',
        //   label: '餐段',
        //   dataList: [
        //     { value: '', label: '全部' },
        //     ...MEAL_TYPES
        //   ]
        // },
        meal_type_list: {
          type: 'select',
          label: '餐段',
          value: [],
          multiple: true,
          placeholder: '请选择',
          collapseTags: true,
          dataList: MEAL_TYPES
        },
        take_meal_status: {
          type: 'select',
          value: [],
          label: '取餐状态',
          multiple: true,
          collapseTags: true,
          dataList: [
            { value: 'take_out', label: '已取餐' },
            { value: 'no_take', label: '未取餐' },
            { value: 'cancel', label: '已取消' },
            { value: 'time_out', label: '已过期' }
          ]
        },
        meal_pack_is_null: {
          type: 'select',
          value: '',
          label: '来源',
          dataList: [
            { value: '', label: '全部' },
            { value: false, label: '餐包' },
            { value: true, label: '报餐' }
          ]
        },
        consume_type: {
          type: 'select',
          value: null,
          label: '支付方式',
          dataList: [
            { label: '全部', value: '' },
            { label: '线上', value: 'online' },
            { label: '线下', value: 'offline' }
          ]
        },
        name: {
          type: 'input',
          value: '',
          label: '姓名',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入人员编号'
        },
        organization_id: {
          type: 'organizationSelect',
          multiple: false,
          checkStrictly: true,
          isLazy: false,
          label: '消费点',
          value: [],
          placeholder: '请选择消费点'
        },
        unified_out_trade_no: {
          type: 'input',
          value: '',
          label: '总单号',
          placeholder: '请输入总单号'
        },
        out_trade_no: {
          type: 'input',
          value: '',
          label: '订单号',
          placeholder: '请输入订单号'
        }
      },
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      templateUrl: location.origin + '/api/temporary/template_excel/报餐模版/导入报餐模板.xlsx', // 导入模板的链接 to do debug
      tableSettingImport: deepClone(TABLE_HEAD_DATA_IMPORT_MEAL), // 导入表格头部设置
      openExcelType: 'MealReportImport', // 导入类型
      isShowImportDialog: false, // 是否显示确认键loading
      isShowImportBtn: false // 是否显示导入报餐按钮
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMealReportDetail()
      this.getMealReportCheck()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getMealReportDetail()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getMealReportDetail() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReportMealInfoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          item.pay_fee = divide(item.pay_fee)
          item.source = item.order_report_meal_pack_id ? '餐包' : '报餐'
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMealReportDetail()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMealReportDetail()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 表格总单号垂直合并 // row当前行 rowIndex当前行号 column当前列 columnIndex当前列号
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const dataProvider = this.tableData
        const cellValue = row[column.property]
        if (cellValue) {
          const prevRow = dataProvider[rowIndex - 1]
          let nextRow = dataProvider[rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    handleExport() {
      const option = {
        type: 'ExportMealReportDetail',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    /**
     * 导入按钮点击
     */
    handlerMealImport() {
      this.isShowImportDialog = true
    },
    // 检测是否有权限
    async getMealReportCheck() {
      var userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO') || '{}')) || {}
      const [err, res] = await to(this.$apis.apiBackgroundReportMealReportMealSettingsReportMealOrganizationCheckPost({
        id: userInfo.company_id
      }))
      if (err) {
        this.isShowImportBtn = false
        return
      }
      if (res && res.code === 0) {
        this.isShowImportBtn = true
      } else {
        this.isShowImportBtn = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 日历
.el-range-editor--mini.el-input__inner {
  height: 32px !important;
  width: 300px;
}
.meal-details {
  .el-select {
    width: 190px !important;
  }
  .top {
    border-bottom: 1px solid #e7ecf2;
    margin-bottom: 20px;
  }
  .el-form--inline .el-form-item__label:first-child {
    width: 50px;
  }
}
</style>
