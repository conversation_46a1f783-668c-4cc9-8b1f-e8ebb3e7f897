<template>
  <div class="MealPackageDetailDialog">
    <CustomDrawer
      :show.sync="showDrawer"
      :size="800"
      :title="title"
      :loading.sync="isLoading"
      fixedFooter
      :confirmShow="false"
      cancelText="关闭"
      cancelClass="ps-btn"
    >
      <div class="meal-package-box m-r-20">
        <el-form ref="formRef" :model="detailFormData" inline>
          <el-form-item label="日期筛选：">
            <el-date-picker
              v-model="detailFormData.searchDate"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              clearable
              class="ps-poper-picker"
              @change="clickChangeHandle"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="detailFormData.mealStatus"
              placeholder="请选择状态"
              class="ps-select"
              popper-class="ps-popper-select"
              multiple
              collapse-tags
              @change="clickChangeHandle"
            >
              <el-option
                v-for="(item, index) in mealStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="餐段：">
            <el-select
              v-model="detailFormData.mealType"
              placeholder="请选择餐段"
              class="ps-select"
              popper-class="ps-popper-select"
              @change="clickChangeHandle"
              clearable
            >
              <el-option
                v-for="(item, index) in mealTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="ps-flex statistics-btn m-b-20">
          <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />
          <div class="table-btn">
            <el-button type="mini" class="ps-origin-btn" @click="mulRefundConfirm">批量退款</el-button>
            <el-button type="mini" class="ps-origin-btn" @click="clickStopHandle('mul')">批量停餐</el-button>
          </div>
        </div>
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="packageList"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" class-name="ps-checkbox" width="55"></el-table-column>
          <el-table-column prop="meal_date" label="日期"></el-table-column>
          <el-table-column prop="meal_type_alias" label="餐段"></el-table-column>
          <el-table-column prop="take_meal_status_alias" label="状态"></el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button type="text" size="small" :disabled="!scope.row.can_refund" @click="clickRefundHandle(scope.row)">
                退款
              </el-button>
              <el-button type="text" size="small" v-if="scope.row.dining_status === 'dining' && scope.row.can_refund && scope.row.take_meal_status !== 'time_out'" @click="clickStopHandle('one',scope.row)">
                停餐
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </CustomDrawer>
  </div>
</template>

<script>
import { MEAL_TYPES } from '@/utils/constants'
import { debounce, parseTime, getDateRang } from '@/utils'
import { confirm } from '@/utils/message'
export default {
  name: 'MealPackageDetailDialog',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '餐包使用情况'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    packinfoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      detailFormData: {
        searchDate: [],
        mealType: '',
        mealStatus: []
      },
      mealTypeList: MEAL_TYPES,
      mealStatusList: [{
        value: 'no_take',
        label: '未取餐'
      }, {
        value: 'time_out',
        label: '已过期'
      }, {
        value: 'take_out',
        label: '已取餐'
      }, {
        value: 'cancel',
        label: '已退款'
      }],
      packageList: [],
      collect: [ // 统计
        { key: 'total', value: 0, label: '合计', dot: true },
        { key: 'no_take_total', value: 0, label: '未取餐：', dot: true },
        { key: 'time_out_total', value: 0, label: '已过期：', dot: true },
        { key: 'take_out_total', value: 0, label: '已取餐：', dot: true },
        { key: 'refund_total', value: 0, label: '已退款：' }

      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
      selectList: [],
      refundOrderIds: [],
      dialogLoading: false
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        this.packageList = []
        this.detailFormData.searchDate = getDateRang(-7)
        this.detailFormData.mealType = ''
        this.detailFormData.mealStatus = []
        this.getMealPackageDetail()
        this.getMealPackageCountDetail()
      }
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
      }
    }, 300),
    parseTime,
    clickChangeHandle: debounce(function(item) {
      this.getMealPackageDetail()
    }, 200),
    async getMealPackageDetail() {
      this.isLoading = true
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        unified_trade_no: this.infoData.trade_no,
        // start_pay_time: parseTime(this.infoData.pay_time, '{y}-{m}-{d} {hh}:{mm}:{ss}'),
        // end_pay_time: parseTime(new Date(parseTime(this.infoData.pay_time, '{y}/{m}/{d} {hh}:{mm}:{ss}')).getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d} {hh}:{mm}:{ss}')
        date_type: 'report_date',
        start_date: parseTime(this.detailFormData.searchDate[0], '{y}-{m}-{d}'),
        end_date: parseTime(this.detailFormData.searchDate[1], '{y}-{m}-{d}')
      }
      if (this.detailFormData.mealStatus) {
        params.take_meal_status_list = this.detailFormData.mealStatus
      }
      if (this.detailFormData.mealType) {
        params.meal_type = this.detailFormData.mealType
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderPaymentListPost(params))
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.packageList = res.data.results.map(item => {
          item.meal_date = item.report_date + parseTime(item.report_date, ' 星期{a}')
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMealPackageDetail()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMealPackageDetail()
    },
    async getMealPackageCountDetail() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderReportMealOrderReportMealPackDetailsCountPost({
        unified_trade_no: this.infoData.trade_no
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.collect.forEach(item => {
          for (let i in res.data) {
            if (item.key === i) {
              item.value = res.data[i]
            }
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 列表选择
    handleSelectionChange(val) {
      this.selectList = []
      this.refundOrderIds = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        if (item.can_refund) {
          this.refundOrderIds.push(item.id)
        }
        this.selectList.push(item)
      })
    },
    clickRefundHandle(item) {
      let query = {
        tab: 1,
        queryData: {
          trade_no: item.trade_no,
          date_type: 'pay_time',
          select_time: [parseTime(this.infoData.pay_time, '{y}-{m}-{d}'), parseTime(new Date(parseTime(this.infoData.pay_time, '{y}/{m}/{d}')).getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}')]
        }
      }
      query.queryData = JSON.stringify(query.queryData)
      this.$router.push({
        name: 'MerchantConsumption',
        query: query
      })
    },
    async mulRefundConfirm() {
      if (!this.selectList.length) return this.$message.error('请选择要退款的订单！')
      if (!this.refundOrderIds.length) return this.$message.error('当前所选订单不存在可退款订单！')
      confirm({ content: `确定要将这些订单进行退款？` }).then(_ => {
        let ids = this.selectList.map(item => {
          return item.id
        })
        this.mulRefundHandle(ids)
      }).catch(e => {
        if (e === 'cancel') {
        }
      })
    },
    async mulRefundHandle(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost({
        order_ids: params
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(`操作成功，其中不可退款订单数${this.selectList.length - this.refundOrderIds.length}笔`)
        this.getMealPackageDetail()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickStopHandle(type, data) {
      if (type === 'one') {
        confirm({ content: `是否对${data.report_date} ${data.meal_type_alias}进行停餐操作` }).then(_ => {
          this.setMealOrderStop([data.trade_no])
        }).catch(e => {
          if (e === 'cancel') {
          }
        })
      } else if (type === 'mul') {
        confirm({ content: `是否对共${this.selectList.length}单，进行停餐操作` }).then(_ => {
          let trades = this.selectList.map(item => {
            return item.trade_no
          })
          this.setMealOrderStop(trades)
        }).catch(e => {
          if (e === 'cancel') {
          }
        })
      }
    },
    async setMealOrderStop(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderRefundReportMealOrderStopPost({
        trade_nos: params
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMealPackageDetail()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
};
</script>

<style lang="scss">
.MealPackageDetailDialog{
  .meal-package-box {
    margin: 20px;
  }
  .statistics-btn{
    justify-content: space-between;
    align-items: center;
  }
}

</style>
