import * as dayjs from 'dayjs'
export const RECENTSEVENTDAY = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const PICKEROPTIONS = {
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        picker.$emit('pick', [start, end])
      }
    }
  ]
}

// MealDetails
export const MEALDETAILS_TABLE_CLOUMN = [
  {
    label: '总单号',
    prop: 'unified_trade_no',
    width: '170'
  },
  {
    label: '订单号',
    prop: 'trade_no',
    width: '190'
  },
  {
    label: '分组',
    prop: 'payer_group_name'
  },
  {
    label: '姓名',
    prop: 'name',
    width: '90'
  },
  {
    label: '人员编号',
    prop: 'person_no',
    width: '90'
  },
  {
    label: '部门',
    prop: 'payer_department_group_name'
  },
  {
    label: '创建时间',
    prop: 'create_time'
  },
  {
    label: '报餐时间',
    prop: 'report_date'
  },
  {
    label: '用餐时间',
    prop: 'dining_time',
    width: '95'
  },
  {
    label: '报餐餐段',
    prop: 'meal_type_alias',
    width: '80'
  },
  {
    label: '报餐方式',
    prop: 'report_meal_type_alias',
    width: '80'
  },
  {
    label: '扣费方式',
    prop: 'consume_type_alias',
    width: '80'
  },
  {
    label: '份数',
    prop: 'count',
    width: '50'
  },
  {
    label: '订单金额',
    prop: 'pay_fee',
    width: '80'
  },
  {
    label: '取餐状态',
    prop: 'take_meal_status_alias',
    width: '80'
  },
  {
    label: '报餐消费点',
    prop: 'consumption_name',
    width: '100'
  },
  {
    label: '来源',
    prop: 'source'
  }
]
export const MEALDETAILS_TABLE_DATE = [
  {
    total_number: '142',
    order_number: '111',
    grouping: '',
    username: '',
    user_number: '',
    department: '',
    change_time: '',
    dinner_time: '',
    meal_type: '',
    deduction_type: '',
    total_numcountber: '',
    order_money: '',
    get_meal_state: '',
    consumption: ''
  },
  {
    total_number: '142',
    order_number: '111',
    grouping: '',
    username: '',
    user_number: '',
    department: '',
    change_time: '',
    dinner_time: '',
    meal_type: '',
    deduction_type: '',
    total_numcountber: '',
    order_money: '',
    get_meal_state: '',
    consumption: ''
  },
  {
    total_number: '22',
    order_number: '111',
    grouping: '',
    username: '',
    user_number: '',
    department: '',
    change_time: '',
    dinner_time: '',
    meal_type: '',
    deduction_type: '',
    total_numcountber: '',
    order_money: '',
    get_meal_state: '',
    consumption: ''
  }
]
export const HEADCOUNT_TABLE_CLOUMN = [
  {
    label: '时间',
    prop: 'report_date'
  },
  {
    label: '人员编号',
    prop: 'person_no'
  },
  {
    label: '姓名',
    prop: 'name'
  },
  {
    label: '分组',
    prop: 'card_user_groups'
  },
  {
    label: '部门',
    prop: 'department_group_name'
  }
]
