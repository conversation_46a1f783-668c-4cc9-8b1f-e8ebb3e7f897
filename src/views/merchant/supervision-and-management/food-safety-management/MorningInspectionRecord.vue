<template>
  <!-- 晨检记录-->
  <div class="morning-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="tab-box">
      <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
        <el-radio-button
          v-for="tab in tabTypeList"
          :key="tab.value"
          :label="tab.value"
          v-permission="[tab.permissions]"
        >
          {{ tab.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <MorningInspectionRecordDetail ref="detail" v-if="tabType === 'detail'"></MorningInspectionRecordDetail>
    <MorningInspectionRecordSummary ref="summary" v-if="tabType === 'summary'"></MorningInspectionRecordSummary>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'
import MorningInspectionRecordDetail from './compontents/MorningInspectionRecordDetail.vue'
import MorningInspectionRecordSummary from './compontents/MorningInspectionRecordSummary.vue'

export default {
  name: 'MorningInspectionRecord',
  mixins: [exportExcel, report],
  components: {
    MorningInspectionRecordDetail,
    MorningInspectionRecordSummary
  },
  data() {
    return {
      tabType: '',
      tabTypeList: [
        {
          label: '晨检明细',
          value: 'detail',
          permissions: 'background_fund_supervision.canteen_safety_management.morning_check_detail'
        },
        {
          label: '晨检汇总',
          value: 'summary',
          permissions: 'background_fund_supervision.canteen_safety_management.morning_check_collect'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.initTabList()
    },
    // 初始页面权限
    initTabList() {
      let result = []
      this.tabTypeList.forEach(v => {
        if (this.allPermissions.includes(v.permissions)) {
          result.push(v)
        }
      })
      this.tabTypeList = result
      this.tabType = this.tabTypeList.length ? this.tabTypeList[0].value : ''
    },
    async refreshHandle() {
      this.$refs[this.tabType].refreshHandle()
    },
    // 切换tab
    changeTabHandle(e) {}
  }
}
</script>

<style lang="scss" scoped></style>
