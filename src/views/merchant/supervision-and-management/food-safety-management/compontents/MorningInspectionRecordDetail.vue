<template>
  <!-- 晨检记录-->
  <div class="morning-wrapper container-wrapper">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        @reset="resetHandler"
        label-width="120px"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            :height="maxHeight"
            :max-height="maxHeight"
            stripe
            header-row-class-name="ps-table-header-row"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item">
              <template #images="{ row }">
                <div @click="handlerImageDetail(row)" class="ps-origin pointer">查看</div>
              </template>
              <template #checkResult="{ row }">
                <div :class="row.check_result_alias === '失败' ? 'ps-red' : ''">
                  {{ row.check_result_alias }}
                </div>
              </template>
              <template #temperature="{ row }">
                {{ row.temperature ? row.temperature + '°C' : '' }}
              </template>
              <template #checkStatus="{ row }">
                <div :class="row.check_status ? '' : 'ps-red'">
                  {{ row.check_status ? '已晨检' : '未晨检' }}
                </div>
              </template>
              <template #extraField1="{ row, col }">
                1
                <div v-if="row.extra && row.extra[col.extraKey] !== undefined">
                  <i v-if="row.extra[col.extraKey]" class="el-icon-check" style="color: #67C23A; font-size: 16px;"></i>
                  <span v-else style="color: #F56C6C;">异常</span>
                </div>
                <div v-else>-</div>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
    ></print-setting>
    <!--弹窗图片-->
    <pic-details-dialog :isshow.sync="showPicDialog" :images="imageList" :name="picName" />
  </div>
</template>

<script>
import { SEARCH_SETTING_MORNING_INSPECTION_DETAILS, TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS } from '../constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PicDetailsDialog from './PicDetailsDialog'
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'
import { debounce, deepClone } from '@/utils'

export default {
  name: 'MorningInspectionRecordDetail',
  mixins: [exportExcel, report],
  components: {
    PicDetailsDialog
  },
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS),
      currentTableSetting: [],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_SETTING_MORNING_INSPECTION_DETAILS),
      printType: 'SupervisionCanteenSafetyCheckDetail',
      showPicDialog: false,
      imageList: {},
      picName: '',
      maxHeight: 460
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization'])
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.initPrintSetting()
      this.setTabDataHandle()
    },
    resetHandler() {
      this.currentPage = 1
      this.setTabDataHandle()
    },
    async refreshHandle() {
      this.currentPage = 1
      this.setTabDataHandle()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    // 设置tab数据
    setTabDataHandle() {
      this.tableData = []
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0] + ' 00:00:00'
            params.end_time = data[key].value[1] + ' 23:59:59'
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const res = await this.$apis.apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailPost(params)
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = deepClone(data.results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tabbleSetting = deepClone(this.currentTableSetting)
      tabbleSetting = tabbleSetting.filter(item => item.key !== 'images')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '晨检明细',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tabbleSetting),
          current_table_setting: JSON.stringify(tabbleSetting),
          collect: null,
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          }),
          isMerge: '0'
        }
      })
      window.open(href, '_blank')
    },
    // 查看图片详情
    handlerImageDetail(row) {
      let imgData = {
        scene_img: row.images.scene_img,
        picture_img: row.images.picture_img,
        picture_back_img: row.images.picture_back_img
      }
      this.imageList = imgData
      this.picName = row.name
      this.showPicDialog = true
    }
  }
}
</script>

<style lang="scss" scoped></style>
