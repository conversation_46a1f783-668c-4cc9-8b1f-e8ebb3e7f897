<template>
  <customDrawer
    :show.sync="visible"
    cancelText="关闭"
    :confirmShow="false"
    :loading="isLoading"
    :title="'历史记录'"
    :size="800"
    @confirm="saveSetting"
  >
    <div class="m-t-20 m-b-10 flex flex-align-c">
      <div>时间：</div>
      <div class="m-l-10px">
        <el-date-picker
          v-model="searchDate"
          :default-value="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :clearable="false"
          :picker-options="datePickerOptions"
        />
      </div>
    </div>

    <div class="table-content">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="historytableData"
        ref="historytableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <el-table-column prop="time" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="content" label="操作人" align="center"></el-table-column>
        <el-table-column prop="details" label="所属组织" align="center"></el-table-column>
        <el-table-column prop="operator" label="所属菜谱" align="center"></el-table-column>
        <el-table-column prop="operator" label="菜品" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作内容" align="center"></el-table-column>
      </el-table>
      <!-- table end -->
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100, 500]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount"
        background
        class="ps-text"
        popper-class="ps-popper-select"
      ></el-pagination>
    </div>
  </customDrawer>
  <!-- end -->
</template>

<script>
import { to } from '@/utils'
import dayjs from 'dayjs'

export default {
  name: 'HistoryDialog',
  props: {
    paramsInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    isshow: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      historytableData: [],
      searchDate: [],
      datePickerOptions: {
        // 禁用超过最近30天之前的日期
        disabledDate: time => {
          const today = dayjs()
          const thirtyDaysAgo = today.subtract(30, 'days')
          return time.getTime() < thirtyDaysAgo || time.getTime() > today.endOf('day')
        }
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {},
  created() {
    // this.getRuleHistory()
  },
  mounted() {
    this.searchDate = [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  methods: {
    saveSetting(e) {
      this.visible = false
    },
    // 获取规则的历史记录
    async getRuleHistory() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeGetRuleHistoryPost({
          ...this.paramsInfo,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.historytableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getRuleHistory()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRuleHistory()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>
