<template>
  <div class="refund-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchForm" @search="searchHandle" :autoSearch="false">
      <template #perv>
        <div class="searchref_top">
          <el-button :class="{ active: current === 0 }" @click="tabHandler(0)" v-permission="['background_order.order_refund.on_scene_list']">堂食订单</el-button>
          <el-button :class="{ active: current === 1 }" @click="tabHandler(1)" v-permission="['background_order.order_refund.reservation_list']">预约订单</el-button>
          <el-button :class="{ active: current === 2 }" @click="tabHandler(2)" v-permission="['background_order.order_refund.order_approve_list']">访客订单</el-button>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-show="current === 0" v-permission="['background_order.order_refund.on_scene_list_export']">导出报表</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-show="current === 1" v-permission="['background_order.order_refund.reservation_list_export']">导出报表</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-show="current === 2" v-permission="['background_order.order_refund.order_approve_list_export']">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="currentTableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :index="indexMethod"
          :isFirst="isFirstSearch"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <!-- table end -->
      <div v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText">
        <ul class="total">
          <li>
            退款笔数:
            <span>{{ totalCount }}</span>
          </li>
          <li>
            合计退款金额:
            <span>￥{{ total_amount | formatMoney }}</span>
          </li>
          <li v-if="current !== 2">
            餐补返还合计:
            <span>￥{{ total_food_subsidy_fee | formatMoney }}</span>
          </li>
          <li v-if="current !== 2">
            手续费合计:￥
            <span>{{ total_rate_fee | formatMoney }}</span>
          </li>
          <li v-if="current === 1">
              服务费合计:￥
              <span>{{ totalServeFee | formatMoney }}</span>
            </li>
        </ul>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import {
  REFUND_SCENE_TABLE,
  REFUND_RESERVATION_TABLE,
  REFUND_SCENE_SEARCH,
  REFUND_RESERVATION_SEARCH,
  getRequestParams
} from './constants'
import { mapGetters } from 'vuex'
export default {
  name: 'RefundOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      current: 0,
      // 数据列表
      tableData: [],
      searchForm: {},
      sceneSearchForm: deepClone(REFUND_SCENE_SEARCH),
      reservationSearchForm: deepClone(REFUND_RESERVATION_SEARCH),
      isLoading: false, // 刷新数据
      isLoadingCollect: false,
      elementLoadingText: "数据正在加载，请耐心等待...",
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页
      total_amount: 0,
      total_food_subsidy_fee: 0, // 餐补返还合计
      total_rate_fee: 0,
      // 报表打印相关
      tableSetting: [],
      sceneTableSetting: [],
      reservationTableSetting: [],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'RefundOrderScene',
      totalServeFee: 0, // 服务费合计
      isFirstSearch: true
    }
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  watch: {
    allPermissions: {
      handler: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (newVal.includes('background_order.order_refund.on_scene_list') && !newVal.includes('background_order.order_refund.reservation_list') && !newVal.includes('background_order.order_refund.order_approve_list')) {
            this.current = 0
          } else if (!newVal.includes('background_order.order_refund.on_scene_list') && newVal.includes('background_order.order_refund.reservation_list') && !newVal.includes('background_order.order_refund.order_approve_list')) {
            this.current = 1
          } else if (!newVal.includes('background_order.order_refund.on_scene_list') && !newVal.includes('background_order.order_refund.reservation_list') && newVal.includes('background_order.order_refund.order_approve_list')) {
            this.current = 2
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // this.sceneSearchForm.device_org.value = [this.$store.getters.organization]
    this.initLoad(true)
  },
  mounted() {
    this.getLevelNameList()
  },
  methods: {
    initLoad(isFirst) {
      if (this.current === 0) {
        this.searchForm = this.sceneSearchForm
        if (!isFirst) {
          this.getRefundOnSceneList()
          this.getRefundOnSceneStatisticalDataList()
        }
      } else if (this.current === 1) {
        this.searchForm = this.reservationSearchForm
        if (!isFirst) {
          this.getRefundReservationList()
          this.getRefundReservationStatisticalDataList()
        }
      } else if (this.current === 2) {
        this.searchForm = this.sceneSearchForm
        if (!isFirst) {
          this.getRefundVisitorList()
          this.getRefundVisitorStatisticalDataList()
        }
      }
    },
    tabHandler(type) {
      this.current = type
      this.page = 1
      // 重置合计数据
      this.totalCount = 0
      this.total_amount = 0
      this.total_food_subsidy_fee = 0
      this.total_rate_fee = 0
      this.totalServeFee = 0
      this.isFirstSearch = true
      this.$nextTick(() => {
        if (type === 0) {
          this.printType = 'RefundOrderScene'
          this.searchForm = this.sceneSearchForm
          this.tableSetting = this.sceneTableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getRefundOnSceneList()
          // this.getRefundOnSceneStatisticalDataList()
        } else if (type === 1) {
          this.printType = 'RefundOrderReservation'
          this.searchForm = this.reservationSearchForm
          this.tableSetting = this.reservationSableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getRefundReservationList()
          // this.getRefundReservationStatisticalDataList()
        } else if (type === 2) {
          this.printType = 'RefundOrderVisitor'
          this.searchForm = this.sceneSearchForm
          this.tableSetting = this.sceneTableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getRefundVisitorList()
        }
      })
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.page = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.page = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 获取退款预约订单列表
    async getRefundReservationList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderRefundReservationListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.totalCount = res.data.count
        // this.total_amount = res.data.total_amount
        // this.total_rate_fee = res.data.total_rate_fee
        // this.totalServeFee = res.data.total_fuwu_fee
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取退款堂食订单列表
    async getRefundOnSceneList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderRefundOnSceneListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.totalCount = res.data.count
        // this.total_amount = res.data.total_amount
        // this.totalServeFee = res.data.total_serve_fee
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取退款预约合计数据
    async getRefundReservationStatisticalDataList(paramsData) {
      this.isLoadingCollect = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderRefundReservationListCollectPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        this.totalCount = res.data.total_count
        this.total_amount = res.data.total_amount
        this.total_food_subsidy_fee = res.data && res.data.total_food_subsidy_fee ? res.data.total_food_subsidy_fee : "0.00"
        this.total_rate_fee = res.data.total_rate_fee
        this.totalServeFee = res.data.total_fuwu_fee
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error(res.msg)
      }
    },
    // 获取退款堂食订单合计数据
    async getRefundOnSceneStatisticalDataList(paramsData) {
      this.isLoadingCollect = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderRefundOnSceneListCollectPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        this.totalCount = res.data.total_count
        this.total_amount = res.data.total_amount
        this.total_food_subsidy_fee = res.data && res.data.total_food_subsidy_fee ? res.data.total_food_subsidy_fee : "0.00"
        this.totalServeFee = res.data.total_serve_fee
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error(res.msg)
      }
    },
    // 获取退款访客餐订单列表
    async getRefundVisitorList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderRefundOrderApproveListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.totalCount = res.data.count
        // this.total_amount = res.data.total_amount
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取退款访客订单合计数据
    async getRefundVisitorStatisticalDataList(paramsData) {
      this.isLoadingCollect = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderRefundOrderApproveListCollectPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        this.totalCount = res.data.total_count
        this.total_amount = res.data.total_amount
        this.total_food_subsidy_fee = res.data && res.data.total_food_subsidy_fee ? res.data.total_food_subsidy_fee : "0.00"
        this.totalServeFee = res.data.total_serve_fee
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 导出报表
    handleExport() {
      let type
      if (this.current === 1) {
        type = 'ExportRefundOrder'
      } else if (this.current === 0) {
        type = 'ExportRefundOnSceneList'
      } else if (this.current === 2) {
        type = 'ExportRefundVisitorList'
      }
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const option = {
        type,
        params
      }
      this.exportHandle(option)
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      // 初始化每个tableSetting
      this.sceneTableSetting = deepClone(REFUND_SCENE_TABLE)
      this.reservationSableSetting = deepClone(REFUND_RESERVATION_TABLE)
      this.sceneTableSetting.splice(7, 0, ...arr2)
      this.reservationSableSetting.splice(7, 0, ...arr2)
      if (this.current === 0) {
        this.tableSetting = this.sceneTableSetting
      } else if (this.current === 1) {
        this.tableSetting = this.reservationSableSetting
      } else if (this.current === 2) {
        this.tableSetting = this.sceneTableSetting
      }
      this.initPrintSetting()
    }
  }
}
</script>

<style lang="scss" scoped>
// 日历
// .el-range-editor--mini.el-input__inner {
//   height: 32px !important;
//   width: 320px;
// }
.el-loading-wrapp{
  .el-loading-spinner{
    margin-top:0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.refund-order {
  .el-input {
    width: 180px;
  }
  .el-select {
    width: 180px;
  }
  .searchref_top {
    margin-bottom: 10px;
    .active {
      background-color: #ff9b45!important;
      color: #fff!important;
    }
    .el-button:focus.el-button--default:not(.is-plain):not(.el-button--primary), .el-button:hover.el-button--default:not(.is-plain):not(.el-button--primary) {
      background-color: #fff;
      color: #ff9b45;
    }
  }
  .el-table {
    font-size: 12px;
  }
  .searchref_top {
    .el-button {
      width: 120px;
    }
  }
  .total {
    padding: 0 20px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
}
</style>
