<template>
  <div class="consumption-failure container-wrapper">
    <refresh-tool ref="searchRef" @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.order_offline.list_export']">导出报表</button-icon>
          <button-icon color="plain" @click="mulRefundHandler" v-permission="['background_order.order_offline.bulk_order_pay']">批量扣款</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <!-- <button-icon color="plain" type="export" @click="handleExport" style="margin-right:20px">
            导出报表
          </button-icon>
          <el-button size="small">全部重新扣款</el-button> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table  style="width: 100%" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" use-virtual :row-height="30" :height="600" :big-data-checkbox="true"
        class="table-data" @selection-change="handleSelectionChange" row-key="p_id" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
          <el-table-column type="selection" width="50" class-name="ps-checkbox"></el-table-column>
          <el-table-column :index="index" v-for="(item,index) in currentTableSetting" :key="index"
          :prop="item.key" :label="item.label" :width="item.width" align="center">
           <template slot-scope="scope">
            <span v-if="item.type === 'index'">{{ (scope.$index+1)+(currentPage-1)*pageSize }}</span>
            <span v-if="item.type === 'money'"> ¥{{ scope.row[item.key] | formatMoney }}</span>
            <span v-if="!item.type">{{ scope.row[item.key] }}</span>
            <span v-if="item.type === 'slot' && item.slotName === 'operation'">
              <el-button type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>
              <el-button type="text" size="small" @click="clickBtnHandle('repay', scope.row)" v-permission="['background_order.order_offline.order_pay']">重新扣款</el-button>
              <el-button type="text" size="small" class="ps-text" @click="clickBtnHandle('cancel', scope.row)" v-permission="['background_order.order_offline.cancel_bulk_order_pay']">取消订单</el-button>
              <el-button type="text" size="small" @click="clickBtnHandle('origin', scope.row)" v-permission="['background_order.order_offline.order_pay']">原价扣款</el-button>
            </span>
           </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
        <!-- <ul class="total">
          <li>
            合计笔数:
            <span>{{ totalData.total_count }}</span>
          </li>
          <li>
            合计订单金额:￥
            <span>{{ totalData.total_amount | formatMoney }}</span>
          </li>
          <li>
            合计实收金额:￥
            <span>{{ totalData.total_pay_amount | formatMoney }}</span>
          </li>
        </ul> -->
      </div>
      <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 200, 500, 1000, 2000]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
     <!-- 扣款弹窗 -->
     <el-dialog
        width="30%"
        custom-class="el-dialog__body"
        :title="dialogType == 'refund'?'批量扣款':'提示'"
        :visible.sync="isShowRefundDialog"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        lock-scroll
        append-to-body
        :show-close="false"
        @close="dialogClose"
      >
      <!--关闭按钮-->
      <!-- <div @click="dialogClose" class="close-icon" v-if="dialogType!='refunding'">
        <i class="el-icon-close" style="width: 18px;height: 18px;"></i>
      </div> -->
      <!--选择扣款模式-->
      <div v-if="dialogType == 'refund'" class="flex-btn-center">
      <el-radio text-color="#FF9B45" v-model="flagRefundMoney" label="1">重新扣款</el-radio>
      <el-radio text-color="#FF9B45" v-model="flagRefundMoney" label="2">原价扣款</el-radio>
      </div>
      <!--扣款中-->
      <div v-else-if="dialogType == 'refunding'" class="">
        <div>后台执行批量扣款中，请稍后...</div>
        <el-progress :text-inside="true" :stroke-width="24" :percentage="percentage" status="success"></el-progress>
      </div>
      <!--扣款结果-->
      <div v-else>
        <p style="color:black;text-align: center;">扣款成功：{{numberRefundSuccess}}单</p>
        <p style="color:#E0364C;text-align: center;">扣款失败：{{numberRefundFail}}单</p>
      </div>
      <div slot="footer" class="flex-btn-center">
        <el-button size="small" class="ps-cancel-btn" @click="handlerCancleRefund"  :disabled="isCancleBtnDisable" v-loading="dialogCancleLoading" v-if="dialogType != 'success'">取 消</el-button>
        <el-button size="small" class="ps-btn" @click="handlerConfirmRefund" v-loading="dialogLoading" type="primary" :disabled="isConfirmBtnDisable" v-if="dialogType == 'refund'">确认扣款</el-button>
        <el-button size="small" class="ps-btn" @click="handlerConfirmClose" v-loading="dialogLoading" type="primary" :disabled="isConfirmBtnDisable" v-if="dialogType == 'success'">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
import { MEALTYPE, PICKEROPTIONS, PAYMENTSTATE } from './component/order'
import { CONSUMPTION_FAILURE } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapState } from 'vuex'
export default {
  name: 'ConsumptionFailure',
  mixins: [report, exportExcel],
  components: {
  },
  data() {
    return {
      meal_type: MEALTYPE, // 餐段
      paymentstate: PAYMENTSTATE, // 支付状态
      searchSetting: deepClone(CONSUMPTION_FAILURE),
      pickerOptions: PICKEROPTIONS,
      tableData: [],
      // 报表设置相关
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: "80" },
        { label: '总单号', key: 'unified_out_trade_no', width: "150" },
        { label: '订单号', key: 'trade_no', width: "150" },
        { label: '创建时间', key: 'create_time', width: "150" },
        { label: '支付时间', key: 'pay_time', width: "150" },
        // { label: '扣款时间', key: 'deduction_time', width: "150" },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        // { label: '优惠金额', key: 'discount_fee', type: 'money' },
        // { label: '补贴消费', key: 'subsidy_fee', type: 'money' },
        // { label: '服务费', key: 'fuwu_fee', type: 'money' },
        // { label: '实收金额', key: 'pay_fee', type: 'money' },
        // { label: '支付状态', key: 'order_status_alias' },
        { label: '失败原因', key: 'error_reason' },
        // { label: '上传状态', key: 'upload_status_alias' },
        { label: '设备状态', key: 'pay_device_status_alias' },
        // { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
        // { label: '储值动账', key: 'wallet_fee', type: 'money' },
        // { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
        // { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
        // { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
        // { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
        { label: '餐段', key: 'meal_type_alias' },
        { label: '用户名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '分组', key: 'payer_group_name' },
        { label: '部门', key: 'payer_department_group_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "180" }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'failureOrder',
      columns: [], // 动态获取组织的层级
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      totalData: {
        total_count: 0,
        total_amount: 0,
        total_pay_amount: 0
      },
      orgKey: [], // 区分下组织和其它
      collect: [ // 统计
        { key: 'total_count', value: 0, label: '合计笔数' },
        { key: 'total_amount', value: 0, label: '合计订单金额：￥', type: 'money' },
        { key: 'total_pay_amount', value: 0, label: '合计实收金额：￥', type: 'money' }
      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
      orderIds: [], // 批量选择的订单
      flagRefundMoney: '1', // 重新扣款还是原价扣款
      dialogType: 'refunding', // 弹窗类型
      numberRefundSuccess: 0, // 扣款成功笔数
      numberRefundFail: 0, // 扣款失败笔数
      isShowRefundDialog: false, // 弹窗是否显示
      dialogLoading: false, // 弹窗按钮loading
      timeCount: 3, // 关闭倒计时
      timeThread: null, // 倒计时线程
      isCancleBtnDisable: false,
      isConfirmBtnDisable: false,
      timer: null, // 重复轮训线程
      percentage: 0, // 进度，最高百分白
      dialogCancleLoading: false, // 取消loading
      queryId: null, // 线程ID
      isFirstSearch: true
    }
  },
  async created() {
    await this.getLevelNameList()
    // this.initLoad()
  },
  mounted() {
  },
  filters: {
    capitalize: function(value) {
      return value.toFixed(2)
    }
  },
  computed: {
    ...mapState('navTabs', ['navMenuList'])
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    initLoad() {
      this.getFailureOrderList()
      this.getFailureOrderStatisticalDataList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 拉取脱机消费失败订单
    async getFailureOrderList() {
      this.isLoading = true
      let params = this.formatQueryParams(this.searchSetting)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineListPost({
        ...params,
        page: this.currentPage,
        page_size: this.pageSize
      }))
      // this.getFailureOrderStatisticalDataList(params)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        // this.totalData.total_count = res.data.total_count
        // this.totalData.total_amount = res.data.total_amount
        // this.totalData.total_pay_amount = res.data.total_pay_amount
        this.tableData = res.data.results.map(item => {
          let data = deepClone(item)
          let orderPaymen = deepClone(data.order_payment)
          delete data.order_payment
          for (const key in orderPaymen) {
            if (Object.hasOwnProperty.call(orderPaymen, key)) {
              if (this.orgKey.includes(key)) {
                data['org_' + key] = orderPaymen[key]
              } else {
                if (data[key] !== undefined) {
                  data['p_' + key] = orderPaymen[key]
                } else {
                  data[key] = orderPaymen[key]
                }
              }
            }
          }
          return data
        })
        console.log(this.tableData)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表合计数据
    async getFailureOrderStatisticalDataList() {
      const params = this.formatQueryParams(this.searchSetting)
      this.isLoadingCollect = true
      const res = await this.$apis.apiBackgroundOrderOrderOfflineListCollectPost(params)
      if (res.code === 0) {
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        console.log(res)
        // 统计
        this.collect.forEach(item => {
          for (let i in res.data) {
            if (item.key === i) {
              item.value = res.data[i]
            }
          }
        })
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error('汇总数据加载失败，请重试。')
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getFailureOrderList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFailureOrderList()
    },
    // 导出报表
    handleExport() {},
    gotoDetail(row) {
      this.$router.push({
        name: 'MerchantConsumptionFailureDetail',
        query: {
          id: row.p_id,
          trade_no: row.trade_no
          // data: JSON.stringify(row)
        }
      })
    },
    // 列表序号 补0
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      // let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      // let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      let list = res.data.map(v => {
        this.orgKey.push(v.level)
        return {
          label: v.name,
          key: 'org_' + v.level
        }
      })
      this.tableSetting.splice(6, 0, ...list)
      console.log(this.tableSetting)
      this.initPrintSetting()
    },
    clickBtnHandle(type, row) {
      let tipsText = `确定${type === 'repay' ? '重新发起扣款吗' : type === 'cancel' ? '取消订单吗' : '原价扣款'}？`
      this.$confirm(tipsText, '提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-origin-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            if (type === 'repay') {
              await this.repayOrder(row.p_id)
            }
            if (type === 'cancel') {
              await this.closeOrder(row.p_id)
            }
            if (type === 'origin') {
              await this.repayOrder(row.p_id, true) // 第二个参数为是否原价扣款
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 重新发起订单支付, isOrigin表示是否原价扣款
    async repayOrder(id, isOrigin) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let params = {
        order_payment_id: id
      }
      if (isOrigin) params.is_original_price = isOrigin
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineOrderPayPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重新发起订单支付
    async closeOrder(id) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineOrderClosePost({
        order_payment_id: id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log("handleSelectionChange", val);
      var selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map((item, i) => {
        selectListId.push(item.p_id)
      })
      this.orderIds = deepClone(selectListId)
    },
    // 批量扣款
    mulRefundHandler() {
      if (this.dialogLoading) return
      if (!this.orderIds.length) return this.$message.error('请选择要批量扣款的订单！')
      this.dialogType = "refund"
      this.flagRefundMoney = '1'
      this.isShowRefundDialog = true
      console.log("mulRefundHandler", this.orderIds);
    },
    // 扣款成功
    async handlerConfirmRefund() {
      console.log("handlerConfirmRefund");
      this.dialogLoading = true
      var params = {
        order_payment_ids: this.orderIds,
        is_original_price: this.flagRefundMoney === '2'
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineBulkOrderPayPost(params))
      this.dialogLoading = false
      if (err) {
        this.dialogType = "refund"
        this.$message.error(err.message || '扣款失败')
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        this.queryId = data.query_id || ''
        this.startQueryHandle()
      } else {
        this.startQueryHandle()
        this.$message.error(res.msg || '扣款失败')
      }
    },
    // 设置倒数 不要了这个产品说不要倒计时关闭弹窗了
    setTimeBackward() {
      var that = this
      this.timeThread = setInterval(() => {
        that.timeCount--
        if (that.timeCount === 0) {
          that.isShowRefundDialog = false
          clearInterval(this.timeThread)
          that.timeThread = null
          that.timeCount = 3
          that.currentPage = 1
          that.getFailureOrderList()
        }
      }, 1000);
    },
    // 弹窗关闭
    dialogClose() {
      console.log("dialogClose");
      // this.handlerConfirmClose()
      this.handlerCancleRefund()
    },
    // 开始轮询
    startQueryHandle() {
      this.percentage = 0
      this.dialogType = 'refunding'
      this.getResultUrl(this.queryId)
      this.timer = setInterval(() => {
        this.getResultUrl(this.queryId)
      }, 3000)
    },
    // 轮询查看结果
    async getResultUrl(queryId) {
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineBulkOrderPayTaskQueryPost({
        query_id: queryId
      }))
      if (err) {
        this.showErrorMsg(res.msg)
        return
      }
      if (res.code === 0) {
        this.percentage = res.data.progress || 0
        if (res.data && res.data.status === 'success') {
          this.numberRefundSuccess = res.data.success || 0
          this.numberRefundFail = res.data.fail || 0
          clearInterval(this.timer)
          setTimeout(() => {
            this.dialogType = "success"
          }, 1000);
        } else if (res.data.status === 'failure') {
          this.showErrorMsg(res.msg)
        }
      } else {
        this.showErrorMsg(res.msg)
      }
    },
    // 显示错误信息
    showErrorMsg(msg) {
      this.dialogType = 'refund'
      this.$message.error(msg)
      clearInterval(this.timer)
    },
    // 取消批量扣款
    async handlerCancleRefund() {
      if (this.dialogType === 'refunding') {
        clearInterval(this.timer)
        this.timer = null
        this.dialogCancleLoading = true
        const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineCancelBulkOrderPayPost(({
          query_id: this.queryId
        })))
        this.dialogCancleLoading = false
        if (err) {
          this.showErrorMsg(res.msg)
          return
        }
        if (res && res.code === 0) {
          this.numberRefundSuccess = res.data.success || 0
          this.numberRefundFail = res.data.fail || 0
          this.dialogType = "success"
        } else {
          this.$message.error(res.msg || '取消失败')
        }
      } else if (this.dialogType === 'refund') {
        this.isShowRefundDialog = false
      } else {
        this.handlerConfirmClose()
      }
    },
    // 关闭弹窗
    handlerConfirmClose() {
      this.isShowRefundDialog = false
      this.dialogType = 'refund'
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.getFailureOrderList()
    },
    gotoExport() {
      let params = this.formatQueryParams(this.searchSetting)
      const option = {
        url: 'apiBackgroundOrderOrderOfflineListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-failure {
  // // 日历
  // .el-range-editor--mini.el-input__inner {
  //   height: 32px !important;
  //   width: 320px;
  // }
  .table-data{
    ::v-deep .el-table__row td{
      border-bottom: 1px solid #EBEEF5;
      .umy-table-beyond{
      text-align: center;
    }
    }
    ::v-deep .el-table__fixed-right {
      height :100% !important;
    }
    ::v-deep .el-table__fixed {
      height :100% !important;
    }
  }
  .el-input {
    width: 180px;
  }
  .el-select {
    width: 180px;
  }
  //数据报表头部
  .table-wrapper {
    .table-header {
      display: flex;
      justify-content: space-between;
      .el-button {
        margin-right: 10px;
        &:nth-of-type(3) {
          margin-right: 20px;
        }
      }
    }
  }
  .el-table {
    text-align: center;
    font-size: 12px;
  }
  .total {
    // padding: 0 20px;
    margin-top: 20px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
  .el-loading-wrapp{
    .el-loading-spinner{
      margin-top:0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
::v-deep .el-dialog__body {
  padding: 10px 20px  10px 20px!important;
  position: relative;
  border-radius: 10px;
}
.flex-btn-center {
  display: flex;
  justify-content: center;
}
.time-count {
   text-align:right;
}
::v-deep .ps-table-header-row{
  height: 60px !important;
}
.close-icon {
  position: absolute;
  top:-30px;
  right: 20px;
}

</style>
