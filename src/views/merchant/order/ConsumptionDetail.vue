<template>
  <div class="order_detail" v-loading="isLoading">
    <div class="info">
      <div class="user_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">基本信息</span>
        </div>
        <div class="content">
          <div class="user_image">
            <el-image
              :src="
                orderData.card_face_url ? orderData.card_face_url : require('@/assets/img/man.png')
              "
              style="width: 80px; height: 50px"
              fit="contain"
              :preview-src-list="[orderData.card_face_url]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <ul>
            <li>
              <span>姓名:</span>
              <span>{{ orderData.name }}</span>
            </li>
            <li>
              <span>手机号:</span>
              <span>{{ orderData.phone }}</span>
            </li>
            <li>
              <span>分组:</span>
              <span>{{ orderData.payer_group_name }}</span>
            </li>
            <li>
              <span>人群:</span>
              <span></span>
            </li>
            <li>
              <span>人员编号:</span>
              <span>{{ orderData.person_no }}</span>
            </li>
            <li>
              <span>卡号:</span>
              <span>{{ orderData.card_no }}</span>
            </li>
            <li>
              <span>部门:</span>
              <span>{{ orderData.payer_department_group_name }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="order_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">订单信息</span>
        </div>
        <ul>
          <li>
            <span>总订单号:</span>
            <span>{{ orderData.unified_out_trade_no }}</span>
          </li>
          <li>
            <span>订单号:</span>
            <span>{{ orderData.trade_no }}</span>
          </li>
          <li>
            <span>创建时间:</span>
            <span>{{ orderData.create_time }}</span>
          </li>
          <li>
            <span>支付时间:</span>
            <span>{{ orderData.pay_time }}</span>
          </li>
          <li v-if="$route.query.type == 0">
            <span>扣款时间:</span>
            <span>{{ orderData.deduction_time }}</span>
          </li>
          <li>
            <span>第三方订单号:</span>
            <span>{{ orderData.out_trade_no }}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span>消费点:</span>
            <span>{{ isAutoCell ? orderData.primary : orderData.consumption_name }}</span>
          </li>
          <li>
            <span>餐段:</span>
            <span>{{ orderData.meal_type_alias }}</span>
          </li>
          <li v-show="$route.query.id == 1">
            <span>取餐方式:</span>
            <span>{{ orderData.take_meal_type_alias }}</span>
          </li>
          <li v-show="$route.query.id == 1">
            <span>配送地址:</span>
            <span>{{ orderData.adders_name }}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span>订单金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.origin_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>优惠金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.discount_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>餐补金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.food_subsidy_fee | formatMoney }}
            </span>
          </li>
          <li v-if="orderData.coupon_type &&$route.query.type==1">
            <span>抵扣金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.deduction_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>补贴消费:</span>
            <span>
              <i>￥</i>
              {{ orderData.subsidy_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>实收金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.pay_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>优惠类型:</span>
            <span>{{ orderData.discount_type }}</span>
          </li>
          <li>
            <span>动账钱包:</span>
            <span>{{ orderData.wallet }}</span>
          </li>
          <li  v-if="orderData.coupon_type && $route.query.type==1">
            <span>券类型:</span>
            <span>{{ orderData.coupon_type_alias }}</span>
          </li>
          <!-- EXCHANGE 兑换券 -->
          <li  v-if="orderData.coupon_type && orderData.coupon_type === 'EXCHANGE' && $route.query.type==0">
            <span>兑换内容:</span>
            <span>{{ couponExtra(orderData) }}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span>支付类型:</span>
            <span>{{ orderData.sub_payway_alias }}</span>
          </li>
          <li>
            <span>支付状态:</span>
            <span>{{ orderData.order_status_alias }}</span>
          </li>
          <li>
            <span>设备状态:</span>
            <span>{{ orderData.pay_device_status_alias }}</span>
          </li>
          <li>
            <span>对账状态:</span>
            <span>{{ orderData.settle_status_alias }}</span>
          </li>
        </ul>
      </div>
      <div class="shebei_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">设备信息</span>
        </div>
        <table border="1">
          <tbody align="center">
            <tr>
              <th>设备类型</th>
              <th>设备号</th>
              <th>设备名</th>
              <!-- <th>操作员</th> -->
            </tr>
            <tr>
              <td>{{ orderData.device_type }}</td>
              <td v-if="!isAutoCell">{{ orderData.device_number }}</td>
              <!-- 自动贩卖机设备信息 -->
              <td v-if="isAutoCell">{{ orderData.serial_no }}</td>
              <td>{{ orderData.device_name }}</td>
              <!-- <td></td> -->
            </tr>
          </tbody>
        </table>
      </div>

      <div class="menu_info flex-start">
        <div>
          <div class="l-title flex-b-c" style="width: 800px; padding-right: 0px;">
            <span class="float-l min-title-h title">{{isAutoCell || orderData.payment_order_type === 'goods_cashier' ? '商品' : '菜品'}}信息</span>
            <button-icon color="origin" v-if="(orderData.meal_scene === 'HF' || orderData.meal_scene === 'WG' || orderData.meal_scene === 'WG_TRAY' ) && orderData.order_status === 'ORDER_FAILED'" @click="originalPriceDeducted">原价扣款</button-icon>
          </div>
          <el-table
            :data="foodData"
            ref="foodData"
            style="width: 800px; padding-right: 5px"
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <el-table-column label="图片" prop="consumptionImage" align="center" v-if="!isAutoCell">
              <template slot-scope="scope">
                <el-image
                  :src="
                    scope.row.consumptionImage
                      ? scope.row.consumptionImage
                      : 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'
                  "
                  :preview-src-list="[scope.row.consumptionImage]"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="图片" prop="food_url" align="center" v-if="isAutoCell">
              <template slot-scope="scope">
                <el-image
                  :src="
                    scope.row.food_url
                      ? scope.row.food_url
                      : 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'
                  "
                  :preview-src-list="[scope.row.food_url]"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column
              :label="isAutoCell || orderData.payment_order_type ? '商品名称' : '菜品名称'"
              prop="consumptionName"
              align="center"
            ></el-table-column>
            <el-table-column label="销售价格" prop="raw_fee" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.raw_fee | formatMoney }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数量" prop="count" align="center"></el-table-column>
            <el-table-column label="总重量" prop="weight" align="center" v-if="!isAutoCell"></el-table-column>
            <el-table-column label="货道" prop="cargo_lane" align="center" v-if ="isAutoCell"></el-table-column>
            <el-table-column label="消费金额" prop="real_fee" align="center" v-if ="!isAutoCell">
              <template slot-scope="scope">
                <span>{{ scope.row.real_fee | formatMoney }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消费金额" prop="real_fee" align="center" v-if="isAutoCell">
              <template slot-scope="scope">
                <span>{{ scope.row.all_raw_fee | formatMoney }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="出货状态"
              prop="shipping_status_alias"
              align="center"
              v-if="isAutoCell"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.shipping_status_alias }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="typePath === 'consumption'" label="设备名" prop="device_name" align="center"></el-table-column>
            <el-table-column v-if="typePath === 'consumption'" label="设备地址" prop="device_mac" align="center"></el-table-column>
            <el-table-column label="营养详情" prop="" align="center" v-if="!isAutoCell">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="showDialogNutrition(scope.row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
            <el-table-column v-if="typePath === 'consumption'" label="查看" prop="" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.video_list && scope.row.video_list.length">
                  <el-button v-for="(item,index) in scope.row.video_list" :key="index" type="text" size="small" @click="clickDialogVideo(item)">视频{{ index +1}}</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="padding: 10px 10px 10px 5px;" v-if="(orderData.meal_scene === 'HF' || orderData.meal_scene === 'WG' || orderData.meal_scene === 'WG_TRAY') && orderData.order_status === 'ORDER_FAILED'">
          <span class="float-l min-title-h title" style="color: #FF9B45;font-weight: 400">默认使用消费规则后扣费，如达到上限则按原价扣费。</span>
        </div>
      </div>
      <div class="shebei_info" v-if="orderData.food_scene_image">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">菜品实况图</span>
        </div>
        <div>
          <img style="width: 40%" :src="orderData.food_scene_image" alt="" />
        </div>
      </div>
      <div
        v-if="orderData.sub_payway == 'facepay' || orderData.face_url"
        class="face_info"
        v-loading="checkFaceLoading"
      >
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">人脸支付信息</span>
        </div>
        <div class="face-wrapper m-t-20">
          <span class="face-label vertical-t">刷脸照片:</span>
          <el-image
            :src="orderData.face_url"
            class="face-img m-l-10 m-r-10"
            fit="cover"
            :preview-src-list="[orderData.face_url]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <el-button class="vertical-t" type="primary" size="small" @click="checkFaceHandle">
            在线校验
          </el-button>
          <div class="face-table-wrapper m-t-20 clearfix">
            <div class="float-l face-table m-r-20 m-b-20">
              <div class="m-b-15">刷脸结果</div>
              <el-table :data="trackInfo" stripe header-row-class-name="ps-table-header-row">
                <el-table-column
                  type="index"
                  label="排序"
                  :index="indexMethod"
                  align="center"
                ></el-table-column>
                <el-table-column label="近似人脸" prop="face_url" align="center">
                  <template slot-scope="scope">
                    <el-image :src="scope.row.face_url" :preview-src-list="[scope.row.face_url]">
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column
                  label="人员编号"
                  prop="member_card"
                  align="center"
                ></el-table-column>
                <el-table-column label="姓名" prop="card_name" align="center"></el-table-column>
                <el-table-column label="手机号" prop="card_phone" align="center"></el-table-column>
                <el-table-column label="性别" prop="gender" align="center">
                  <template slot-scope="scope">
                    {{
                      scope.row.gender === 'WOMEN' ? '女' : scope.row.gender === 'MAN' ? '男' : ''
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="识别分数" prop="score" align="center">
                  <template slot-scope="scope">
                    {{ getScore(scope.row.score) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-if="showCheckFace" class="m-b-20 float-l face-table">
              <div class="m-b-15">识别结果</div>
              <el-table
                :data="recognizedInfo"
                stripe
                header-row-class-name="ps-table-header-row"
                max-height="600px"
                height="600px"
              >
                <el-table-column
                  type="index"
                  label="排序"
                  :index="indexMethod"
                  align="center"
                ></el-table-column>
                <el-table-column label="近似人脸" prop="face_url" align="center">
                  <template slot-scope="scope">
                    <el-image :src="scope.row.face_url" :preview-src-list="[scope.row.face_url]">
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column
                  label="人员编号"
                  prop="member_card"
                  align="center"
                ></el-table-column>
                <el-table-column label="姓名" prop="card_name" align="center"></el-table-column>
                <el-table-column label="手机号" prop="card_phone" align="center"></el-table-column>
                <el-table-column label="性别" prop="gender" align="center">
                  <template slot-scope="scope">
                    {{
                      scope.row.gender === 'WOMEN' ? '女' : scope.row.gender === 'MAN' ? '男' : ''
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="识别分数" prop="score" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.score.toFixed(2) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <div class="">
        <el-dialog
          title="营养信息"
          custom-class="ps-dialog"
          :visible.sync="dialogVisible"
          width="70%"
        >
          <!-- <el-table
          :data="nutritionData"
          border
          stripe
          header-row-class-name="ps-table-header-row">
            <el-table-column v-for="item in nutritionList" :label="item.name" :key="item.key" :prop="item.key" ></el-table-column>
          </el-table> -->
          <el-form v-loading="isLoading" :model="nutritionData" class="" size="small">
            <!-- 营养 start -->
            <div>
              <template v-for="nutrition in nutritionList">
                <div class="nutrition-item" :key="nutrition.key">
                  <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
                  <el-form-item :prop="nutrition.key">
                    <el-input
                      style="width: 120px"
                      readonly
                      v-model="nutritionData[nutrition.key]"
                      class="ps-input"
                    ></el-input>
                    <span style="margin-left: 10px">{{ nutrition.unit }}</span>
                  </el-form-item>
                </div>
              </template>
            </div>
            <!-- 营养 end -->
          </el-form>
        </el-dialog>
        <!-- 视频 -->
        <el-dialog
          v-if="dialogVideoVisible"
          title="视频"
          :visible.sync="dialogVideoVisible"
          width="30%"
          top="20vh"
          custom-class="ps-dialog"
          :close-on-click-modal="true">
          <div>
              <videoPlayer height="500" :src="videoStc"/>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="ps-cancel-btn" @click="dialogVideoVisible = false">取 消</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { to, replaceSingleQuote } from '@/utils'
import { type } from '@/utils/type'
import { NUTRITION_LIST } from '@/views/merchant/meal-management/food-admin/constants'

export default {
  name: 'orderDetail',
  data() {
    return {
      isLoading: false,
      dialogVisible: false,
      dialogVideoVisible: false,
      videoStc: "", // 视频地址
      orderData: {},
      detail_obj: {},
      foodData: [],
      tableSetting: [
        { label: '图片', key: 'food_img', width: '100px' },
        // { label: '识别图片', key: 'food_img', width: '100px' },
        { label: '菜品名称', key: 'food_name', width: '100px' },
        { label: '销售价格', key: 'real_fee', width: '100px' },
        { label: '数量', key: 'count', width: '100px' },
        { label: '重量', key: 'weight', width: '100px' },
        { label: '消费金额', key: 'raw_fee', width: '100px' },
        { label: '营养详情', key: 'detailChange', width: '100px' }
      ],
      nutritionList: NUTRITION_LIST,
      nutritionData: {},
      checkFaceLoading: false,
      showCheckFace: false,
      faceTraceback: null,
      trackInfo: [], // 刷脸结果
      recognizedInfo: [], // 识别结果
      isAutoCell: false, // 是否是自动售卖机
      typePath: ''
    }
  },
  created() {
    if (this.$route.query && Reflect.has(this.$route.query, 'isAutoCell')) {
      this.isAutoCell = this.$route.query.isAutoCell
    }
    if (this.$route.query.type_path) {
      this.typePath = this.$route.query.type_path
    }
    this.getOrderDetail()
  },
  methods: {
    async getOrderDetail() {
      this.isLoading = true
      var [err, res] = []
      if (this.isAutoCell) {
        ;[err, res] = await to(
          this.$apis.apiBackgroundOrderVendingMachineOrderDetailPost({
            order_payment_id: this.$route.query.id
          })
        )
      } else {
        ;[err, res] = await to(
          this.$apis.apiBackgroundOrderOrderPaymentDetailListPost({
            order_payment_id: this.$route.query.id
          })
        )
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.orderData = res.data
        console.log('orderData', this.orderData)
        if (res.data.food_info.length) {
          this.foodData = res.data.food_info.map(v => {
            v.consumptionImage = v.food_img
            v.consumptionName = v.food_name
            return v
          })
        }
        if (res.data.goods_info.length) {
          this.foodData = res.data.goods_info.map(v => {
            v.consumptionImage = v.goods_img
            v.consumptionName = v.goods_name
            return v
          })
        }
        this.trackInfo = res.data && res.data.face_user_list ? res.data.face_user_list : []
        console.log('this.trackInfo', this.trackInfo)
      } else {
        this.$message.error(res.msg)
      }
    },
    showDialogNutrition(rowNutrition) {
      this.nutritionData = {}
      if (!rowNutrition) {
        // 防止nutrition没值的情况
        rowNutrition = {}
      }
      let element = {}
      let vitamin = {}
      // 什么时候更了新版？返回对象了，之前不是一直返回字符串吗
      if (type(rowNutrition.element) === 'object') {
        element = rowNutrition.element
      } else {
        element = rowNutrition.element
          ? JSON.parse(replaceSingleQuote(rowNutrition.element))
          : {}
      }
      if (type(rowNutrition.element) === 'object') {
        vitamin = rowNutrition.vitamin
      } else {
        vitamin = rowNutrition.vitamin
          ? JSON.parse(replaceSingleQuote(rowNutrition.vitamin))
          : {}
      }
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.nutritionData, nutrition.key, rowNutrition[nutrition.key])
        }
        if (nutrition.type === 'element') {
          this.$set(this.nutritionData, nutrition.key, element[nutrition.key])
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.nutritionData, nutrition.key, vitamin[nutrition.key])
        }
      })
      this.dialogVisible = true
    },
    clickDialogVideo(item) {
      this.dialogVideoVisible = true
      this.videoStc = item
    },
    async checkFaceHandle() {
      this.checkFaceLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderPaymentFaceTracebackPost({
          order_payment_id: this.$route.query.id,
          organization_id: this.orderData.organization_id
        })
      )
      await this.$sleep(1000)
      this.checkFaceLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      this.showCheckFace = true
      if (res.code === 0) {
        // this.faceTraceback = res.data
        // this.trackInfo = res.data && res.data.track_info ? res.data.track_info : []
        this.recognizedInfo = res.data && res.data.recognized_info ? res.data.recognized_info : []
      } else {
        this.$message.error(res.msg)
      }
    },
    indexMethod(index) {
      return index + 1
    },
    showHomeFn() {
      // this.$emit('toOrderDetailFn', true)
      // console.log(this.current)
      // 返回form的路由
      this.$closeCurrentTab(this.$route.path)
    },
    // 获取分数
    getScore(score) {
      if (score && typeof score === 'number') {
        return score.toFixed(2)
      } else if (score && typeof score === 'string') {
        return parseFloat(score).toFixed(2)
      }
      return score
    },
    // 手动扣款
    originalPriceDeducted() {
      this.$apis.apiBackgroundOrderOrderPaymentBuffetOrderPayingPost({
        order_id: this.$route.query.order_id ? this.$route.query.order_id : ''
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('扣款成功')
          this.getOrderDetail()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    couponExtra(data) {
      let nameList = data.coupon_extra.map(v => {
        return v.name
      })
      return nameList.join(',')
    }
  }
}
</script>

<style lang="scss">
i {
  font-style: normal;
}
.order_detail {
  background-color: #fff;
  margin-top: 20px;
  padding-top: 10px;
  .info {
    font-size: 14px;
    margin-left: 30px;
    padding-bottom: 300px;
    .user_info {
      font-weight: bold;
      margin-bottom: 30px;
      .title {
        margin: 15px 0;
      }
      .content {
        display: flex;
        .user_image {
          // border: 1px solid #cbcbcb;
          width: 80px;
          height: 50px;
          margin-right: 30px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        ul {
          display: inline-block;
          vertical-align: top;
          width: 600px;
          li {
            float: left;
            margin: 0 50px 10px 0;
            span {
              &:nth-of-type(2) {
                display: inline-block;
                text-indent: 8px;
              }
            }
          }
        }
      }
    }
    .order_info {
      margin-bottom: 30px;
      .title {
        margin: 15px 0;
      }
      font-weight: bold;
      ul {
        height: 20px;
        margin-bottom: 6px;
        li {
          float: left;
          margin-right: 30px;
          span {
            &:nth-of-type(2) {
              display: inline-block;
              text-indent: 8px;
            }
          }
        }
      }
    }
    .shebei_info .title,
    .menu_info .title {
      margin: 15px 0;
      font-weight: bold;
    }
    .menu_info {
      margin: 30px 0;
    }
    .vertical-t {
      vertical-align: top;
    }
    .face-wrapper {
      .face-img {
        width: 100px;
        height: 120px;
        // text-align: center;
        // background-color: ;
        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;
          font-size: 14px;
        }
      }
    }
    table {
      border-collapse: collapse;
      tr th,
      tr td {
        width: 100px;
      }
      tr td {
        height: 35px;
      }
    }
    .nutrition_info {
      table {
        tr,
        td,
        th {
          border: 1px solid #ccc;
        }
      }
    }
  }
}
.ps-dialog {
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
</style>
