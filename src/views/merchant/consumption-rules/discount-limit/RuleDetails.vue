<template>
  <div class="discount-limit">
    <div class="booking-meal-wrapper container-wrapper">
      <search-form ref="searchRef" :loading="isLoading" @search="searchHandle"
        :form-setting="searchFormSetting" @reset="handlerReset"></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            <span>数据列表</span>
          </div>
          <div class="align-r">
            <button-icon color="origin" type="export" @click="gotoExport" v-permission="['background_marketing.discount_limit.discount_limit_cardinfo_mealtype_list_export']">
              导出
            </button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row"
            @expand-change="handleTableExpand" :expand-row-keys="currentExpandRow" row-key="id"
            >
            <el-table-column type="expand">
              <template #default="{ row }">
                <div>
                  <el-table :data="row.tableDataMealType" stripe header-row-class-name="ps-table-header-row" v-loading="isLoadingMealType">
                    <table-column v-for="(itemMealType, indexMealType) in tableSettingMealType" :key="indexMealType"
                      :col="itemMealType">
                      <template #meal_type_alias="{ row }">
                        {{ getMealTypeStr(row) }}
                      </template>
                    </table-column>
                  </el-table>
                  <pagination :onPaginationChange="onPaginationChangeMealType" :current-page.sync="currentPageMealType"
                    :page-size.sync="pageSizeMealType" :layout="'total, prev, pager, next, jumper'"
                    :total="totalMealType">
                  </pagination>
                </div>
              </template>

            </el-table-column>
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #update_time="{ row }">
                {{ getRangeTime(row) }}
              </template>
            </table-column>
          </el-table>
          <!-- table content end -->
        </div>
        <!-- 分页 start -->
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'" :total="total"></pagination>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { cloneDeep } from 'lodash';
export default {
  name: 'RuleDetails',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false,
      isLoadingMealType: false,
      tableSetting: [
        { label: '规则名称', key: 'discount_limit_name' },
        { label: '限制周期', key: 'update_time', type: 'slot', slotName: 'update_time' },
        { label: '姓名', key: 'person_name' },
        { label: '人员编号', key: 'person_no' }
      ],
      tableSettingMealType: [
        { label: '餐段', key: 'meal_type_alias', type: 'slot', slotName: 'meal_type_alias' },
        { label: '使用时间', key: 'limit_date' },
        { label: '限制额度', key: 'discount_fee', type: 'money' },
        { label: '使用额度', key: 'use_discount_fee', type: 'money' },
        { label: '使用总额度', key: 'use_total_discount_fee', type: 'money' },
        { label: '剩余额度', key: 'remaining_fee', type: 'money' },
        { label: '限制次数', key: 'discount_num' },
        { label: '使用次数', key: 'use_num' },
        { label: '使用总次数', key: 'use_total_num' },
        { label: '剩余次数', key: 'remaining_num' }
      ],
      tableData: [],
      tableDataMealType: [],
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      currentPageMealType: 1,
      pageMealType: 1,
      pageSizeMealType: 10,
      totalMealType: 0,
      searchFormSetting: {
        discount_limit_name: {
          type: 'input',
          label: '规则名称',
          value: '',
          placeholder: '请输入规则名称'
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入名称'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        }
      },
      showLimitFormDialog: false, // 是否显示弹窗
      tabType: 'rule',
      limitId: '',
      currentExpandRow: []
    }
  },
  created() {
    this.getDiscountLimitList()
  },
  mounted() { },
  methods: {
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getDiscountLimitList()
    },
    handlerReset() {
      this.searchHandle()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getDiscountLimitList()
    }, 300),
    // 获取列表数据
    async getDiscountLimitList() {
      this.isLoading = true
      // params
      const [err, res] = await to(this.$apis.apiBackgroundMarketingDiscountLimitDiscountLimitCardinfoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        return this.$message.error("获取数据失败")
      }
      if (res && res.code === 0) {
        this.total = res.data.count
        this.tableData = cloneDeep(res.data.results)
        this.currentExpandRow = []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表数据
    async getDiscountLimitMealType(id) {
      this.isLoadingMealType = true
      this.limitId = id
      this.currentExpandRow = [id]
      // params
      const [err, res] = await to(this.$apis.apiBackgroundMarketingDiscountLimitDiscountLimitCardinfoMealtypeListPost({
        discount_limit_card_info_id: id,
        page: this.currentPageMealType,
        page_size: this.pageSizeMealType
      }))
      console.log("getDiscountLimitMealType", res);
      this.isLoadingMealType = false
      if (err) {
        return this.$message.error("获取数据失败")
      }
      if (res && res.code === 0) {
        this.totalMealType = res.data.count
        this.tableDataMealType = res.data.results
        for (let index = 0; index < this.tableData.length; index++) {
          const element = this.tableData[index];
          if (element.id === id) {
            this.$set(this.tableData[index], 'tableDataMealType', res.data.results)
            // this.$set(this.tableData[index], 'isLoadingMealType', false)
            break
          }
        }
        console.log("getDiscountLimitMealType", this.tableData);
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 下载导出
    gotoExport() {
      const option = {
        type: 'ExportLimitDiscount',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 获取有效时间
    getRangeTime(row) {
      if (row && row.limit_start_time && row.limit_end_time) {
        return row.limit_start_time + '~' + row.limit_end_time
      }
      return '--'
    },
    // 展开
    handleTableExpand(row) {
      let currentId = this.currentExpandRow[0] ? this.currentExpandRow[0] : ''
      if (currentId === row.id) {
        this.currentExpandRow = []
        return
      }
      console.log("handleTableExpand", row);
      this.getDiscountLimitMealType(row.id)
    },
    // 内部页面翻页
    onPaginationChangeMealType(val) {
      this.currentPageMealType = val.current
      this.pageSizeMealType = val.pageSize
      this.getDiscountLimitMealType(this.limitId)
    },
    // 翻页
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDiscountLimitList()
    },
    // 获取餐段数据
    getMealTypeStr(row) {
      console.log("getMealTypeStr", row);
      if (row && row.meal_type_alias && row.meal_type_alias.length > 0) {
        return row.meal_type_alias.join('、')
      }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped></style>
