<template>
  <div id="organization" class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- <div class="search-form ps-search-form">
      <div class="search-item">
        <el-input v-model="searchForm.name" @input="searchHandle" clearable :placeholder="$t('placeholder.role_search')" class="ps-input"></el-input>
        <el-button @click="searchHandle" icon="el-icon-search" type="primary" class="ps-origin-btn" style="margin-left: 10px;">{{ $t('search.btn') }}</el-button>
      </div>
    </div> -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          row-key="id"
          stripe
          :expand-row-keys="expands"
          lazy
          :load="load"
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'has_children' }"
        >
          <el-table-column prop="name" show-overflow-tooltip :label="$t('table.department')"></el-table-column>
          <el-table-column prop="role_num" width="120" show-overflow-tooltip align="right" :label="$t('table.role_num')"></el-table-column>
          <el-table-column prop="account_num" width="90" align="right" :label="$t('table.account_num')"></el-table-column>
          <el-table-column prop="create_time" width="170" align="right" :label="$t('table.create_time')" ></el-table-column>
          <el-table-column class-name="tools-row" align="center" width="180" :label="$t('table.operate')">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialogHaldler('edit', scope.row)"
                v-permission="['background.admin.department.modify']"
              >
                {{ $t('table.edit') }}
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="openDialogHaldler('lower', scope.row)"
                v-permission="['background.admin.department.add']"
              >
                {{ $t('form.add_department') }}
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                v-if="scope.row.level!==0"
                @click="deleteHaldler('one', scope.row)"
                v-permission="['background.admin.department.delete']"
              >
                {{ $t('table.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <!-- 分页 end -->
    <!-- 编辑/添加弹窗 start -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="380px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @closed="dialogHandleClose"
    >
      <el-form
        ref="formData"
        v-loading="formLoading"
        :rules="formDataRuls"
        :model="formData"
        class="dialog-form"
        label-width="80px"
      >
        <el-form-item v-if="dialogType === 'add'" :label="$t('form.add_department')">
          <el-input size="small" class="ps-input" v-model="formData.parentName" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item v-if="(dialogType === 'edit' || dialogType === 'lower') && formData.parentName" :label="$t('form.superior_department')">
          <el-input size="small" class="ps-input" v-model="formData.parentName" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item :label="dialogType === 'edit' ? $t('form.modify_department') : $t('form.add_department')" prop="name">
          <el-input size="small" class="ps-input" v-model="formData.name"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" size="small" :disabled="formLoading" @click="dialogVisible = false">
          {{ $t('form.cancel_btn') }}
        </el-button>
        <el-button
          class="ps-btn"
          :disabled="formLoading"
          type="primary"
          size="small"
          @click="submitDialogHandler"
        >
          {{ dialogType==='edit' ? $t('form.confirm_btn') : $t('form.add_btn') }}
        </el-button>
      </span>
    </el-dialog>
    <!-- 弹窗 end -->
  </div>
</template>

<script>
import { to, debounce } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'SuperDepartment',
  // mixins: [activatedLoadData],
  data() {
    return {
      dialogTitle: '',
      dialogType: '',
      dialogVisible: false,
      searchForm: {
        name: ''
      },
      formData: {
        id: '',
        parentId: '',
        parentName: '',
        name: ''
      },
      tableData: [],
      formDataRuls: {
        name: [
          { required: true, message: this.$t('placeholder.organization_name'), trigger: "blur" }
        ]
        // user_name: [
        //   { required: true, validator: accountValidator , trigger: "blur" },
        // ],
      },
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      pageCount: 0,
      formLoading: false,
      type: '',
      isLoading: false, // 刷新数据
      expands: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '',
          value: '',
          placeholder: '请输入部门'
        }
      }
    }
  },
  created () {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
      this.getOrganizationList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getOrganizationList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // 额外参数重置
      this.searchForm.id = ''
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getOrganizationList() {
      this.isLoading = true
      let params = {
        status__in: 'enable',
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.searchFormSetting.name.value !== '') {
        params.name__contains = this.searchFormSetting.name.value
      } else {
        // params.parent__is_null = '1'
      }
      try {
        const res = await this.$apis.apiBackgroundAdminDepartmentListPost(params)
        this.isLoading = false
        if (res.code === 0) {
          this.totalCount = res.data.count
          if (!this.searchFormSetting.name.value) {
            this.tableData = res.data.results
            if (this.tableData.length > 0 && this.tableData[0].has_children) {
              this.$nextTick(() => {
                // 只能通过class去触发click事件了
                const expandEls = document.querySelectorAll('.el-table__expand-icon')
                if (!expandEls[0].classList.contains('el-table__expand-icon--expanded')) {
                  expandEls[0] && expandEls[0].click()
                }
              })
            }
          } else {
            this.tableData = res.data.results.map(v => {
              v.has_children = false
              return v
            })
          }
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error(error.message)
      }
    },
    // 动态加载远程数据
    async load(tree, treeNode, resolve) {
      const [err, res] = await to(this.$apis.apiBackgroundAdminDepartmentListPost({
        parent__in: tree.id,
        status__in: 'enable',
        page: 1,
        page_size: 99999
      }));
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    dialogHandleClose() {
      this.resetFormData()
      this.dialogVisible = false
      this.dialogType = ''
    },
    resetFormData() {
      this.formData.parentId = ''
      this.formData.parentName = ''
      this.formData.id = ''
      this.formData.name = ''
      // this.$refs.formData.resetFields()
      this.$refs.formData.clearValidate()
    },
    openDialogHaldler(type, row) {
      this.dialogType = type
      if (type === 'add') {
        // this.dialogTitle = '新增组织'
      } else if (type === 'lower') {
        this.dialogTitle = this.$t('dialog.add_title')
        this.formData.parentId = row.id
        this.formData.parentName = row.name
      } else {
        this.formData.id = row.id
        this.dialogTitle = this.$t('dialog.edit_title')
        this.formData.name = row.name
        this.formData.parentId = row.parent
        this.formData.parentName = row.parent_alias
      }
      this.dialogVisible = true
    },
    submitDialogHandler() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.dialogType === 'add') {
            this.addHandler({
              // parent: this.formData.parentId,
              name: this.formData.name
            })
          } else if (this.dialogType === 'lower') {
            this.addHandler({
              parent: this.formData.parentId,
              name: this.formData.name
            })
          } else {
            this.modifyHandler({
              id: this.formData.id,
              parent: this.formData.parentId,
              name: this.formData.name
            })
          }
        } else {
          // console.log('error submit!!');
        }
      })
    },
    async addHandler(params) {
      this.formLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminDepartmentAddPost(params))
      this.formLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (params.parent) {
          this.uploadTableTree(params.parent)
        }
        this.dialogVisible = false
        this.$message.success('添加成功')
        // this.groupList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async modifyHandler(params) {
      // this.changeTableTreeNode(params.id, true, params)
      // return;

      // eslint-disable-next-line no-unreachable
      this.formLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminDepartmentModifyPost(params))
      this.formLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success('修改成功')
        this.changeTableTreeNode(params.id, false, params)
        // this.getOrganizationList()
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHaldler(type, data) {
      let delId = ''
      if (type === 'one') {
        delId = data.id
      }
      // this.isLoading = true
      // this.changeTableTreeNode(delId,true,data)
      // return
      this.$confirm('是否删除该组织架构？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminDepartmentDeletePost({
                ids: [delId]
              })
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success('删除成功')
              this.getOrganizationList()
              this.changeTableTreeNode(data.id, true, data)
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    changeTableTreeNode(id, del = false, data) {
      let lazyTreeNodeMap = this.$refs.tableData.store.states.lazyTreeNodeMap
      const keys = Object.keys(lazyTreeNodeMap)
      // return
      let isLazyTree = false
      for (let i = 0; i < keys.length; i++) {
        if (lazyTreeNodeMap[keys[i]] && lazyTreeNodeMap[keys[i]].length > 0) {
          lazyTreeNodeMap[keys[i]].map((item, k) => {
            if (item.id === id) {
              isLazyTree = true
              this.uploadTableTree(data.parent)
              // eslint-disable-next-line no-useless-return
              return
            }
          })
        }
        if (isLazyTree) break;
      }
    },
    async uploadTableTree(id) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminDepartmentListPost({
          parent__in: id,
          status__in: 'enable',
          page: 1,
          page_size: 99999
        })
      )
      this.isLoading = false
      if (err) {
        return
      }
      if (res.code === 0) {
        this.$set(this.$refs.tableData.store.states.lazyTreeNodeMap, id, res.data.results)
      } else {
      }
    },
    formatter(row, column) {
      return row.address
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.getOrganizationList()
    },
    closeImportDialog() {}
  }
}
</script>

<style lang="scss">
// @import '@/assets/css/origin.scss';
#organization {
  .header-box {
    padding: 10px 0 25px;
    text-align: right;
  }
  .ps-table-tree{
    .ps-table-header-row{
      background: #f5f6fa;
      th{
        background: #f5f6fa;
      }
    }
    .el-tree-node__expand-icon.expanded{
        // transform: rotate(0deg);
    }
    .el-icon-arrow-right{
      vertical-align: middle;
    }
    //有子节点 且未展开
    .el-icon-arrow-right:before {
      background: url('~@/assets/img/zz2.png') no-repeat 0 0px;
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      font-size: 16px;
      background-size: 16px;
    }
    // 0级
    .el-table__row--level-0 .el-icon-arrow-right:before {
      background: url('~@/assets/img/zz4.png') no-repeat 0 0px;
    }
    .el-table__row--level-1 .el-icon-arrow-right:before {
      background: url('~@/assets/img/zz3.png') no-repeat 0 0px;
    }
    //有子节点 且已展开
    .el-table__expand-icon--expanded {
      .el-icon-arrow-right:before {
        background: url('~@/assets/img/zz2.png') no-repeat 0 0;
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        font-size: 16px;
        background-size: 16px;
      }
    }
    .el-table__row--level-0 {
      .el-table__expand-icon--expanded {
        .el-icon-arrow-right:before {
          background: url('~@/assets/img/zz1.png') no-repeat 0 0;
        }
      }
    }
    //没有子节点
    // .el-tree-node__expand-icon.is-leaf::before
    // .el-table__placeholder::before {
    //   // background: url('./images/file.png') no-repeat 0 0;
    //   content: '';
    //   display: block;
    //   width: 16px;
    //   height: 18px;
    //   font-size: 16px;
    //   background-size: 16px;
    // }
    .el-table__expand-icon--expanded{
      transform:rotate(180deg);
    }
  }
  .dialog-select {
    width: 100%;
  }
  .dialog-form {
    max-width: 400px;
  }
  .el-table .cell.el-tooltip {
    text-overflow: ellipsis;
  }
  .no-children {
    .th-row {
      .cell {
        padding-left: 32px;
      }
    }
  }
  .el-table__body-wrapper .tools-row {
    .cell{
      text-align: left;
    }
    .el-button {
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          right: -8px;
          width: 1px;
          height: 10px;
          background: #ebebeb;
          transform: translateY(-50%);
        }
      }
    }
    .delete {
      color: #ff0000;
    }
  }
  .dataTemplate {
    padding: 0px 0px 20px 40px;
  }
  .importValue {
    padding: 15px 0 10px 40px;
    display: flex;
  }
}
.dialog-footer {
  width: 100%;
  text-align: right;
}
</style>
