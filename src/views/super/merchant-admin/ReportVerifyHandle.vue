<template>
  <div class="ReportVerifyHandle container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :loading="isLoading"
      @search="searchHandle"
      :form-setting="searchFormSetting"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="openDialog('all')">全部生成</button-icon>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column  v-for="item in tableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialog('part', row)"
                >重新生成</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- table content end -->
    </div>
    <!-- 拒绝弹窗 -->
    <el-dialog
      title="重新生成"
      :visible.sync="dialogVisible"
      width="600px"
      customClass="ps-dialog"
      >
      <div v-loading="isLoading">
        <el-form
          :model="dialogForm"
          ref="dialogFormRef"
          :rules="dialogFormRules"
          label-width="100px"
          class="jiaofei-form"
        >
          <el-form-item label="数据时间：" prop="selectTime">
            <el-date-picker
              v-model="dialogForm.selectTime"
              type="daterange"
              align="left"
              unlink-panels
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              clearable
              class="ps-poper-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="选择报表：" prop="reportType">
            <el-radio-group class="ps-radio report-radio" v-model="dialogForm.reportType">
              <el-radio v-for="(value, key) in reportTypeList" :key="key" :label="key">{{value.name}}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div class="tips" v-if="dialogType === 'all'">注：全部生成时不显示脚本的运行状态</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="dialogVisible = false" :disabled="isLoading">取 消</el-button>
        <el-button type="primary" class="ps-btn" @click="confirmDialog" :disabled="isLoading">立即执行</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'

export default {
  name: 'ReportVerifyHandle',
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: "80" },
        { key: 'company_name', label: '项目名称' },
        { key: 'yingyee', label: '营业额日报表' },
        { key: 'gerenxiaofei', label: '个人消费汇总' },
        { key: 'bumenxiaofei', label: '部门消费汇总表' },
        { key: 'qianbaoribaobiao', label: '个人钱包日报表、账户钱包日报表' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '项目名称',
          placeholder: '请输入项目名称'
        }
      },
      dialogForm: {
        selectTime: [],
        reportType: ''
      },
      reportTypeList: {
        yingyee: {
          name: '营业额日报表',
          api: 'apiBackgroundAdminScriptReportFixOrderBusinessReportPost'
        },
        gerenxiaofei: {
          name: '个人消费汇总',
          api: 'apiBackgroundAdminScriptReportFixPersonDepartmentPaymentReportPost'
        },
        bumenxiaofei: {
          name: '部门消费汇总表',
          api: 'apiBackgroundAdminScriptReportFixPersonDepartmentPaymentReportPost'
        },
        qianbaoribaobiao: {
          name: '个人钱包日报表、账户钱包日报表',
          api: 'apiBackgroundAdminScriptReportFixCardWalletPersonDailyReportPost'
        }
      },
      dialogFormRules: {
        selectTime: [{ required: true, message: '请选择数据时间', trigger: 'blur' }],
        reportType: [{ required: true, message: '请选择报表', trigger: 'blur' }]
      },
      dialogType: '',
      dialogVisible: false,
      selectInfo: {},
      timer: null,
      reportStatus: 0 // 0表示没有任务在执行
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getAllCompany()
    },
    // 刷新页面
    refreshHandle() {
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getAllCompany()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getAllCompany() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminScriptReportGetAllCompanyPost(this.formatQueryParams(this.searchFormSetting))
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = []
        res.data.map(item => {
          this.tableData.push({
            company_id: item[0],
            company_name: item[1],
            yingyee: '',
            gerenxiaofei: '',
            bumenxiaofei: '',
            qianbaoribaobiao: ''
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    openDialog(type, data) {
      if (this.reportStatus) this.$message.error('报表任务执行中')
      this.dialogType = type
      this.selectInfo = {}
      this.dialogForm.selectTime = []
      this.dialogForm.reportType = ''
      if (data) this.selectInfo = data
      this.dialogVisible = true
    },
    confirmDialog() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          let params = {
            start_date: this.dialogForm.selectTime[0],
            end_date: this.dialogForm.selectTime[1]
          }
          if (this.dialogType === 'part') params.company_id = this.selectInfo.company_id
          this.scriptReport(params)
        }
      })
    },
    async scriptReport(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis[this.reportTypeList[this.dialogForm.reportType].api](params)
      this.isLoading = false
      this.dialogVisible = false
      if (res.code === 0) {
        this.reportStatus = 1
        if (this.dialogType === 'part') {
          this.tableData.map(item => {
            if (item.company_id === this.selectInfo.company_id) {
              item[this.dialogForm.reportType] = '正在生成'
            }
            return item
          })
        }
        this.getRepotResult(res.data.query_id)
        this.timer = setInterval(() => {
          this.getRepotResult(res.data.query_id)
        }, 10000)
      } else {
        this.$message.error(res.msg)
        this.isLoading = false
      }
    },
    async getRepotResult(queryId) {
      await this.$sleep(2000)
      const res = await this.$apis.apiBackgroundAdminScriptReportScriptResultQueryPost({
        query_id: queryId
      })
      if (res.code === 0) {
        let text
        if (res.data.status === 'success') {
          this.$message.success(res.msg)
          text = '成功'
          clearInterval(this.timer)
          this.reportStatus = 0
        } else if (res.data.status === 'failure') {
          this.$message.error(res.msg)
          text = '失败'
          clearInterval(this.timer)
          this.reportStatus = 0
        }
        if (Object.keys(this.selectInfo).length && res.data.status !== 'processing') {
          this.tableData.map(item => {
            if (item.company_id === this.selectInfo.company_id) {
              item[this.dialogForm.reportType] = text
            }
            return item
          })
        }
      } else {
        this.$message.error(res.msg)
        clearInterval(this.timer)
        this.reportStatus = 0
      }
    }
  }
}
</script>
<style lang="scss">
.report-radio{
  .el-radio{
    display: block;
    margin-top: 15px;
  }
}
.tips{
  color: #ff9b45;
  margin-left: 20px;
}

</style>
