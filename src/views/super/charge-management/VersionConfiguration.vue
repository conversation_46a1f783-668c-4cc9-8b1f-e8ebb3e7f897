<template>
  <div class="container-wrapper" style="height: 100%;">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="version-configuration">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="version-configuration-sidebar bg-style"  v-loading="isLoading">
            <div class="p-t-20 p-b-20 version-configuration-sidebar-btn w-100-p">
              <el-button size="small" type="primary" class="w-100" @click="selectThisVersion('add')">添加版本</el-button>
            </div>
            <transition-group name="el-zoom-in-center" tag="div" class="version-configuration-sidebar-content">
              <div class="version-configuration-sidebar-item flex-b-c" v-for="(item, index) in versionList" :key="index" @click="getVersionInfo(item)">
                <div style="width: 60%;">
                  <span @click="selectThisVersion('edit', item)">
                    {{ item.name }}
                  </span>
                </div>
                <div class="flex-a-c" style="width: 40%;">
                  <div class="flex-a-c" style="width: 60%;">
                    <div class="button-area flex-center" @click="swapElements('up', index, item)">
                      <!-- <i :class="['el-icon-arrow-up', index > 0 && index <= versionList.length - 1 ? 'enable-up' : 'disable']"></i> -->
                      <svg-icon icon-class="button_up" :class="[ index > 0 && index <= versionList.length - 1 ? 'enable-up' : 'disable']" style="width: 16px; height: 16px;" />
                    </div>
                    <div class="button-area flex-center" @click="swapElements('down', index, item)">
                      <!-- <i :class="['el-icon-arrow-down', index >= 0 && index < versionList.length - 1 ? 'enable-down' : 'disable']"></i> -->
                      <svg-icon icon-class="button_down" :class="[ index >= 0 && index < versionList.length - 1 ? 'enable-down' : 'disable']" style="width: 16px; height: 16px;" />
                    </div>
                  </div>
                  <div class="button-area flex-center" @click="delItem(item)">
                    <i class="el-icon-delete" :style="item.org_count === 0 ? {color: 'red'} : {color: '#D7D7D7'}"></i>
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="version-configuration-content p-20 bg-style">
            <!-- 商户后台和商户移动端的配置 -->
            <div v-if="isSelectVersion">
              <div class="version-configuration-content-head">
                <el-button size="small" :type="selectType === 'Merchant' ? 'primary' : ''" class="w-100 m-b-20" @click="selectType = 'Merchant'">商户后台</el-button>
                <el-button size="small" :type="selectType === 'UserPhone' ? 'primary' : ''" class="w-100 m-b-20" @click="selectType = 'UserPhone'">用户移动端</el-button>
                <el-button size="small" :type="selectType === 'MerchantPhone' ? 'primary' : ''" class="w-100 m-b-20" @click="selectType = 'MerchantPhone'">商户移动端</el-button>
              </div>
              <el-tabs type="card" tab-position="left" class="version-configuration-content-box" v-if="selectType !== 'UserPhone'" v-loading="isLoading">
                <el-tab-pane :label="item.verbose_name" v-for="(item, index) in selectType === 'Merchant' ? merchantFeatureList : merchantMobileFeatureList" :key="index">
                  <div class="p-20">
                    <div class="m-b-20">
                      <el-checkbox
                        v-model="item.isSelect"
                        class="ps-flex-align-c flex-align-c"
                        :indeterminate="item.isIndeterminate"
                        :disabled="isSave"
                        @change="selectHandle(item.isSelect, item, selectType, index, true)">
                        <span class="font-size-18 f-w-500">全选当前页</span>
                      </el-checkbox>
                    </div>
                    <div v-for="(item1, index1) in item.children" :key="index1" class="m-b-20">
                      <el-checkbox
                        v-model="item1.isSelect"
                        class="ps-flex-align-c flex-align-c m-b-10"
                        :indeterminate="item1.isIndeterminate"
                        :disabled="isSave"
                        @change="selectHandle(item1.isSelect, item1, selectType, index, false)">
                        <span class="font-size-18 f-w-700">{{item1.verbose_name}}</span>
                      </el-checkbox>
                      <div class="box-item p-t-20 p-l-20 p-r-20" v-if="item1.children.length">
                        <el-row :gutter="20">
                          <el-col :span="6" v-for="(item2, index2) in item1.children" :key="index2">
                            <el-checkbox
                              v-model="item2.isSelect"
                              :indeterminate="item2.isIndeterminate"
                              class="m-b-20"
                              :disabled="isSave"
                              @change="selectHandle(item2.isSelect, item2, selectType, index, false)">
                              <span class="ellipsis w-180">{{ item2.verbose_name }}</span>
                            </el-checkbox>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
              <!-- 用户移动端的配置 -->
              <div v-else class="version-configuration-content-box2" v-loading="isLoading">
                <div class="p-20">
                  <div class="box-item p-t-20 p-l-20 p-r-20">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="(item, index) in userPhoneList" :key="index">
                        <el-checkbox v-model="item.isSelect" class="m-b-20" :disabled="isSave" @change="selectHandle(item.isSelect, item, selectType, index, false)">
                          <span class="ellipsis w-180">{{ item.verbose_name }}</span>
                        </el-checkbox>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div class="version-configuration-content-footer m-t-20 m-b-20">
                <div class="button-area m-r-40">
                  <el-button size="small" class="w-100" @click="editOrCancelHandle(isSave)">{{isSave ? '编辑' : '取消'}}</el-button>
                  <el-button v-if="!isSave" size="small" type="primary" class="w-100" @click="save">保存</el-button>
                </div>
                <div class="checkbox-area m-r-40">
                  <el-checkbox v-model="selectAll" @change="isSelectAll(selectType, 'selectAll', true)" :disabled="isSave">
                    <span class="font-size-16">全选</span>
                  </el-checkbox>
                  <el-checkbox v-model="selectNone" @change="isSelectAll(selectType, 'selectNone', false)" :disabled="isSave">
                    <span class="font-size-16">全不选</span>
                  </el-checkbox>
                </div>
                <div>定制数量：{{ computedSelectCount(selectType) }}/{{ computedTotalCount(selectType) }}</div>
              </div>
            </div>
            <div class="flex-center" style="height: 100%" v-else>
              <el-empty description="请先选择版本"></el-empty>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="ps-el-drawer">
      <el-drawer
        :title="drawerType === 'add' ? '新增版本' : '编辑版本'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerForm" :model="drawerForm" :rules="rules" label-width="80px">
            <el-form-item label="版本名称" prop="name">
              <el-input v-model="drawerForm.name" class="w-250" placeholder="不超过5个字" maxlength="5"></el-input>
            </el-form-item>
            <el-form-item label="收费配置" prop="is_toll">
              <el-switch
                v-model="drawerForm.is_toll"
                active-text="收费">
              </el-switch>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="drawerShow = false">关闭</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils';

export default {
  data() {
    return {
      isLoading: false,
      versionList: [],
      merchantFeatureList: [], // 整个权限
      merchantMobileFeatureList: [], // 整个权限
      userPhoneList: [], // 整个权限
      tsMerchantFeatureList: [], // 暂存用的，数组形式
      tsMerchantMobileFeatureList: [], // 暂存用的，数组形式
      tsUserPhoneList: [], // 暂存用的，数组形式
      merchantPermissionList: [], // 版本有的permission
      merchantPhonePermissionList: [], // 版本有的permission
      userPhonePermissionList: [], // 版本有的permission
      drawerShow: false,
      drawerType: '',
      selectType: 'Merchant',
      selectAll: false,
      selectNone: false,
      isSave: true,
      drawerForm: {
        name: '',
        is_toll: false
      },
      rules: {
        name: [{ required: true, message: '版本名称不能为空', trigger: ['blur', 'change'] }]
      },
      isSelectVersion: false,
      selectRow: {},
      isIndeterminate: false
    }
  },
  watch: {
    selectAll(newVal, oldVal) {
      if (newVal) {
        if (this.selectNone) {
          this.selectNone = false
        }
      }
    },
    selectNone(newVal, oldVal) {
      if (newVal) {
        if (this.selectAll) {
          this.selectAll = false
        }
      }
    },
    // 监听按钮点击了才请求
    selectType: {
      handler: function(newVal, oldVal) {
        if (this.isSave) {
          this.showSelect(newVal)
        } else {
          switch (oldVal) {
            case 'Merchant':
              this.tsMerchantFeatureList = this.getNewPermissionOut(this.merchantFeatureList)
              this.resetSelectList(this.merchantFeatureList, this.tsMerchantFeatureList)
              break
            case 'UserPhone':
              this.tsUserPhoneList = this.getNewPermissionOut(this.userPhoneList)
              this.resetSelectList(this.userPhoneList, this.tsUserPhoneList)
              break
            case 'MerchantPhone':
              this.tsMerchantMobileFeatureList = this.getNewPermissionOut(this.merchantMobileFeatureList)
              this.resetSelectList(this.merchantMobileFeatureList, this.tsMerchantMobileFeatureList)
              break
          }
        }
        this.selectAll = false
        this.selectNone = false
      },
      immediate: true
    }
  },
  computed: {
    computedSelectCount() {
      return d => {
        let count = 0
        switch (d) {
          case 'Merchant':
            this.merchantFeatureList.forEach(item => {
              count += item.tabSelectCount
            })
            break
          case 'UserPhone':
            this.userPhoneList.forEach(item => {
              count += item.tabSelectCount
            })
            break
          case 'MerchantPhone':
            this.merchantMobileFeatureList.forEach(item => {
              count += item.tabSelectCount
            })
            break
        }
        return count
      }
    },
    computedTotalCount() {
      return d => {
        let count = 0
        switch (d) {
          case 'Merchant':
            this.merchantFeatureList.forEach(item => {
              count += item.tabTotalCount
            })
            break
          case 'UserPhone':
            this.userPhoneList.forEach(item => {
              count += item.tabTotalCount
            })
            break
          case 'MerchantPhone':
            this.merchantMobileFeatureList.forEach(item => {
              count += item.tabTotalCount
            })
            break
        }
        return count
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getPermission()
      this.getVersionList()
    },
    // 以下是版本配置相关的
    // 请求权限
    getPermission() {
      this.getMerchantPermissions()
      this.getAppPermissions()
      this.getMerchantMobilePermissions()
    },
    // 获取权限
    getMerchantPermissions() {
      this.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost().then(res => {
        if (res.code === 0) {
          this.merchantFeatureList = res.data.map(item => {
            this.pushIsSelect(item)
            // 插入计数变量
            Object.assign(item, {
              tabSelectCount: 0,
              tabTotalCount: 0
            })
            return item
          })
          // 计数
          let arr = this.merchantFeatureList.map((item, index) => {
            this.setCount(this.merchantFeatureList, item, index)
            return item
          })
          this.merchantFeatureList = deepClone(arr)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getMerchantMobilePermissions() {
      this.$apis.apiBackgroundAdminOrganizationGetMerchantMobilePermissions().then(res => {
        if (res.code === 0) {
          this.merchantMobileFeatureList = res.data.map(item => {
            this.pushIsSelect(item)
            // 插入计数变量
            Object.assign(item, {
              tabSelectCount: 0,
              tabTotalCount: 0
            })
            return item
          })
          // 计数
          let arr = this.merchantMobileFeatureList.map((item, index) => {
            this.setCount(this.merchantMobileFeatureList, item, index)
            return item
          })
          this.merchantMobileFeatureList = deepClone(arr)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getAppPermissions() {
      this.$apis.apiBackgroundAdminOrganizationGetAppPermissionsPost().then(res => {
        if (res.code === 0) {
          this.userPhoneList = res.data[0].children.map(item => {
            this.pushIsSelect(item)
            // 插入计数变量
            Object.assign(item, {
              tabSelectCount: 0,
              tabTotalCount: 0
            })
            return item
          })
          // 计数
          let arr = this.userPhoneList.map((item, index) => {
            this.setCount(this.userPhoneList, item, index)
            return item
          })
          this.userPhoneList = deepClone(arr)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 循环往下查询，有children的话向里面的item插入isSelect
    pushIsSelect(data) {
      // debugger
      Object.assign(data, {
        isSelect: false,
        isIndeterminate: false
      })
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.pushIsSelect(item)
        })
      }
    },
    // 初始化设置计数
    setCount(arr, data, index) {
      if (Object.keys(data).includes('isSelect')) {
        arr[index].tabTotalCount++
      }
      if (data.isSelect) {
        arr[index].tabSelectCount++
      }
      // 判断该层是否有children
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.setCount(arr, item, index)
        })
      }
    },
    // 获取版本列表
    getVersionList() {
      this.$apis.apiBackgroundAdminBackgroundTollVersionListPost({
        page: 1,
        page_size: 9999
      }).then(res => {
        if (res.code === 0) {
          this.versionList = deepClone(res.data.results) || []
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 刷新页面
    refreshHandle() {
      this.getPermission()
      this.initLoad()
      this.isSelectVersion = false
    },
    // 元素换位
    swapElements(direction, index, row) {
      if (direction === 'up' && index > 0) {
        const temp = this.versionList[index - 1]
        let params1 = {
          id: row.id,
          order: temp.order
        }
        let params2 = {
          id: temp.id,
          order: row.order
        }
        this.modifyVersion(params1)
        this.modifyVersion(params2)
      } else if (direction === 'down' && index < this.versionList.length - 1) {
        const temp = this.versionList[index + 1]
        let params1 = {
          id: row.id,
          order: temp.order
        }
        let params2 = {
          id: temp.id,
          order: row.order
        }
        this.modifyVersion(params1)
        this.modifyVersion(params2)
      }
    },
    // 修改版本信息
    modifyVersion(params) {
      this.isLoading = true
      this.$apis.apiBackgroundAdminBackgroundTollVersionModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.drawerShow = false
          this.getPermission()
          this.getVersionList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 删除
    delItem(data) {
      if (data.org_count !== 0) {
        return this.$message.error('项目点使用中，不可删除')
      }
      this.isSelectVersion = false
      this.$apis.apiBackgroundAdminBackgroundTollVersionDeletePost({
        ids: [data.id]
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('删除成功')
          // this.versionList.forEach((item, index) => {
          //   let params = {
          //     id: item.id,
          //     order: item.id
          //   }
          //   this.modifyVersion(params)
          // })
          this.getVersionList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 新增版本
    addNewVersion(params) {
      this.$apis.apiBackgroundAdminBackgroundTollVersionAddPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
          this.getVersionList()
        } else {
          this.$message.error(res.msg)
        }
        this.drawerShow = false
      })
    },
    // 新增保存
    saveHandle() {
      this.$refs.drawerForm.validate((valid) => {
        if (valid) {
          let params = {
            id: this.drawerType === 'add' ? undefined : this.selectRow.id,
            name: this.drawerForm.name,
            permission: this.drawerType === 'add' ? [] : undefined,
            app_permission: this.drawerType === 'add' ? [] : undefined,
            merchant_app_permission: this.drawerType === 'add' ? [] : undefined,
            order: this.drawerType === 'add' ? this.versionList[this.versionList.length - 1].order + 1 : this.selectRow.order,
            is_toll: this.drawerForm.is_toll
          }
          if (this.drawerType === 'add') {
            this.addNewVersion(params)
          } else {
            this.modifyVersion(params)
          }
        }
      })
    },
    // 显示弹窗
    selectThisVersion(type, data) {
      this.drawerType = type
      if (type === 'edit') {
        this.drawerForm.name = data.name
        this.drawerForm.is_toll = data.is_toll
      } else {
        this.drawerForm.name = ''
        this.drawerForm.is_toll = false
      }
      this.drawerShow = true
    },

    // 以下是权限选择相关的
    /*
      2024/12/04
          这里的逻辑整理一下：点击版本获取版本返回的权限数据，这时一定要将三个平台的权限都做处理
      （在此之前的都是通过监听tab的切换才处理的，这就导致当你不切换tab的时候，另外两个平台的权限是空的）
    */
    // 点击获取版本已有的权限
    getVersionInfo(data) {
      this.isLoading = true
      this.isSave = true
      this.selectRow = deepClone(data)
      this.selectType = 'Merchant'
      // 将该版本的permission依次赋值
      this.merchantPermissionList = deepClone(data.permission)
      this.userPhonePermissionList = deepClone(data.app_permission)
      this.merchantPhonePermissionList = deepClone(data.merchant_app_permission)
      // 反显
      this.showSelect('Merchant')
      this.showSelect('UserPhone')
      this.showSelect('MerchantPhone')
      // 将暂存的也赋值？（要处理的，忘了考虑当他不切换tab的情况，以至于拿到的都是空的权限）
      this.tsMerchantFeatureList = this.getNewPermissionOut(this.merchantFeatureList)
      this.tsUserPhoneList = this.getNewPermissionOut(this.userPhoneList)
      this.tsMerchantMobileFeatureList = this.getNewPermissionOut(this.merchantMobileFeatureList)
      this.isSelectVersion = true
      setTimeout(() => {
        this.isLoading = false
      }, 500)
    },
    // 点击后反显
    showSelect(type) {
      switch (type) {
        case 'Merchant':
          this.resetSelectList(this.merchantFeatureList, this.merchantPermissionList)
          break
        case 'UserPhone':
          this.resetSelectList(this.userPhoneList, this.userPhonePermissionList)
          break
        case 'MerchantPhone':
          this.resetSelectList(this.merchantMobileFeatureList, this.merchantPhonePermissionList)
          break
      }
    },
    resetSelectList(showList, permissionList) {
      // 先循环将list里面的totalCount置空
      showList = showList.map(item => {
        item.tabSelectCount = 0
        return item
      })
      // 循环改变各级isSelect状态
      showList.forEach((item, index) => {
        this.isSelectKey(item, permissionList)
      })
      // 再循环计数
      showList.forEach((item, index) => {
        if (item.isSelect) {
          showList[index].tabSelectCount++
        }
        if (item.children && item.children.length) {
          this.resetTabSelectCount(item.children, index, showList)
        }
      })
    },
    // 遍历数组计数
    resetTabSelectCount(arr, index, fatherArr) {
      if (arr.length) {
        arr.forEach(item => {
          if (item.isSelect) {
            fatherArr[index].tabSelectCount++
          }
          if (item.children && item.children.length) {
            this.resetTabSelectCount(item.children, index, fatherArr)
          }
        })
      }
    },
    // 循环勾选isSelect
    isSelectKey(data, permissionArr) {
      if (permissionArr.includes(data.key)) {
        data.isSelect = true
      } else {
        data.isSelect = false
      }
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.isSelectKey(item, permissionArr)
        })
      }
    },

    // 点击选中的操作
    selectHandle(status, data, type, index, selectAllOrNot) {
      // debugger
      if (status) {
        this.selectNone = false
      } else {
        this.selectAll = false
      }
      switch (type) {
        case 'Merchant':
          this.changeSelectStatus(status, data, this.merchantFeatureList, index, selectAllOrNot)
          this.tsMerchantFeatureList = this.getNewPermissionOut(this.merchantFeatureList)
          break
        case 'UserPhone':
          this.changeSelectStatus(status, data, this.userPhoneList, index, selectAllOrNot)
          this.tsUserPhoneList = this.getNewPermissionOut(this.userPhoneList)
          break
        case 'MerchantPhone':
          this.changeSelectStatus(status, data, this.merchantMobileFeatureList, index, selectAllOrNot)
          this.tsMerchantMobileFeatureList = this.getNewPermissionOut(this.merchantMobileFeatureList)
          break
      }
    },
    changeSelectStatus(status, data, arr, index, selectAllOrNot) {
      // 对其所有下级进行操作
      this.selectAllItem(status, data)
      // 改变自身的状态
      this.changeSelfIndeterminate(status, data)
      // 父级选择的联动
      if (!selectAllOrNot) {
        arr.forEach(item => {
          this.changeParentIndeterminate(status, item, data, arr)
        })
      }
      this.tabSelectCountHandle(arr, index)
    },
    getNewPermissionOut(permissionTree) {
      let tsArr = []
      permissionTree.forEach(item => {
        if (item.isSelect) {
          tsArr.push(item.key)
        }
        if (item.children && item.children.length) {
          this.getNewPermissionIn(item.children, tsArr)
        }
      })
      return deepClone(tsArr)
    },
    // 获取点击后的permission
    getNewPermissionIn(arr, tsArr) {
      arr.forEach(item => {
        if (item.isSelect) {
          tsArr.push(item.key)
        }
        if (item.children && item.children.length) {
          this.getNewPermissionIn(item.children, tsArr)
        }
      })
    },
    // 重新计数
    tabSelectCountHandle(arr, index) {
      // 处理计数的情况
      arr[index].tabSelectCount = 0
      if (arr[index].isSelect) {
        arr[index].tabSelectCount++
      }
      if (arr[index].children && arr[index].children.length) {
        // 遍历找isSelect是true的情况
        this.resetTabSelectCount(arr[index].children, index, arr)
      }
    },
    // 改变自己的选中状态
    changeSelfIndeterminate(status, data) {
      // 点击前改变当前节点的状态
      if (status) {
        // 看底下children情况
        if (!data.children.some(item => item.isSelect)) {
          data.isIndeterminate = false
        } else if (data.children.some(item => !item.isSelect)) {
          data.isIndeterminate = true
        } else {
          data.isIndeterminate = false
        }
      } else {
        if (data.children.some(item => item.isSelect)) {
          data.isIndeterminate = true
        } else if (!data.children.some(item => item.isSelect)) {
          data.isIndeterminate = false
        } else {
          data.isIndeterminate = false
        }
      }
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.changeSelfIndeterminate(status, item)
        })
      }
    },
    // 改变父级的选中状态
    changeParentIndeterminate(status, father, data, permissionTree) {
      // debugger
      // 先判断item的index是不是data的parent
      if (father.index === data.parent) {
        // 将key丢进权限里
        if (status) {
          // 判断此父级的勾选状态
          if (!father.children.some(item => item.isSelect)) {
            father.isIndeterminate = false
            father.isSelect = false
          } else if (father.children.some(item => !item.isSelect)) {
            father.isIndeterminate = true
            father.isSelect = true
          } else {
            father.isIndeterminate = false
            father.isSelect = true
          }
        } else {
          // 判断此父级的勾选状态
          if (father.children.some(item => item.isSelect)) {
            father.isIndeterminate = true
            father.isSelect = true
          } else if (!father.children.some(item => item.isSelect)) {
            father.isIndeterminate = false
            father.isSelect = false
          } else {
            father.isIndeterminate = false
            father.isSelect = true
          }
        }
        // 更往上的状态也要变动
        if (father.level !== 0) {
          permissionTree.forEach(item => {
            this.changeParentIndeterminate(status, item, father, permissionTree)
          })
        }
      } else {
        if (father.children && father.children.length) {
          father.children.forEach(item => {
            this.changeParentIndeterminate(status, item, data, permissionTree)
          })
        }
      }
    },
    // 选中下面的全部权限
    selectAllItem(status, data) {
      // 先将自己的key存入对应的已勾选的权限list里
      data.isSelect = status
      if (data.children && data.children.length) {
        data.children.forEach((item) => {
          item.isSelect = status
          this.selectAllItem(status, item)
        })
      }
    },
    // 是否全选全部功能
    isSelectAll(type, btn, status) {
      if ((btn === 'selectAll' && status) || (btn === 'selectNone' && !status)) {
        switch (type) {
          case 'Merchant':
            this.merchantFeatureList.forEach((item, index) => {
              item.isSelect = status
              this.selectAllItem(status, item)
              this.changeSelfIndeterminate(status, item)
              this.tabSelectCountHandle(this.merchantFeatureList, index)
            })
            this.tsMerchantFeatureList = this.getNewPermissionOut(this.merchantFeatureList)
            break
          case 'UserPhone':
            this.userPhoneList.forEach((item, index) => {
              item.isSelect = status
              this.selectAllItem(status, item)
              this.changeSelfIndeterminate(status, item)
              this.tabSelectCountHandle(this.userPhoneList, index)
            })
            this.tsUserPhoneList = this.getNewPermissionOut(this.userPhoneList)
            break
          case 'MerchantPhone':
            this.merchantMobileFeatureList.forEach((item, index) => {
              item.isSelect = status
              this.selectAllItem(status, item)
              this.changeSelfIndeterminate(status, item)
              this.tabSelectCountHandle(this.merchantMobileFeatureList, index)
            })
            this.tsMerchantMobileFeatureList = this.getNewPermissionOut(this.merchantMobileFeatureList)
            break
        }
      }
    },

    // 取消或编辑
    editOrCancelHandle(status) {
      if (!status) {
        this.merchantPermissionList = deepClone(this.selectRow.permission)
        this.userPhonePermissionList = deepClone(this.selectRow.app_permission)
        this.merchantPhonePermissionList = deepClone(this.selectRow.merchant_app_permission)
        this.showSelect()
      }
      this.isSave = !status
    },
    // 保存配置
    save() {
      // debugger
      this.isLoading = true
      this.merchantPermissionList = deepClone(this.traverseGroups(this.merchantFeatureList))
      this.userPhonePermissionList = deepClone(this.traverseGroups(this.userPhoneList))
      this.merchantPhonePermissionList = deepClone(this.traverseGroups(this.merchantMobileFeatureList))
      let params = {
        id: this.selectRow.id,
        permission: this.merchantPermissionList,
        app_permission: this.userPhonePermissionList,
        merchant_app_permission: this.merchantPhonePermissionList
      }
      this.modifyVersion(params)
      this.isSave = true
      this.isSelectVersion = false
      this.selectAll = false
      this.selectNone = false
    },
    traverseGroups(permissionTree) {
      let arr = []
      this.traverseGroupsDetail(permissionTree, arr)
      return arr
    },
    // 遍历权限数组重新赋值
    traverseGroupsDetail(permissionTree, permissionArr) {
      permissionTree.forEach(item => {
        if (item.isSelect) {
          permissionArr.push(item.key)
        }
        if (item.children && item.children.length) {
          this.traverseGroupsDetail(item.children, permissionArr)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.version-configuration {
  &-sidebar {
    padding: 0px 20px 0px;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    &-btn {
      position: sticky;
      top: 0px;
      background-color: #fff;
    }
    &-content {
      display: flex;
      flex-direction: column;
      // gap: 20px;
      &::after {
        content: '';
        background-color: #fff;
        // width: 336px;
        padding: 10px;
        position: sticky;
        bottom: 0px;
      }
    }
    &-item {
      margin: 10px 0px;
      cursor: pointer;
      background-color: #f8f9fa;
      padding: 10px 20px;
      border-radius: 4px;
      .button-area {
        width: 32px;
        height: 32px;
      }
      .enable {
        &-up {
          color: #FF9B45;
        }
        &-down {
          color: #B8741A;
        }
      }
      .disable {
        color: #D7D7D7;
      }
    }
  }
  &-content {
    &-box {
      height: calc(86vh - 220px);
    }
    &-box2 {
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    .box-item {
      background-color: #ffffff;
      border-radius: 4px;
    }
    &-footer {
      display: flex;
      align-items: center;
      .button-area {
        display: flex;
        align-items: center;
      }
      .checkbox-area {
        display: flex;
        align-items: center;
      }
    }
  }
  .bg-style {
    height: calc(86vh - 75px);
    background-color: #fff;
    border-radius: 8px;
  }
}
::v-deep .el-tabs--card>.el-tabs__header {
  border-bottom: none;
  & .el-tabs__nav {
    border: none;
  }
}
::v-deep .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  border: none;
  &.is-active {
    background-color: #f8f9fa;
  }
}
::v-deep .el-tabs--left .el-tabs__header.is-left {
  margin-right: 0px;
}
::v-deep .el-tabs__content {
  background-color: #f8f9fa;
  overflow: auto;
  height: 100%;
}
::v-deep .el-checkbox {
  display: flex;
  align-items: center;
  &__label {
    display: flex;
    align-items: center;
  }
}
</style>
