<template>
  <div class="super-add-article container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      size="small"
      label-width="80px"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="padding: 0 20px">
          <el-form-item label="标题" prop="title">
            <el-input
              class="ps-input w-250"
              v-model="formData.title"
              type="textarea"
              :rows="4"
              maxlength="40"
              show-word-limit
            ></el-input>
            <!-- <el-input
              v-model="formData.title"
              placeholder="请输入标题"
              class="ps-input p-r-48"
              style="width: 240px"
              show-word-limit
            ></el-input> -->
          </el-form-item>
          <el-form-item label="来源" prop="source">
            <el-select
              v-model="formData.source"
              placeholder="请下拉选择"
              class="ps-select w-250"
              popper-class="ps-popper-select"
            >
              <el-option label="原创" :value="1"></el-option>
              <el-option label="转载" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否推荐" prop="is_recommend">
            <el-select
              v-model="formData.is_recommend"
              placeholder="请下拉选择"
              class="ps-select w-250"
              popper-class="ps-popper-select"
            >
              <el-option label="是" :value="true"></el-option>
              <el-option label="否" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="作者" prop="author">
            <el-input
              v-model="formData.author"
              placeholder="请输入标题"
              class="ps-input p-r-50 w-300"
              maxlength="10"
              show-word-limit
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="标签" prop="tags">
            <el-select
              v-model="formData.tags"
              placeholder="请下拉选择"
              class="ps-select"
              multiple
              popper-class="ps-popper-select"
            >
              <el-option
                v-for="(item, index) in tagsList"
                :key="index"
                :label="item.name"
                :value="item.id"
                :disabled="item.status == 'disable' ? true : false"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="分类" prop="categorys">
            <el-select
              v-model="formData.categorys"
              placeholder="请下拉选择"
              class="ps-select w-250"
              multiple
              popper-class="ps-popper-select"
            >
              <el-option
                v-for="(item, index) in categorysList"
                :key="index"
                :label="item.name"
                :value="item.id"
                :disabled="item.status == 'disable' ? true : false"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="封面" prop="image">
            <el-upload
              class="avatar-uploader"
              :data="uploadParams"
              ref="uploadFood"
              :action="actionUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="formData.image" :src="formData.image" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="正文" prop="content">
            <TinymceUeditor
              :content="formData.content"
              v-model="formData.content"
              @message="messageTinymceUeditor"
            ></TinymceUeditor>
          </el-form-item>
        </div>
      </div>

      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'

export default {
  name: 'SuperAddEditArticle',
  components: {
    TinymceUeditor
  },
  // mixins: [activatedLoadData, exportExcel],
  data() {
    let validataTitle = (rule, value, callback) => {
      if (value) {
        if (value.length > 40) {
          callback(new Error('标题不能超过40个字'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入文章标题'))
      }
    }
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        title: '',
        tags: [],
        categorys: [],
        source: '',
        is_recommend: null,
        author: '',
        image: '',
        content: ''
      },
      formRuls: {
        title: [
          {
            required: true,
            validator: validataTitle,
            trigger: 'blur'
          }
        ],
        categorys: [
          {
            required: true,
            message: '请选择分类',
            trigger: 'blur'
          }
        ],
        source: [
          {
            required: true,
            message: '请选择来源',
            trigger: 'blur'
          }
        ],
        is_recommend: [
          {
            required: true,
            message: '请选择是否推荐',
            trigger: 'blur'
          }
        ],
        content: [
          {
            required: true,
            message: '请输入文章内容',
            trigger: 'blur'
          }
        ]
      },
      tagsList: [],
      categorysList: [],
      actionUrl: '',
      uploadParams: {}
    }
  },
  created() {
    this.getUploadToken()
    // this.getArticleChildTagList()
    this.getArticleCategoryList()
    this.type = this.$route.query.type
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        this.formData = {
          id: data.id,
          title: data.title,
          // tags: data.tags.map(v => v.id),
          categorys: data.categorys.map(v => v.id),
          source: data.source,
          is_recommend: data.is_recommend,
          author: data.author,
          image: data.image,
          content: data.content ? data.content : ''
        }
        console.log(data)
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: 'jpeg/png'
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // async getArticleChildTagList() {
    //   const [err, res] = await to(
    //     this.$apis.apiBackgroundArticleTagChildListPost({
    //       page: 1,
    //       page_size: 10000
    //     })
    //   )
    //   if (err) {
    //     this.$message.error(err.message)
    //     return
    //   }
    //   if (res.code === 0) {
    //     // 一级分类
    //     this.tagsList = res.data.results
    //     this.initLoad()
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    async getArticleCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminArticleCategoryListPost({
          page: 1,
          page_size: 10000
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 一级分类
        this.categorysList = res.data.results
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },
    handleAvatarSuccess(res, file) {
      if (res.code === 0) {
        this.$refs.uploadFood.clearFiles()
        this.formData.image = res.data.public_url
        this.getUploadToken()
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    messageTinymceUeditor(content) {
      this.formData.content = content
    },
    // 添加
    async addModifyArticle(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'add') {
        ;[err, res] = await to(this.$apis.apiBackgroundAdminArticleAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundAdminArticleModifyPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.addModifyArticle(this.formData)
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss">
.super-add-article {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
.el-input__count .el-input__count-inner {
  background-color: transparent !important;
}
</style>
