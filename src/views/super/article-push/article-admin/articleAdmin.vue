<template>
  <div class="ArticleAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="plain" @click="gotoArticleCategory()">文章分类</button-icon> -->
          <button-icon color="origin" type="add" @click="gotoArticle('add')">添加文章</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'has_children' }"
        >
          <el-table-column prop="title" label="文章标题" align="center"></el-table-column>
          <el-table-column prop="image" label="封面" align="center">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.image ? scope.row.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/5067c24dac9548be9ebe8f29d88d3dad1688112556266.jpg'"
                :preview-src-list="[scope.row.image]"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="source_alias" label="来源" align="center"></el-table-column>
          <!-- <el-table-column
            prop="person_no"
            label="标签"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div class="label-article" v-for="(item, index) in scope.row.tags" :key="index">
                {{ item.name }}、
              </div>
            </template>
          </el-table-column> -->
          <el-table-column prop="is_recommend" label="是否推荐" align="center">
            <template slot-scope="scope">
              <div>
                {{ scope.row.is_recommend ? '是' : '否' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="categorys_all"
            label="分类"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column prop="author" label="作者" align="center"></el-table-column>
          <el-table-column prop="read_number" label="阅读量" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="release_time" label="发布时间" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="gotoArticle('modify', scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="!scope.row.is_release"
                @click="clickIsRelease(true, scope.row)"
              >
                发布
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="scope.row.is_release"
                @click="clickIsRelease(false, scope.row)"
              >
                取消发布
              </el-button>
              <span style="margin: 0 10px; color: #e2e8f0">|</span>
              <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteHandler('single', scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import { RECENTSEVEN } from '@/utils/constants'

export default {
  name: 'ArticleAdmin',
  components: {},
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '发布时间',
              value: 'release_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: []
        },
        title: {
          type: 'input',
          label: '标题',
          value: '',
          placeholder: '请输入标题'
        },
        source: {
          type: 'select',
          value: '',
          label: '来源',
          placeholder: '请选择',
          listNameKey: 'name',
          listValueKey: 'id',
          multiple: false,
          collapseTags: true,
          dataList: [
            {
              name: '原创',
              id: '1'
            },
            {
              name: '转载',
              id: '2'
            }
          ]
        },
        is_recommend: {
          type: 'select',
          value: '',
          label: '是否推荐',
          placeholder: '请选择',
          listNameKey: 'name',
          listValueKey: 'id',
          multiple: false,
          collapseTags: true,
          dataList: [
            {
              name: '是',
              id: true
            },
            {
              name: '否',
              id: false
            }
          ]
        },
        categorys: {
          type: 'select',
          value: [],
          label: '分类',
          placeholder: '请选择',
          listNameKey: 'name',
          listValueKey: 'id',
          multiple: true,
          collapseTags: true,
          dataList: []
        },
        operator: {
          type: 'input',
          label: '操作人',
          value: '',
          placeholder: '请输入操作人'
        }
      }
    }
  },
  created() {
    this.getArticleChildTagList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getArticleList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1;
      // this.tableData = []
      this.initLoad()
    },
    async getArticleChildTagList() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminArticleCategoryListPost({
        page: 1,
        page_size: 9999
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.categorys.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 列表
    async getArticleList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminArticleListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          let list = []
          item.categorys.map(cItem => {
            list.push(cItem.name)
          })
          item.categorys_all = list.join('、')
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 是否发布
    async getArticleChangeRelease(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminArticleChangeReleasePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getArticleList()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickIsRelease(isRelease, row) {
      let params = {
        is_release: isRelease,
        id: row.id
      }
      this.getArticleChangeRelease(params)
    },
    gotoArticle(type, row) {
      this.$router.push({
        name: 'SuperAddEditArticle',
        query: {
          type: type,
          data: this.$encodeQuery(row)
        }
      })
    },
    gotoArticleCategory() {
      this.$router.push({
        name: 'SuperArticleCategory'
      })
    },
    deleteHandler(type, id) {
      // let ids = []
      // if (type === 'single') {
      //   ids = [id]
      // } else {
      //   if (!this.checkList.length) return this.$message.error('请先选择要删除的数据！')
      //   ids = this.checkList
      // }
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminArticleDeletePost({
                ids: [id]
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getArticleList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getArticleList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.ArticleAdmin {
  .label-article {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.el-tooltip__popper {
  max-width: 15%;
}
</style>
