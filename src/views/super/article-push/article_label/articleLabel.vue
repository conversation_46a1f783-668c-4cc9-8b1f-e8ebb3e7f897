<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="article-label" v-loading="isLoading">
      <div class="organization-tree">
        <el-input
          v-model="primaryName"
          placeholder="请输入标签分类"
          class="tree-search ps-input"
          @input="changeArticleTag"
        ></el-input>
        <div>
          <!-- <button-icon color="origin" type="" @click="openImport">批量导入分类</button-icon> -->
          <button-icon color="origin" type="" @click="clickShowDialogLabel('addCategory')">
            新建标签分类
          </button-icon>
          <ul class="infinite-list" :style="{ overflow: 'auto', height: `${labelBoxHeight}px` }">
            <li v-for="(item, index) in foodFoodCategoryPrimaryList" :key="index">
              <div class="primary-label">
                <span
                  :class="{ active: index === articleTagIndex }"
                  style="cursor: pointer"
                  @click="clickArticleTag(item, index)"
                >
                  {{ item.name }}
                </span>
                <div>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="clickShowDialogLabel('editCategory', item)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-red"
                    @click="deleteHaldler('delCategory', item)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="label-list">
        <search-form
          ref="searchRef"
          :form-setting="searchFormSetting"
          @search="searchHandle"
        ></search-form>
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <button-icon
                color="origin"
                type="add"
                @click="clickShowDialogLabel('addCategoryLabel')"
              >
                新建标签
              </button-icon>
            </div>
          </div>
          <div class="table-content">
            <!-- table start -->
            <el-table
              :data="tableData"
              ref="tableData"
              style="width: 100%"
              row-key="id"
              stripe
              header-row-class-name="ps-table-header-row"
              class="ps-table"
            >
              <el-table-column prop="name" label="名称" align="center"></el-table-column>
              <el-table-column
                prop="article_number"
                label="文章数"
                align="center"
              ></el-table-column>
              <el-table-column fixed="right" label="操作" width="180" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="clickShowDialogLabel('editCategoryLabel', scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    v-if="scope.row.status === 'disable'"
                    @click="clickStatus(scope.row, 'enable')"
                  >
                    启用
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    v-if="scope.row.status === 'enable'"
                    @click="clickStatus(scope.row, 'disable')"
                  >
                    停用
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-red"
                    @click="deleteHaldler('delCategoryLabel', scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- table end -->
          </div>
          <!-- 分页 start -->
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="pageSize"
              layout="total, prev, pager, next,sizes,jumper"
              :total="totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 一级分类 -->
    <el-dialog
      :title="dialogLabelTitle"
      :visible.sync="showDialogCategory"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        :model="dialogCategoryForm"
        @submit.native.prevent
        status-icon
        ref="dialogCategoryForm"
        :rules="dialogCategoryRules"
        v-loading="formCategoryLoading"
        label-width="80px"
      >
        <el-form-item label="分类" prop="primaryCategoryName">
          <el-input
            class="ps-input"
            style="width: 190px"
            placeholder="请输入分类名称"
            v-model="dialogCategoryForm.primaryCategoryName"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDialogCategory = false">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :loading="formCategoryLoading"
          @click="determineCategoryDialog"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
    <!-- 二级分类 -->
    <el-dialog
      :title="dialogLabelTitle"
      :visible.sync="showDialogCategoryLabel"
      width="600px"
      custom-class="ps-dialog"
    >
      <el-form
        :model="dialogCategoryLabelForm"
        @submit.native.prevent
        status-icon
        ref="dialogCategoryLabelForm"
        :rules="dialogCategoryLabelRules"
        v-loading="formCategoryLabelLoading"
        label-width="100px"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input
            v-model="dialogCategoryLabelForm.name"
            class="ps-input"
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签关键词">
          <div class="p-l-10" style="color:red;">关键词之间用、隔开</div>
          <el-input
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 6 }"
            v-model="dialogCategoryLabelForm.keyWords"
            class="ps-input"
            style="width: 250px"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDialogCategoryLabel = false">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :loading="formCategoryLoading"
          @click="determineCategoryLabelDialog"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
export default {
  name: 'articleLabel',
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      articleTagIndex: -1,
      labelBoxHeight: '',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '筛选',
          value: '',
          placeholder: '请输入标签名称'
        },
        status: {
          type: 'select',
          value: '',
          label: '标签状态',
          placeholder: '请选择',
          listNameKey: 'name',
          listValueKey: 'id',
          multiple: false,
          collapseTags: true,
          dataList: [
            {
              name: '启用',
              id: 'enable'
            },
            {
              name: '停用',
              id: 'disable'
            }
          ]
        }
      },
      primaryName: '',
      dialogLabelTitle: '',
      showDialogLabelType: '',
      showDialogCategoryLabel: false,
      showDialogCategory: false,
      dialogCategoryLabelForm: {
        name: '',
        keyWords: ''
      },
      dialogCategoryLabelRules: {
        name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }]
      },
      dialogCategoryForm: {
        primaryCategoryName: ''
      },
      dialogCategoryRules: {
        primaryCategoryName: [{ required: true, message: '请输入一级分类名称', trigger: 'blur' }]
      },
      formCategoryLabelLoading: false,
      formCategoryLoading: false,
      foodFoodCategoryPrimaryList: [],
      showDialogLabelRow: {},
      articleTagRow: {}
    }
  },
  created() {
    this.getArticleTagList()
  },
  watch: {
    tableData: function () {
      this.$nextTick(function () {
        this.labelBoxHeight =
          document.getElementsByClassName('search-form-wrapper')[0].offsetHeight +
          document.getElementsByClassName('table-wrapper')[0].offsetHeight -
          140
      })
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getArticleTagChildList({
        parent_id: this.articleTagRow.id // 一级标签id
      })
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 因为搜索一级分类名字需要节流
    searchHandleArticleTag: debounce(function () {
      let params = {}
      if (this.primaryName) {
        params.name = this.primaryName
      } else {
        params = {}
      }
      this.getArticleTagList(params)
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      // this.tableData = []
      this.initLoad()
    },
    // 文章标签列表
    // 如果没有传有参数 就是一级标签分类 传有参数就是二级标签
    async getArticleTagList(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundArticleTagListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 一级分类
        this.tableData = []
        this.foodFoodCategoryPrimaryList = res.data.results
        this.showDialogCategory = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 二级标签列表
    async getArticleTagChildList(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundArticleTagChildListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          ...params,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogCategoryLabel = false
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增
    async getAddArticleTag(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundArticleTagAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (this.showDialogLabelType === 'addCategoryLabel') {
          this.getArticleTagChildList({
            parent_id: this.articleTagRow.id // 一级标签id
          })
        } else {
          this.getArticleTagList()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 就是一级标签分类 传有参数就是二级标签文章标签列表修改
    async getModifyArticleTag(params, type) {
      // articleChild 二级标签判断
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundArticleTagModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (type === 'articleChild') {
          this.getArticleTagChildList({
            parent_id: this.articleTagRow.id // 一级标签id
          })
        } else {
          this.getArticleTagList()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 就是一级标签分类 传有参数就是二级标签文章标签列表删除
    async getDeleteArticleTag(type, params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundArticleTagDeletePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (type === 'delCategoryLabel') {
          this.getArticleTagChildList({
            parent_id: this.articleTagRow.id // 一级标签id
          })
        } else {
          this.getArticleTagList()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    changeArticleTag() {
      this.searchHandleArticleTag()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getArticleTagChildList({
        parent_id: this.articleTagRow.id // 一级标签id
      })
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getArticleTagChildList({
        parent_id: this.articleTagRow.id // 一级标签id
      })
    },
    // 点击一级标签
    clickArticleTag(row, index) {
      // 用于点击某条数据 颜色
      this.articleTagIndex = index
      this.articleTagRow = row // 一级标签内容 选中
      this.getArticleTagChildList({ parent_id: row.id })
    },
    clickStatus(row, type) {
      this.getModifyArticleTag({ id: row.id, status: type }, 'articleChild')
    },
    deleteHaldler(type, row) {
      this.$confirm(`是否删除该分类？`, `删除`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            let params = {
              ids: [row.id]
            }
            instance.confirmButtonLoading = true
            this.getDeleteArticleTag(type, params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    clickShowDialogLabel(type, row) {
      this.showDialogLabelRow = {}
      this.showDialogLabelType = type
      if (type === 'addCategory') {
        this.dialogLabelTitle = '新增分类'
        this.dialogCategoryForm.primaryCategoryName = ''
        this.showDialogCategory = true
      } else if (type === 'editCategory') {
        this.dialogLabelTitle = '编辑分类'
        this.dialogCategoryForm.primaryCategoryName = row.name
        this.showDialogLabelRow = row
        this.showDialogCategory = true
      } else if (type === 'addCategoryLabel') {
        if (!this.articleTagRow.id) return this.$message.error('请先选择标签分类')
        this.dialogLabelTitle = '新增标签'
        this.showDialogCategoryLabel = true
        this.dialogCategoryLabelForm = {
          name: '',
          keyWords: ''
        }
      } else if (type === 'editCategoryLabel') {
        // if (!this.articleTagRow.id) return this.$message.error('请先选择标签分类')
        this.dialogLabelTitle = '编辑标签'
        this.showDialogCategoryLabel = true
        this.dialogCategoryLabelForm = {
          name: row.name,
          keyWords: row.key_words.join('、')
        }
        this.showDialogLabelRow = row
      }
    },
    determineCategoryDialog() {
      this.$refs.dialogCategoryForm.validate(valid => {
        if (valid) {
          let params = {
            name: this.dialogCategoryForm.primaryCategoryName
          }
          if (this.showDialogLabelType === 'addCategory') {
            this.getAddArticleTag(params)
          } else if (this.showDialogLabelType === 'editCategory') {
            this.getModifyArticleTag({ id: this.showDialogLabelRow.id, ...params })
          }
        } else {
          return false
        }
      })
    },
    determineCategoryLabelDialog() {
      this.$refs.dialogCategoryLabelForm.validate(valid => {
        if (valid) {
          let params = {
            name: this.dialogCategoryLabelForm.name,
            key_words: this.dialogCategoryLabelForm.keyWords.split('、') // 用 、隔开
          }
          if (this.showDialogLabelType === 'addCategoryLabel') {
            this.getAddArticleTag({
              parent_id: this.articleTagRow.id, // 一级标签id
              ...params
            })
          } else if (this.showDialogLabelType === 'editCategoryLabel') {
            this.getModifyArticleTag({ id: this.showDialogLabelRow.id, ...params }, 'articleChild')
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
#article-label {
  display: flex;
  .label-list {
    flex: 1;
    min-width: 0;
    // background-color: ;
  }
  .active {
    color: #ff994d;
  }
}
.infinite-list {
  .primary-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
