<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="super-commodity-library container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    >
      <!-- <template slot="perv">
        <div class="tab">
          <div
            :class="['tab-item', tabType === 'system' ? 'active' : '']"
            @click="tabClick('system')"
          >
            系统菜品/商品
          </div>
          <div
            :class="['tab-item', tabType === 'merchant' ? 'active' : '']"
            @click="tabClick('merchant')"
          >
            商户菜品/商品
          </div>
        </div>
      </template>-->
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表
          <el-checkbox v-model="isAllSelect" @change="handleAllSelectChange" class="m-l-10">全选</el-checkbox>
        </div>
        <div class="align-r">
          <!-- <button-icon v-if="tabType === 'system'" color="plain" type="refresh" @click="distributeHandler">更新到商户</button-icon> -->
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            @click="handlerMultiModifyType"
            :disabled="!checkList.length"
          >
            批量修改分类
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            :disabled="!checkList.length"
            @click="batchLabelClick('batchLabelDel')"
          >
            批量移除标签
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            :disabled="!checkList.length"
            @click="batchLabelClick('batchLabelAdd')"
          >
            批量打标签
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="origin"
            @click="addFoodHandler"
          >
            新增
          </button-icon>
          <button-icon color="plain" @click="gotoExport">导出菜品</button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            @click="importHandler('import')"
          >
            导入菜品
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            @click="importHandler('modify_import')"
          >
            导入编辑
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            @click="importImgHandler"
          >
            导入图片
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            @click="gotoCategory"
          >
            分类管理
          </button-icon>
          <!-- <button-icon
            v-if="tabType === 'system'"
            color="plain"
            type="del"
            @click="deleteHandler('multi')"
          >
            批量删除
          </button-icon>
          <span v-if="tabType === 'merchant'" style="font-size: 12px;">
            是否允许商户上传信息
            <el-switch
              v-model="updateSetting"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
              @change="updateSettingHandler"
            ></el-switch>
          </span> -->
        </div>
      </div>
      <div class="table-content" :key="tabType">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          row-key="id"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          @select="selectSelection"
          @select-all="selectSelectionAll"
        >
          <el-table-column
            v-if="tabType === 'system'"
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <!-- <el-table-column type="index" label="序号" align="center"></el-table-column> -->
          <!-- <el-table-column
            prop="id"
            label="菜品/商品ID"
            align="center"
            width="80"
          ></el-table-column> -->
          <el-table-column prop="" label="菜品图片" align="center" width="200px">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.image ? scope.row.image : 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"
                :preview-src-list="[scope.row.image?scope.row.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png']"
                fit="cover">
                </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="菜品/商品名称" align="center"></el-table-column>
          <el-table-column prop="all_alias_name" label="别名" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="attributes" label="属性" align="center">
            <template slot-scope="scope">
              {{ scope.row.attributes === 'goods' ? '商品' : '菜品' }}
            </template>
          </el-table-column>
          <el-table-column prop="sort_name" label="一级分类" align="center"></el-table-column>
          <el-table-column prop="category_name" label="二级分类" align="center"></el-table-column>
          <el-table-column prop="category" label="关联食材" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showDialogHandler('ingredientsList', scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="category"
            v-if="tabType === 'merchant'"
            label="已有菜品/商品"
            align="center"
          >
            <template slot-scope="scope">{{ scope.row.is_repeat ? '是' : '否' }}</template>
          </el-table-column>
          <!-- <el-table-column prop="xx" label="营养信息" align="center">
             <template slot-scope="scope">
               <el-button
                 type="text"
                 size="small"
                 class="ps-text"
                 @click="showDialogHandler('nutrition', scope.row)"
               >
                 查看
               </el-button>
             </template>
           </el-table-column> -->
          <el-table-column
            prop="xx"
            label="标签"
            align="center"
            width="220px"
            v-if="tabType === 'system'"
          >
            <template slot-scope="scope">
              <div class="collapse-wrapper">
                <div class="collapse-list hide">
                  <el-tag
                    class="m-r-5 m-t-5 collapse-data"
                    v-for="(item, index) in scope.row.labelList"
                    :key="index"
                    size="medium"
                    effect="plain"
                    :type="item.type === 'ingredients' ? 'light' : 'danger'"
                    :closable="item.type !== 'ingredients'"
                    @close="closeTag(item, scope.row)"
                  >
                    {{ item.name }}
                  </el-tag>
                  <template v-if="scope.row.labelList && scope.row.labelList.length > 3">
                    <span class="collapse-more" @click="showMoreHandler">
                      查看更多
                      <i class="el-icon-arrow-down"></i>
                    </span>
                    <span class="collapse-hide" @click="hideMoreHandler">
                      收起
                      <i class="el-icon-arrow-up"></i>
                    </span>
                  </template>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="is" label="口味" align="center">
            <template slot-scope="scope">
              <div class="tast-wrapper">
                <el-tag
                  class="m-r-5 m-t-5"
                  v-for="(item, index) in scope.row.taste_list"
                  :key="index"
                >
                  {{ item.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column
            prop="update_time"
            v-if="tabType === 'system'"
            label="修改时间"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="tabType === 'system'"
            prop="operator_name"
            label="操作人"
            align="center"
          ></el-table-column>
          <el-table-column  label="操作" width="180" align="center" fixed="right">
            <template slot-scope="scope">
              <template v-if="tabType === 'merchant'">
                <el-button type="text" size="small" @click="addToSystem(scope.row)">
                  创建到菜品/商品库
                </el-button>
              </template>
              <template v-else>
                <el-button type="text" size="small" @click="showIngredientsDetail(scope.row)">
                  详情
                </el-button>
                <span style="margin: 0 10px; color: #e2e8f0">|</span>
                <el-button type="text" size="small" class="" @click="modifyFoodHandler(scope.row)">
                  编辑
                </el-button>
                <span style="margin:0 10px; color: #e2e8f0;">|</span>
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="deleteHandler('single', scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100, 500]"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 start -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      :top="dialogTop"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @closed="dialogHandleClose"
    >
      <el-form v-loading="isLoading" :model="formData" class="" size="small">
        <!-- 营养 start -->
        <div v-if="dialogType === 'nutrition'">
          <template v-for="nutrition in nutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <el-form-item :prop="nutrition.key">
                <el-input
                  style="width: 120px;"
                  readonly
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                ></el-input>
                <span style="margin-left: 10px;">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
        </div>
        <!-- 营养 end -->
        <!-- 食材 start-->
        <el-table
          v-if="dialogType === 'ingredientsList'"
          :data="tableDataIngredients"
          style="width: 100%"
        >
          <el-table-column prop="ingredient_name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="ingredient_scale" label="占比" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.ingredient_scale }}%</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 食材 end -->
      </el-form>
      <!-- <span slot="footer" class="dialog-footer">
          <el-button size="small" class="ps-cancel-btn"  @click="dialogVisible = false">
            {{ $t('dialog.cancel_btn') }}
          </el-button>
          <el-button
            class="ps-origin-btn"
            type="primary"
            size="small"
          >
          </el-button>
        </span> -->
    </el-dialog>
    <!-- 弹窗 end -->
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      :title="titleSelectLaber"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
    />
     <!-- 批量修改分类 -->
     <multi-modify-type-dialog
      :visible="multiModifyTypeDialogVisible"
      :categoryList="options"
      :selectedList="checkList"
      :type="multyModifyType"
      @close="closeMultiModifyType"
      @submit="confirmModifyTypeData"
    />
    <!-- 详情 -->
    <commodity-library-detail-dialog
      :visible="commodityLibraryDetailDialogVisible"
      :data="commodityLibraryDetailData"
      @close="closeCommodityLibraryDetailDialog"
    />
    <!-- 添加/编辑食材弹窗 -->
    <add-commodity-dialog
      :visible="addCommodityDialogVisible"
      :type="dialogOperationType"
      :editData="editCommodityData"
      @close="closeAddCommodityDialog"
      @submit="submitAddCommodityDialog"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, to, replaceSingleQuote, deepClone } from '@/utils'
import {
  NUTRITION_LIST,
  COMMODITY_SEARCH_SETTING_SUPER,
  COMMODITY_SEARCH_SETTING_MERCHANT
} from './constants'
import selectLaber from '../components/selectLaber.vue'
import MultiModifyTypeDialog from './components/MultiModifyTypeDialog.vue'
import CommodityLibraryDetailDialog from './components/CommodityLibraryDetailDialog.vue'
import AddCommodityDialog from './components/AddCommodityDialog.vue'
export default {
  name: 'SuperCommodityLibrary',
  mixins: [exportExcel],
  data() {
    return {
      tabType: 'system',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      updateSetting: false,
      tableData: [],
      searchFormSetting: deepClone(COMMODITY_SEARCH_SETTING_SUPER),
      dialogData: {},
      dialogTitle: '营养信息',
      dialogType: '',
      dialogVisible: false,
      dialogLoading: false,
      dialogWidth: '700px',
      dialogTop: '20vh',
      nutritionList: deepClone(NUTRITION_LIST),
      formData: {},
      checkList: [],
      tableDataIngredients: [],
      selectLaberDialogVisible: false,
      titleSelectLaber: '',
      batchLabelType: '', // 批量标签type
      ruleSingleInfo: {}, // 标签
      isAllSelect: false, // 全选
      selectListIdCount: 0, // 选中的id数量
      multiModifyTypeDialogVisible: false, // 批量修改分类
      options: [],
      commodityLibraryDetailDialogVisible: false, // 详情
      commodityLibraryDetailData: {}, // 详情数据
      addCommodityDialogVisible: false, // 添加/编辑食材弹窗显示状态
      dialogOperationType: 'add', // 弹窗操作类型：add-添加，modify-修改
      editCommodityData: {}, // 编辑时的食材数据
      multyModifyType: 'food' // 批量修改分类类型：ingredient-食材，food-菜品
    }
  },
  components: { selectLaber, MultiModifyTypeDialog, CommodityLibraryDetailDialog, AddCommodityDialog },
  created() {
    // this.initLoad()
    this.getAllLabelGroupList()
    this.getCategorylist()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getTypeFoodlist()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getTypeFoodlist()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
      this.getAllLabelGroupList()
      this.getCategorylist()
    },
    // tab 栏点击事件
    tabClick(type) {
      this.tabType = type
      if (type === 'system') {
        this.searchFormSetting = COMMODITY_SEARCH_SETTING_SUPER
      } else {
        this.searchFormSetting = COMMODITY_SEARCH_SETTING_MERCHANT
      }
      this.tableData = []
      // this.getTypeFoodlist()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            if (key === 'sort_ids' || key === "label_list") {
              if (Array.isArray(data[key].value) && data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else if (data[key].value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    getTypeFoodlist() {
      if (this.tabType === 'system') {
        this.getFoodlist()
      } else {
        this.getFoodManchantlist()
      }
    },
    // 获取系统食材列表
    async getFoodlist() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      this.tableData = []
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.labelList = []
          v.ingredientsLabel = []
          v.ingredients_list.forEach(k => {
            if (k.label.length) {
              k.label.forEach(m => {
                m.type = 'ingredients'
                v.ingredientsLabel.push(m)
              })
            }
          })
          let uniqueArr = this.fn2(v.ingredientsLabel)
          v.labelList.push(...v.label, ...uniqueArr)
          if (v.alias_name !== null) {
            v.all_alias_name = v.alias_name.join(',')
          } else {
            v.alias_name = []
          }
          return v
        })
        this.changeTableSelection()
      } else {
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 去重
    fn2(arr) {
      const res = new Map()
      return arr.filter(arr => !res.has(arr.id) && res.set(arr.id, arr.id))
    },
    // 获取商户食材列表
    async getFoodManchantlist() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodMerchantListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      this.tableData = []
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          if (item.alias_name !== null) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
        this.updateSetting = res.data.food_upload
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 获取食材分类列表
    async getCategorylist() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryAllListPost({
          page: 1,
          page_size: 99999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.sort_ids.dataList = this.deleteEmptyGroupFoodCategory(res.data.results)
        this.options = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开弹窗
    showDialogHandler(type, data) {
      this.dialogType = type
      if (type === 'nutrition') {
        this.dialogTitle = '营养信息'
        this.dialogWidth = '700px'
        this.dialogTop = '20vh'
        this.setDialogNutriton(data)
      } else if (type === 'ingredientsList') {
        this.dialogTitle = '食材'
        this.dialogWidth = '500px'
        this.dialogTop = '20vh'
        this.tableDataIngredients = data.ingredients_list
      }
      this.dialogVisible = true
    },
    // 设置弹窗营养的数据
    setDialogNutriton(row) {
      this.formData = {}
      if (!row.nutrition) row.nutrition = {}
      let element = row.nutrition.element
        ? JSON.parse(replaceSingleQuote(row.nutrition.element))
        : {}
      let vitamin = row.nutrition.vitamin
        ? JSON.parse(replaceSingleQuote(row.nutrition.vitamin))
        : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(
            this.formData,
            nutrition.key,
            row.nutrition[nutrition.key] ? row.nutrition[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'element') {
          this.$set(
            this.formData,
            nutrition.key,
            element[nutrition.key] ? element[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'vitamin') {
          this.$set(
            this.formData,
            nutrition.key,
            vitamin[nutrition.key] ? vitamin[nutrition.key] : 0
          )
        }
      })
    },
    // 更新菜品到商户端
    distributeHandler() {
      this.$confirm(`是否更新到商户？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundAdminFoodSyncFoodPost())
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.currentPage = 1
              this.getTypeFoodlist()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 删除
    deleteHandler(type, id) {
      let ids = []
      if (type === 'single') {
        ids = [id]
      } else {
        if (!this.checkList.length) return this.$message.error('请先选择要删除的数据！')
        ids = this.checkList
      }
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminFoodDeletePost({
                ids: ids
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.currentPage = 1
              this.getTypeFoodlist()
              this.checkList = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 批量打标签
    async getFoodBatchAddLabel(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodBatchAddLabelPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchHandle('search')
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 获取所有的标签
    async getAllLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminLabelGroupAllLabelGroupListPost({
          page_size: 999999,
          page: 1,
          type: 'food'
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(v => {
          v.id = `${v.id}_1`
          if (!v.label_list.length) {
            v.isDisabled = true
          }
          return v
        })
        this.searchFormSetting.label_list.dataList = res.data.results
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 是否允许商户上传信息
    updateSettingHandler(e) {
      let content = '是否允许商户上传信息?'
      if (!e) {
        content = '是否关闭商户上传信息?'
      }
      this.$confirm(content, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminFoodChangeUploadPost({
                is_enable: this.updateSetting ? 1 : 0
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getTypeFoodlist()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              this.updateSetting = !this.updateSetting
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 创建到食材库
    addToSystem(row) {
      this.$router.push({
        name: 'SuperAddMerchantCommodityToSuper',
        query: {
          type: 'modify',
          data: this.$encodeQuery(row)
        }
      })
      // this.$confirm(`是否创建到菜品/商品库?`, {
      //   confirmButtonText: this.$t('dialog.confirm_btn'),
      //   cancelButtonText: this.$t('dialog.cancel_btn'),
      //   closeOnClickModal: false,
      //   customClass: 'ps-confirm',
      //   cancelButtonClass: 'ps-cancel-btn',
      //   confirmButtonClass: 'ps-btn',
      //   center: true,
      //   beforeClose: async (action, instance, done) => {
      //     if (action === 'confirm') {
      //       if (this.isLoading) return this.$message.error('请勿重复提交！')
      //       this.isLoading = true
      //       instance.confirmButtonLoading = true
      //       const [err, res] = await to(this.$apis.apiBackgroundAdminFoodAddToSystemPost({
      //         id: id
      //       }))
      //       this.isLoading = false
      //       if (err) {
      //         this.$message.error(err.message)
      //         return
      //       }
      //       if (res.code === 0) {
      //         done()
      //         this.$message.success(res.msg)
      //         this.getTypeFoodlist()
      //       } else {
      //         instance.confirmButtonLoading = false
      //         this.$message.error(res.msg)
      //       }
      //       instance.confirmButtonLoading = false
      //     } else {
      //       if (!instance.confirmButtonLoading) {
      //         done()
      //         this.updateSetting = !this.updateSetting
      //       }
      //     }
      //   }
      // })
      //   .then(e => {
      //   })
      //   .catch(e => {})
    },
    // table select
    handleSelectionChange(val) {
      // this.checkList = []
      // val.map(item => {
      //   this.checkList.push(item.id)
      // })
    },
    showMoreHandler(e) {
      e.target.parentNode.classList.remove('hide')
    },
    hideMoreHandler(e) {
      e.target.parentNode.classList.add('hide')
    },
    batchLabelClick(type) {
      this.batchLabelType = type
      if (type === 'batchLabelDel') {
        this.titleSelectLaber = '批量移除标签'
      } else if (type === 'batchLabelAdd') {
        this.titleSelectLaber = '批量打标签'
      }
      if (!this.checkList.length) return this.$message.error(`请先选择要${this.titleSelectLaber}的数据！`)
      // 保存一下选择标签后需要返显
      this.ruleSingleInfo = {
        labelType: 'food'
      }
      this.selectLaberDialogVisible = true
    },
    selectLaberData(params) {
      this.$confirm(`是否${this.titleSelectLaber}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            let labelParams = {
              ids: this.checkList,
              label_list: params.selectLabelIdList
            }
            let [err, res] = ''
            if (this.batchLabelType === 'batchLabelAdd') {
              ;[err, res] = await to(
                this.$apis.apiBackgroundAdminFoodBatchAddLabelPost(labelParams)
              )
            } else {
              ;[err, res] = await to(
                this.$apis.apiBackgroundAdminFoodBatchDeleteLabelPost(labelParams)
              )
            }
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.$refs.tableData.clearSelection()
              this.getTypeFoodlist()
              this.checkList = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            this.checkList = []
            if (this.$refs.tableData) {
              this.$refs.tableData.clearSelection()
            }
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 移除标签
    closeTag(data, row) {
      this.batchLabelType = 'delSingleTag'
      this.titleSelectLaber = '删除该标签'
      let params = {
        selectLabelIdList: [data.id]
      }
      this.checkList = [row.id]
      this.selectLaberData(params)
    },
    // 跳转添加菜品页面
    addFoodHandler() {
      // this.$router.push({
      //   name: 'SuperAddCommodity',
      //   query: {
      //     type: 'add'
      //   }
      // })
      this.dialogOperationType = 'add'
      this.editCommodityData = {}
      this.addCommodityDialogVisible = true
    },
    modifyFoodHandler(row) {
      // this.$router.push({
      //   name: 'SuperAddCommodity',
      //   query: {
      //     type: 'modify',
      //     data: this.$encodeQuery(row)
      //   }
      // })
      this.dialogOperationType = 'modify'
      this.editCommodityData = row
      this.addCommodityDialogVisible = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getTypeFoodlist()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTypeFoodlist()
    },
    dialogHandleClose() {},
    // 导入菜品/商品
    importHandler(type) {
      this.$router.push({
        name: 'SuperImportCommodity',
        params: {
          type: type
        }
      })
    },
    // 导入菜品/商品图片
    importImgHandler(type) {
      this.$router.push({
        name: 'SuperImportCommodityImage'
      })
    },
    // 导出弹窗
    gotoExport() {
      const option = {
        type: 'SuperCommodityLibrary',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      if (this.tabType === 'system') {
        option.type = 'SuperCommodityLibrarySystem'
      } else {
        option.type = 'SuperCommodityLibraryMerchant'
      }
      this.exportHandle(option)
    },
    gotoCategory() {
      this.$router.push({
        name: 'SuperMealFoodClassificationNew',
        params: {
        }
      })
    },
    // 全选
    handleAllSelectChange() {
      this.checkList = []
      this.$refs.tableData && this.$refs.tableData.clearSelection();
      if (this.isAllSelect) {
        this.getAllListIds()
      }
    },
    // 改变表格选中
    changeTableSelection() {
      if (this.tableData.length) {
        this.tableData.forEach((item, index) => {
          // 匹配勾选上
          if (this.checkList.includes(item.id)) {
            this.$nextTick(() => {
              this.$refs.tableData.toggleRowSelection(item);
            })
          }
        })
      }
    },
    // 获取到所有列表的id全选
    async getAllListIds() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminFoodListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        only_id: true
      })
      this.isLoading = false
      if (res.code === 0) {
        this.checkList = res.data || []
        this.selectListIdCount = res.data.length
        this.changeTableSelection()
      } else {
        this.$message.error(res.msg)
      }
    },
    selectSelection(row) {
      this.changeSelectSelection(row)
    },
    selectSelectionAll(row) {
      this.changeSelectSelection(row)
    },
    changeSelectSelection (row) {
      let rowIdS = row.map(v => { return v.id })
      this.tableData.forEach((item, indx) => {
        if (!rowIdS.includes(item.id)) {
          var selectListIdIndex = this.checkList.indexOf(item.id)
          if (selectListIdIndex !== -1) {
            this.checkList.splice(selectListIdIndex, 1)
          }
        } else {
          this.checkList.push(...rowIdS)
          this.checkList = [...new Set(this.checkList)]
        }
      })
      if (this.checkList.length) {
        this.isAllSelect = this.selectListIdCount === this.checkList.length ? true : 0
      }
      console.log("this.checkList", this.checkList.length);
    },
    // 批量修改分类
    confirmModifyTypeData() {
      console.log("this.checkList", this.checkList);
      this.multiModifyTypeDialogVisible = false
      this.checkList = []
      this.getTypeFoodlist()
    },
    // 关闭批量修改分类弹窗
    closeMultiModifyType() {
      this.multiModifyTypeDialogVisible = false
    },
    // 批量修改分类
    handlerMultiModifyType() {
      if (!this.checkList.length) {
        return this.$message.error('请先选择要修改分类的数据！')
      }
      this.multiModifyTypeDialogVisible = true
    },
    // 显示详情
    showIngredientsDetail(row) {
      console.log("row", row);
      let detailData = row ? deepClone(row) : {}
      let nutrition = row.nutrition
      detailData.nutrition_info = {
        default: {
          axunge: nutrition.axunge,
          carbohydrate: nutrition.carbohydrate,
          cholesterol: nutrition.cholesterol,
          dietary_fiber: nutrition.dietary_fiber,
          energy_kcal: nutrition.energy_kcal,
          energy_mj: nutrition.energy_mj,
          protein: nutrition.protein
        },
        element: nutrition.element,
        vitamin: nutrition.vitamin
      }
      this.commodityLibraryDetailData = detailData
      this.commodityLibraryDetailDialogVisible = true
    },
    // 关闭详情
    closeCommodityLibraryDetailDialog() {
      this.commodityLibraryDetailDialogVisible = false
    },
    // 关闭添加/编辑食材弹窗
    closeAddCommodityDialog() {
      this.addCommodityDialogVisible = false
      this.editCommodityData = {}
    },
    // 提交添加/编辑食材
    submitAddCommodityDialog() {
      this.addCommodityDialogVisible = false
      this.currentPage = 1
      this.getTypeFoodlist() // 刷新列表
    },
    // 处理下没有children_list
    deleteEmptyGroupFoodCategory(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children) {
            if (item.children.length > 0) {
              item.disabled = false
              traversal(item.children)
            } else {
              if (Reflect.has(item, 'level') && item.level) {
                item.disabled = true
              }
              _that.$delete(item, 'children')
            }
          } else {
            if (Reflect.has(item, 'level') && item.leve) {
              item.disabled = true
            }
            _that.$delete(item, 'children')
          }
        })
      }
      traversal(treeData)
      return treeData
    }
  }
}
</script>

<style lang="scss" scoped>
.super-commodity-library {
  .tab {
    margin-bottom: 20px;
    .tab-item {
      display: inline-block;
      // width: 90px;
      padding: 0 10px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 14px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }
    }
  }
}
.ps-dialog {
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
.collapse-wrapper {
  .collapse-list {
    // text-align: left;
    .collapse-data {
      display: inline-block;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .collapse-more {
      display: none;
    }
    .food-ellipsis {
      display: none;
    }
    .collapse-hide {
      display: block;
      color: #f3b687;
      font-size: 12px;
      cursor: pointer;
    }
    &.hide {
      .collapse-data:nth-child(n + 4) {
        display: none;
      }
      .collapse-more {
        text-align: center;
        display: block;
        color: #f3b687;
        font-size: 12px;
        cursor: pointer;
      }
      .food-ellipsis {
        display: inline-block;
        width: 100px;
      }
      .collapse-hide {
        display: none;
      }
    }
  }
}
</style>
