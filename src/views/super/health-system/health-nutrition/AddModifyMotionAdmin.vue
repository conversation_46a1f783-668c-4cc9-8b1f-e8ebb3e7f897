<template>
  <div class="add_modify_motion_admin container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      size="small"
      label-width="80px"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="padding: 0 20px">
          <el-form-item label="封面" prop="image">
            <el-upload
              class="avatar-uploader"
              :data="uploadParams"
              ref="uploadFood"
              :action="actionUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="formData.image" :src="formData.image" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="运动名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入运动名称"
              class="ps-input p-r-48"
              style="width: 240px"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="数量" prop="count">
            <el-input
              v-model="formData.count"
              placeholder="请输入数量"
              class="ps-input p-r-48"
              style="width: 240px"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="单位" prop="counting_unit">
            <el-select
              v-model="formData.counting_unit"
              placeholder="请下拉选择"
              class="ps-select"
              popper-class="ps-popper-select"
            >
              <el-option label="分钟" value="minute"></el-option>
              <el-option label="公里" value="kilometer"></el-option>
              <el-option label="次" value="freq"></el-option>
              <el-option label="组" value="group"></el-option>
              <el-option label="套" value="set"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="热量" prop="energy_kcal">
            <el-input
              v-model="formData.energy_kcal"
              placeholder="请输入热量"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="40"
              show-word-limit
            >
              <template slot="append">千卡</template>
            </el-input>
          </el-form-item>
        </div>
      </div>

      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '编辑' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'

export default {
  name: 'SuperAddEditArticle',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        image: '',
        name: '',
        count: '',
        counting_unit: '',
        energy_kcal: ''
      },
      formRuls: {
        // image: [
        //   {
        //     required: true,
        //     message: '请上传图片',
        //     trigger: 'blur'
        //   }
        // ],
        name: [
          {
            required: true,
            message: '请输入运动名称',
            trigger: 'blur'
          }
        ],
        count: [
          {
            required: true,
            message: '请输入数量',
            trigger: 'blur'
          }
        ],
        counting_unit: [
          {
            required: true,
            message: '请选择是单位',
            trigger: 'blur'
          }
        ],
        energy_kcal: [
          {
            required: true,
            message: '请输入热量',
            trigger: 'blur'
          }
        ]
      },
      actionUrl: '',
      uploadParams: {}
    }
  },
  created() {
    this.getUploadToken()
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        this.formData = {
          id: data.id,
          image: data.image,
          name: data.name,
          count: data.count,
          counting_unit: data.counting_unit,
          energy_kcal: data.energy_kcal
        }
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: 'jpeg/png'
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleAvatarSuccess(res, file) {
      if (res.code === 0) {
        this.$refs.uploadFood.clearFiles()
        this.formData.image = res.data.public_url
        this.getUploadToken()
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    // 添加
    async addModifyArticle(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'add') {
        ;[err, res] = await to(this.$apis.apiBackgroundAdminSportsAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundAdminSportsModifyPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.addModifyArticle(this.formData)
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading =true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading =false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scope>
.add_modify_motion_admin {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
