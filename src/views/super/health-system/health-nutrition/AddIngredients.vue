<template>
  <div class="super-add-ingredients container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      class=""
      size="small"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div class="" style="padding: 0 20px;">
          <el-form-item label="食材图片" prop="" class="block-label form-content-flex">
            <div class="">
              <div class="inline-block upload-w">
                <el-upload
                  v-loading="uploading"
                  element-loading-text="上传中"
                  class="file-upload"
                  ref="fileUpload"
                  drag
                  :action="serverUrl"
                  :data="uploadParams"
                  :file-list="fileLists"
                  :on-success="uploadSuccess"
                  :before-upload="beforeFoodImgUpload"
                  :limit="1"
                  :multiple="false"
                  :show-file-list="false"
                  :headers="headersOpts"
                  accept=".jpeg,.jpg,.png,.bmp"
                  >
                  <slot>
                    <div class="upload-t" v-if="!formData.imageList.length">
                      <i class="el-icon-circle-plus"></i>
                      <div class="el-upload__text">
                        <span class="">上传食材图片</span>
                      </div>
                    </div>
                    <el-image
                      class="el-upload-dragger"
                      v-if="formData.imageList.length"
                      :src="formData.imageList[0]"
                      @click="removeFoodImg"
                      fit="contain">
                    </el-image>
                  </slot>
                </el-upload>
              </div>
              <div class="inline-block upload-tips">
                <span class="" style="padding-left: 2px;">上传：食材图片。</span><br />
                建议图片需清晰，图片内容与名称相符。<br />
                仅支持jpg、png、bmp格式，大小不超过5M
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="">
          <div class="" style="width: 48%; padding: 0 20px">
            <el-form-item label="食材名称" prop="name" class="block-label form-content-flex">
              <el-input
                v-model="formData.name"
                placeholder="请输入食材名称"
                maxlength="30"
                class="ps-input"
                style="width: 80%"
              ></el-input>
              <el-tooltip effect="dark" content="增加食材别名" placement="top">
                <img class="add-btn-img" @click="addAliasName" src="@/assets/img/plus.png" alt="">
              </el-tooltip>
            </el-form-item>
            <div v-if="formData.aliasName.length">
              <el-form-item label="食材别名" class="block-label">
                <el-form-item
                  :class="[index>0?'m-t-15':'','alias-name-form']"
                  v-for="(item,index) in formData.aliasName"
                  :key="index"
                  :rules="formRuls.aliasName"
                  :prop="`aliasName[${index}]`">
                  <el-input maxlength="20" style="width: 80%" v-model="formData.aliasName[index]" placeholder="请输入食材别名" class="ps-input"></el-input>
                  <img src="@/assets/img/plus.png" @click="addAliasName" alt="">
                  <img src="@/assets/img/reduce.png" @click="delAliasName(index)" alt="">
                </el-form-item>
              </el-form-item>
            </div>
            <el-form-item label="食材类别" prop="sort_id" class="block-label">
              <el-cascader
                class="ps-select"
                placeholder="请选择或输入食材类别"
                style="width: 80%"
                v-model="formData.sort_id"
                :options="categoryList"
                :show-all-levels="false"
                :props="cascaderProps"
              ></el-cascader>
              <el-button class="m-l-10" @click="gotoCategory" type="text">添加分类</el-button>
            </el-form-item>
            <el-form-item label="标签" prop="" class="">
              <el-button
                class="ps-origin-btn"
                type="primary"
                size="small"
                @click="labelClick"
              >
                选择标签
              </el-button>
            </el-form-item>
            <el-form-item :label="`${groupKey}:`" prop="" class="" v-for="(labelGroupItem,groupKey,labelGroupIndex) in formData.labelGroupInfoList" :key="labelGroupIndex">
              <el-tag
                class="m-r-5 collapse-data"
                v-for="(item, index) in labelGroupItem"
                :key="index"
                size="medium"
                effect="plain"
                type="info"
                color="#fff"
                closable
                @close="closeTag(groupKey,index,item)"
              >
                <!-- light -->
                {{item.name}}
              </el-tag>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">营养信息
            <span class="tip-o-7">（每100克所含的营养信息）</span><el-switch v-model="formData.is_enable_nutrition" active-color="#ff9b45" inactive-color="#ffcda2"> </el-switch></div>
        </div>
        <div class="table-content" v-if="formData.is_enable_nutrition">
          <template v-for="nutrition in currentNutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <el-form-item :prop="nutrition.key" :rules="formRuls.nutrition">
                <el-input
                  style="width: 120px"
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                ></el-input>
                <span style="margin-left: 10px">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
          <div class="text-center pointer">
            <span @click="showAll = !showAll" style="color:#027DB4;">{{ showAll ? '收起' : '查看更多营养信息' }}</span>
          </div>
        </div>
      </div>
      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-form>
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, replaceSingleQuote, getSuffix, getToken } from '@/utils'
import { NUTRITION_LIST } from './constants'
import selectLaber from '../components/selectLaber.vue'
export default {
  name: 'SuperAddIngredients',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    let validataNutrition = (rule, value, callback) => {
      if (value) {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('营养数据有误，仅支持两位小数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        id: '',
        name: '', // 食材名
        aliasName: [], // 食材别名
        sort_id: '', // 分类
        ingredient_id: '', // 重复的id
        is_enable_nutrition: false,
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {}, // 带标签组名字的数据
        imageList: []
      },
      formRuls: {
        name: [{ required: true, message: '食材名称不能为空', trigger: 'blur' }],
        aliasName: [{ required: true, message: '请输入食材别名', trigger: 'blur' }],
        category: [{ required: true, message: '请选择食材类别', trigger: 'blur' }],
        nutrition: [{ validator: validataNutrition, trigger: 'change' }],
        sort_id: [{ required: true, message: '请选择食材分类', trigger: 'blur' }]
      },
      nutritionList: NUTRITION_LIST,
      categoryList: [], // 分类
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'sort_list',
        emitPath: false // 绑定的内容只获取最后一级的value值。
      },
      selectLaberDialogVisible: false,
      // 点击当前添加标签每条的数据
      ruleSingleInfo: {},
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      fileLists: [],
      uploadParams: {
        prefix: 'super_food_img'
      },
      showAll: false,
      showDialog: false,
      uploading: false
    }
  },
  computed: {
    currentNutritionList: function () {
      let result = []
      if (!this.showAll) {
        result = this.nutritionList.slice(0, 4)
      } else {
        result = this.nutritionList
      }
      return result
    }
  },
  components: { selectLaber },
  created() {
    this.type = this.$route.query.type
    // Object.freeze()
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      await this.getCategoryCategoryNameList()
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        this.formData.name = data.name
        this.formData.aliasName = data.alias_name
        this.formData.sort_id = data.sort
        if (data.image) {
          this.formData.imageList = [data.image]
          this.fileLists = [{
            url: data.image,
            name: data.image,
            status: "success",
            uid: data.image
          }]
        }
        this.categoryList.map(v => {
          if (v.sort_list.length) {
            v.sort_list.map(child => {
              if (child.id && (Number(child.id.split('-')[1]) === data.sort)) {
                this.formData.sort_id = child.id
              }
            })
          }
        })
        //  回显示标签组名字 {'aa':[{xx:xx}]}
        if (data.label.length) {
          // 格式化标签
          this.initLabelGroup(data.label)
        }
        this.formData.selectLabelListData = data.label
        this.formData.selectLabelIdList = data.label.map(v => { return v.id })
        // end
        this.formData.id = data.id
        this.formData.is_enable_nutrition = !!data.is_enable_nutrition
        this.setNutritonData(data)
      } else {
        this.setNutritonData({})
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    // 设置营养的数据
    setNutritonData(row) {
      if (!row.nutrition_info) row.nutrition_info = {}
      let element = row.nutrition_info.element
        ? JSON.parse(replaceSingleQuote(row.nutrition_info.element))
        : {}
      let vitamin = row.nutrition_info.vitamin
        ? JSON.parse(replaceSingleQuote(row.nutrition_info.vitamin))
        : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(
            this.formData,
            nutrition.key,
            row.nutrition_info[nutrition.key] ? row.nutrition_info[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'element') {
          this.$set(
            this.formData,
            nutrition.key,
            element[nutrition.key] ? element[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'vitamin') {
          this.$set(
            this.formData,
            nutrition.key,
            vitamin[nutrition.key] ? vitamin[nutrition.key] : 0
          )
        }
      })
    },
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost()
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.categoryList = res.data.map(v => {
          if (v.sort_list.length) {
            v.sort_list = v.sort_list.map(item => {
              item.id = v.id + '-' + item.id
              return item
            })
          }
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化下参数
    formatParams() {
      let params = {
        name: this.formData.name,
        label_list: this.formData.selectLabelIdList,
        alias_name: this.formData.aliasName,
        is_enable_nutrition: this.formData.is_enable_nutrition ? 1 : 0
      }
      if (this.formData.ingredient_id && this.type === 'add') {
        params.ingredient_id = this.formData.ingredient_id
      }
      if (this.formData.sort_id) {
        if (this.formData.sort_id.indexOf('-') > -1) {
          let sortId = this.formData.sort_id.split('-')
          params.sort_id = sortId[1]
        } else {
          params.sort_id = this.formData.sort_id
        }
      }
      if (this.type === 'modify') {
        params.id = this.formData.id
      }
      // 是否开启营养录人
      if (this.formData.is_enable_nutrition) {
        // 营养
        let element = {}
        let vitamin = {}
        NUTRITION_LIST.forEach(nutrition => {
          let value = this.formData[nutrition.key] ? this.formData[nutrition.key] : 0
          if (nutrition.type === 'default') {
            params[nutrition.key] = value
          }
          if (nutrition.type === 'element') {
            element[nutrition.key] = value
          }
          if (nutrition.type === 'vitamin') {
            vitamin[nutrition.key] = value
          }
        })
        params.element = JSON.stringify(element)
        params.vitamin = JSON.stringify(vitamin)
      }
      if (this.formData.imageList.length) {
        params.image = this.formData.imageList[0]
      }
      return params
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (this.type === 'modify') {
            this.modifyIngredients()
          } else {
            this.addIngredients()
          }
        }
      })
    },
    // 添加
    async addIngredients() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientAddPost(this.formatParams())
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else if (res.code === 2) {
        this.formData.ingredient_id = res.data.ingredient_id
        this.$confirm(res.msg, '提示', {
          confirmButtonText: '覆 盖',
          cancelButtonText: this.$t('dialog.cancel_btn'),
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              await this.addIngredients()
              instance.confirmButtonLoading = false
            } else {
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
          .then(e => {
          })
          .catch(e => {
            this.formData.ingredient_id = ''
          })
      } else {
        this.$message.error(res.msg)
      }
    },
    labelClick() {
      this.ruleSingleInfo = {
        labelType: 'ingredient',
        selectLabelIdList: this.formData.selectLabelIdList,
        selectLabelListData: this.formData.selectLabelListData
      }
      this.selectLaberDialogVisible = true
    },
    // 删除标签
    closeTag(key, index, item) {
      // 删除
      let idx = this.formData.selectLabelIdList.indexOf(item.id)
      let ids = this.formData.selectLabelListData.indexOf(item)
      this.formData.selectLabelIdList.splice(idx, 1)
      this.formData.selectLabelListData.splice(ids, 1)
      // 重置数据
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    // 选择标签
    selectLaberData(params) {
      this.formData.selectLabelIdList = params.selectLabelIdList
      this.formData.selectLabelListData = params.selectLabelListData
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    initLabelGroup(data) {
      data.forEach(v => {
        if (!this.formData.labelGroupInfoList[v.label_group_name]) {
          this.formData.labelGroupInfoList[v.label_group_name] = []
        }
        if (this.formData.labelGroupInfoList[v.label_group_name] && !this.formData.labelGroupInfoList[v.label_group_name].includes(v)) {
          this.formData.labelGroupInfoList[v.label_group_name].push(v)
        }
      })
    },
    // 修改
    async modifyIngredients() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientModifyPost(this.formatParams())
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoCategory() {
      this.$router.push({
        name: 'SuperImportIngredientsCategory'
      })
    },
    // 添加食材别名
    addAliasName() {
      this.formData.aliasName.push('')
    },
    delAliasName(index) {
      this.formData.aliasName.splice(index, 1);
    },
    // 移除图片
    removeFoodImg(index) {
      this.formData.imageList.splice(index, 1)
      this.fileLists.splice(index, 1)
    },
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res.code === 0) {
        this.fileLists = fileList
        this.formData.imageList = [res.data.public_url]
        console.log(this.formData.imageList)
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    async clickConfirmHandle(e) {
      this.clickCancleHandle()
      await this.$sleep(100)
      this.$router.push({
        name: 'MerchantCopyFoods'
      })
    },
    clickCancleHandle(e) {
      this.$closeCurrentTab(this.$route.path)
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss">
.super-add-ingredients {
  .m-l-10{
    margin-left: 10px;
  }
  .block-label{
    width: 100%;
    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
  .form-content-flex{
    .el-form-item__content{
      display: flex;
    }
  }
  .add-btn-img{
    width:25px;
    height:25px;
    margin:3px 0 0 10px;
  }
  .alias-name-form{
    margin-bottom: 0px!important;
    .el-form-item__content{
      display: flex;
      align-items: center;
      img{
        width: 25px;
        height: 25px;
        margin-left: 10px;
      }
    }
  }
  .tip-o-7{
    font-size: 14px;
    color: #23282d;
    opacity: 0.7;
  }
  .nutrition-item{
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
  .upload-w{
    width: 224px;
    height: 142px;
    border-radius: 4px;
    border: solid 1px #e0e6eb;
    text-align: center;
    vertical-align: top;
    // display: flex;
    // justify-content: center;
    // align-items: center;
  }
  .el-upload-dragger{
    width: 224px;
    height: 142px;
  }
  .upload-t{
    vertical-align: top;
    margin-top: 35px;
    color: #ff9b45;
    .el-icon-circle-plus{
      font-size: 30px;
      color: #ff9b45;
    }
  }
  .upload-tips{
    margin-top: 30px;
    padding-left: 20px;
    color: #9fa7ad;
    // line-height: 1.5;
  }
  .bg-white{
    padding: 6px;
    background-color: #fff;
  }
  .bg-text{
    padding: 8px 18px;
    background-color: #f2f2f2;
  }
}
</style>
