<template>
  <div class="HabitCultivate">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoHabitCultivate('add')">
            新增
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'has_children' }"
        >
          <el-table-column prop="icon" label="图标" align="center" width="170px">
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.image"
                style="width: 50px; height: 50px"
                :src="scope.row.image"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="习惯名称" align="center"></el-table-column>
          <el-table-column prop="users" label="使用用户数" align="center"></el-table-column>
          <el-table-column prop="clocks" label="打卡总数" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="update_time" label="修改时间" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <el-table-column prop="" label="使用状态" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.is_open"
                active-color="#f59933"
                @change="modifyStatus($event, scope.row.id)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="gotoHabitCultivate('modify', scope.row)"
              >
                编辑
              </el-button>
              <span style="margin: 0 10px; color: #e2e8f0">|</span>
              <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteHandler('single', scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'

export default {
  name: 'HealthAssessment',
  components: {},
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      currentPage: 1, // 第几页
      totalCount: 0, // 总条数
      totalPageSize: 0, // 总页数
      tableData: [],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '修改时间',
              value: 'update_time'
            }
          ]
        },
        select_time: {
          type: 'datetimerange',
          label: '',
          format: 'yyyy-MM-dd HH:mm:ss',
          value: []
        },
        name: {
          type: 'input',
          label: '习惯名称',
          value: '',
          placeholder: '请输入习惯名称'
        },
        status: {
          type: 'select',
          label: '状态',
          value: '',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: 'all'
            },
            {
              label: '启用',
              value: 'enable'
            },
            {
              label: '禁用',
              value: 'disable'
            }
          ]
        }
      },
      dialogLoading: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getHabitList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 习惯养成列表
    async getHabitList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminHabitListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
        this.tableData = res.data.results.map(v => {
          // eslint-disable-next-line no-unneeded-ternary
          v.is_open = v.status === 'enable' ? true : false
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHandler(type, id) {
      let ids = []
      if (type === 'single') {
        ids = [id]
      }
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundHealthyAdminHabitDeletePost({
                ids: [id]
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1 && type === 'one') {
                  this.currentPage--
                } else if (
                  this.currentPage === this.totalPageSize &&
                  ids.length === this.tableData.length
                ) {
                  this.currentPage--
                }
              }
              this.getHabitList()
              // this.checkList = []
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    async modifyStatus(isOpen, id) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundHealthyAdminHabitModifyPost({
        id,
        status: isOpen ? 'enable' : 'disable'
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('操作成功！')
        this.getHabitList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转新增编辑
    gotoHabitCultivate(type, row) {
      this.$router.push({
        name: 'SuperAddModifyHabit',
        params: {
          type: type
        },
        query: {
          type: type,
          data: type === 'modify' ? this.$encodeQuery(row) : ''
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getHabitList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getHabitList()
    },
    // 格式化查询参数
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
</style>
