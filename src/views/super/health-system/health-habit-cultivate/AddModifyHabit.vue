<template>
  <div class="add-modify-habit container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      size="small"
      label-width="80px"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="padding: 0 20px">
          <el-form-item label="图标" prop="image" class="upload-block-label">
            <!-- <div class="inline-block upload-w"> -->
            <file-upload
              class="uploadImg"
              ref="uploadImg"
              :limit="1"
              @fileLists="getSuccessUploadRes"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :on-remove="remove"
              prefix="habit"
            >
              <el-image
                v-if="formData.image"
                :src="formData.image"
                class="avatar"
                @click="clearFileHandle"
              />
              <div v-else class="upload-t inline-block upload-w">
                <i class="el-icon-plus avatar-uploader-icon"></i>
                <!-- <i class="el-icon-circle-plus"></i> -->
              </div>
              <div class="el-upload__tip" slot="tip">只能上传图片</div>
            </file-upload>
            <!-- </div> -->
          </el-form-item>
          <el-form-item label="习惯名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入习惯名称"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="6"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '编辑' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'

export default {
  name: 'SuperAddEditArticle',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        image: '',
        // image:"",
        name: ''
      },
      formRuls: {
        image: [
          {
            required: true,
            message: '请上传图片',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入运动名称',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        console.log(data)
        this.formData = {
          id: data.id,
          image: data.image,
          name: data.name
        }
      }
    },
    // 移除图片
    remove() {
      this.formData.image = ''
    },
    // 上传图片成功回调
    getSuccessUploadRes(res) {
      if (res.length) {
        this.formData.image = res[0].url
      }
      this.isLoading = false
    },
    clearFileHandle() {
      // 每次上传成功先清空上一次的结果，否则下一次无法再次选择
      this.$refs.uploadImg.clearHandle()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
    }, 300),
    // 上传图片
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      this.isLoading = true
      return isJPG && isLt5M
    },
    // 添加
    async addModifyHabit(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'add') {
        ;[err, res] = await to(this.$apis.apiBackgroundHealthyAdminHabitAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundHealthyAdminHabitModifyPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.addModifyHabit(this.formData)
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scope>
.add-modify-habit {
  .upload-block-label {
    .upload-w {
      width: 100px;
      height: 90px;
      border-radius: 4px;
      border: dashed 1px #e0e6eb;
      text-align: center;
      line-height: 90px;
      .uploadImg {
        width: 100%;
        height: 100%;
      }
    }
    .avatar {
      display: block;
      width: 50px;
      height: 50px;
    }
    .upload-t {
      // color: #ff9b45;
      .el-icon-circle-plus {
        font-size: 30px;
        // color: #ff9b45;
      }
    }
  }
}
</style>
