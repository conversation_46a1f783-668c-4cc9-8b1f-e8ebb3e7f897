<template>
  <div class="super-add-ingredients container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      class=""
      size="small"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="max-width: 300px; padding: 0 20px">
          <el-form-item label="测评名称" prop="name" class="block-label">
            <el-input
              v-model="formData.name"
              placeholder="请输入测评名称"
              class="p-r-48 p-b-10"
              type="textarea"
              :autosize="{ minRows: 0, maxRows: 2 }"
              maxlength="25"
              show-word-limit
            ></el-input>
          </el-form-item>
        </div>
        <div style="max-width: 350px; padding: 0 20px">
          <el-form-item label="测评介绍" prop="content">
            <el-input
              type="textarea"
              placeholder="请输入测评介绍"
              v-model="formData.content"
              :autosize="{ minRows: 4, maxRows: 10 }"
              maxlength="200"
              show-word-limit
              class="p-r-48 p-b-10"
            />
          </el-form-item>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            测评题编写
            <button-icon color="origin" type="add" @click="addQuestionnaire()">
              添加题目
            </button-icon>
          </div>
        </div>
        <div class="table-content">
          <div
            style="max-width: 600px; padding: 0 20px"
            v-for="(questionnaireItem, questionnaireIndex) in formData.topic_list"
            :key="questionnaireIndex"
          >
            <el-form-item
              :label="questionnaireIndex + 1 + '、'"
              :prop="'topic_list.' + questionnaireIndex + '.content'"
              :rules="{
                required: true,
                message: '请输入问题',
                trigger: 'blur'
              }"
              label-width="30px"
            >
              <el-input
                type="textarea"
                placeholder="请输入问题"
                v-model="questionnaireItem.content"
                :autosize="{ minRows: 4, maxRows: 10 }"
                maxlength="200"
                show-word-limit
                class="p-r-48 p-b-10"
              />
            </el-form-item>

            <div class="ps-flex-align-c flex-align-c p-l-20">
              <el-radio-group v-model="questionnaireItem.choice_type" class="ps-radio">
                <el-radio label="single">单选</el-radio>
                <el-radio label="multiple">多选</el-radio>
              </el-radio-group>
              <el-select
                style="margin-left: 20px"
                v-model="questionnaireItem.is_required"
                size="small"
              >
                <el-option label="必填" value="required"></el-option>
                <el-option label="非必填" value="not_required"></el-option>
              </el-select>
              <div
                class="ps-flex-align-c flex-align-c cursor: pointer"
                v-if="formData.topic_list.length > 1"
              >
                <div
                  class="p-l-20"
                  v-if="questionnaireIndex != 0"
                  @click="handleMove(questionnaireIndex, 'up', formData.topic_list)"
                >
                  上移
                  <i class="el-icon-top"></i>
                </div>
                <div
                  class="p-l-20"
                  @click="handleMove(questionnaireIndex, 'down', formData.topic_list)"
                >
                  下移
                  <i class="el-icon-bottom"></i>
                </div>
                <i
                  class="el-icon-circle-close p-l-20 font-size-16"
                  @click="questionnaireRemove(questionnaireIndex)"
                ></i>
              </div>
            </div>
            <div
              class="ps-flex-align-c p-t-20"
              v-for="(optionItem, optionIndex) in questionnaireItem.options_text"
              :key="optionIndex"
            >
              <el-form-item
                :label="chooseNumStr(optionIndex) + '、'"
                :prop="
                  'topic_list.' +
                  questionnaireIndex +
                  '.options_text.' +
                  optionIndex +
                  '.options_text'
                "
                :rules="{
                  required: true,
                  message: '请输入选项',
                  trigger: 'blur'
                }"
                label-width="30px"
              >
                <el-input
                  style="width: 300px"
                  type="textarea"
                  class="p-r-10"
                  :autosize="{ minRows: 0, maxRows: 2 }"
                  maxlength="32"
                  v-model="optionItem.options_text"
                ></el-input>
              </el-form-item>
              <el-form-item
                label-width="20px"
                :prop="
                  'topic_list.' +
                  questionnaireIndex +
                  '.options_text.' +
                  optionIndex +
                  '.options_number'
                "
                :rules="{
                  required: true,
                  message: '请输入分数',
                  trigger: 'blur'
                }"
              >
                <el-input
                  style="width: 120px"
                  class="p-r-10"
                  v-model="optionItem.options_number"
                ></el-input>
                <span>分</span>
              </el-form-item>

              <div class="p-l-20">
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  v-if="questionnaireItem.options_text.length > 1"
                  @click="optionRemove(questionnaireIndex, optionIndex)"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div
              style="color: #fda04d; cursor: pointer; width: 110px"
              class="p-b-20 p-l-20"
              @click="addOption(questionnaireIndex, questionnaireItem.options_text)"
            >
              <i class="el-icon-plus"></i>
              添加选项
            </div>
          </div>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            评价编写
            <button-icon color="origin" type="add" @click="addEvaluate()">添加评价</button-icon>
          </div>
        </div>
        <div class="p-l-20" style="color: #a5a5a5">
          注：填写分数时，符号表示：“小于”=“&gt;”、"大于"=“&gt;”、“小于等于”=“&gt;=”、“大于等于”=“>=”'
        </div>
        <div class="table-content">
          <div
            style="max-width: 600px; padding: 0 20px"
            v-for="(evaluateItem, evaluateIndex) in formData.assess_list"
            :key="evaluateIndex"
          >
            <div class="ps-flex-align-c p-t-20">
              <el-form-item :label="evaluateIndex + 1 + '、'" label-width="30px">
                <span class="p-r-10">分数</span>
                <el-select
                  v-model="evaluateItem.comparison_operator"
                  style="width: 120px"
                  size="small"
                >
                  <el-option label="等于" value="="></el-option>
                  <el-option label="大于" value=">"></el-option>
                  <el-option label="小于" value="<"></el-option>
                  <el-option label="大于等于" value=">="></el-option>
                  <el-option label="小于等于" value="<="></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :prop="'assess_list.' + evaluateIndex + '.assess_number'"
                :rules="{
                  required: true,
                  message: '请输入分数',
                  trigger: 'blur'
                }"
                label-width="30px"
              >
                <el-input
                  style="width: 120px"
                  v-model="evaluateItem.assess_number"
                  placeholder="分数"
                ></el-input>
              </el-form-item>
            </div>

            <el-form-item
              label-width="30px"
              :prop="'assess_list.' + evaluateIndex + '.assess_name'"
              :rules="{
                required: true,
                message: '请输入标题',
                trigger: 'blur'
              }"
            >
              <el-input
                style="width: 250px"
                placeholder="请输入标题"
                type="textarea"
                :autosize="{ minRows: 0, maxRows: 2 }"
                maxlength="32"
                v-model="evaluateItem.assess_name"
              ></el-input>
              <span class="p-l-10">标题</span>
            </el-form-item>
            <el-form-item
              label-width="30px"
              :prop="'assess_list.' + evaluateIndex + '.assess_content'"
              :rules="{
                required: true,
                message: '请输入问题',
                trigger: 'blur'
              }"
            >
              <div class="ps-flex-align-c flex-align-c">
                <el-input
                  type="textarea"
                  placeholder="请输入问题"
                  v-model="evaluateItem.assess_content"
                  :autosize="{ minRows: 4, maxRows: 10 }"
                  maxlength="200"
                  show-word-limit
                  class="p-r-48 p-b-10"
                />
                <div class="p-l-20" v-if="formData.assess_list.length > 1">
                  <el-button
                    type="text"
                    size="small"
                    class="ps-warn-text"
                    @click="evaluateRemove(evaluateIndex)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
      </div>

      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '编辑' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'

export default {
  name: 'SuperAddIngredients',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        name: '',
        content: '',
        topic_list: [
          {
            content: '',
            choice_type: 'single',
            is_required: 'not_required',
            options_text: [
              {
                options_text: '',
                options_number: ''
              }
            ]
          }
        ],
        assess_list: [
          {
            comparison_operator: '=',
            assess_number: '',
            assess_name: '',
            assess_content: ''
          }
        ]
      },
      formRuls: {
        name: [
          {
            required: true,
            message: '请输入测评名称',
            trigger: 'blur'
          }
        ],
        content: [
          {
            required: true,
            message: '请输入测评介绍',
            trigger: 'blur'
          }
        ]
      },
      categoryList: [] // 分类
    }
  },
  created() {
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        this.formData = {
          id: data.id,
          name: data.name,
          content: data.content,
          topic_list: data.topic,
          assess_list: data.assess
        }
        console.log(data)
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    // 设置营养的数据
    // //abcdef
    chooseNumStr(index) {
      let english = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
      return english[index]
    },
    // 添加题目
    addQuestionnaire() {
      this.formData.topic_list.push({
        content: '',
        choice_type: 'single',
        is_required: 'not_required',
        options_text: [
          {
            options_text: '',
            options_number: ''
          }
        ]
      })
    },
    // 删除问卷
    questionnaireRemove(questionnaireIndex) {
      this.formData.topic_list.splice(questionnaireIndex, 1)
    },
    addOption(questionnaireIndex, row) {
      this.formData.topic_list[questionnaireIndex].options_text.push({
        options_text: '',
        options_number: ''
      })
    },
    optionRemove(questionnaireIndex, optionIndex) {
      this.formData.topic_list[questionnaireIndex].options_text.splice(optionIndex, 1)
    },
    // 上下移动
    handleMove(index, moveType, list) {
      if (moveType === 'up') {
        if (index === 0) return
        let isUp = list[index - 1]
        list.splice(index - 1, 1)
        list.splice(index, 0, isUp)
      } else {
        if (index === list.length - 1) return
        let isDown = list[index + 1]
        list.splice(index + 1, 1)
        list.splice(index, 0, isDown)
      }
    },
    // 新增评价
    addEvaluate() {
      this.formData.assess_list.push({
        comparison_operator: '=',
        assess_number: '',
        assess_name: '',
        assess_content: ''
      })
    },
    // 删除评价
    evaluateRemove(evaluateIndex) {
      this.formData.assess_list.splice(evaluateIndex, 1)
    },
    // 添加
    async addModifyHealthyQuestion(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'add') {
        ;[err, res] = await to(this.$apis.apiBackgroundHealthyQuestionAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundHealthyQuestionModifyPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.addModifyHealthyQuestion(this.formData)
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss">
.super-add-ingredients {
  .block-label {
    width: 100%;
    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
}
.el-textarea .el-input__count {
  background-color: transparent !important;
}
</style>
