<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :showFooter="showFooter"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="deviceForm"
      @submit.native.prevent
      status-icon
      ref="deviceForm"
      :rules="deviceFormRules"
      label-width="125px"
      inline
      class="dialog-form"
    >
      <div v-if="type==='add'">
        <el-form-item label="激活码：" prop="activationCode">
          <div class="code-form-item-wrapper">
            <el-input style="width:260px;" v-model="deviceForm.activationCode" disabled></el-input>
            <el-button type="primary" class="code-btn-wrapper" @click="getActivationCode">生成</el-button>
          </div>
        </el-form-item>
        <el-form-item label="设备名：">
          <el-input
            v-model="deviceForm.deviceName"
            placeholder="请输入设备名"
            class="ps-input w-350"
            maxlength="32"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属组织：" prop="organization">
          <organization-select
            class="search-item-w ps-input w-350"
            placeholder="请选择所属组织"
            v-model="deviceForm.organization"
            :isLazy="false"
            :multiple="false"
            :check-strictly="true"
            role="super"
            :append-to-body="true"
            :filterable="false"
            @change="organizationSelectChange"
            >
          </organization-select>
        </el-form-item>
        <el-form-item label="设备类型：" prop="deviceType">
          <el-select
            v-model="deviceForm.deviceType"
            placeholder="请选择设备类型"
            class="ps-select w-350"
            popper-class="ps-popper-select"
            @change="deviceTypeChange"
          >
            <el-option
              v-for="item in deviceTypeList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <div v-if="deviceForm.deviceType==='RLZJ'">
          <el-form-item label="水控模式" prop="shuikong">
            <el-switch v-model="deviceForm.shuikong" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="考勤功能" prop="attendance">
            <el-switch v-model="deviceForm.attendance" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="健康码识别功能" prop="healthyCode">
            <el-switch v-model="deviceForm.code" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="体温检测" prop="temperature">
            <el-switch v-model="deviceForm.temperature" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="门禁功能" prop="control">
            <el-switch v-model="deviceForm.control" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="农行校园设备" prop="schoolDevice">
            <el-switch v-model="deviceForm.schoolDevice" active-color="#ff9b45" inactive-color="#ffcda2" @change="schoolControlChange"></el-switch>
          </el-form-item>
        </div>
        <el-form-item label="设备型号：" prop="deviceModel">
          <el-select
            v-model="deviceForm.deviceModel"
            placeholder="请选择设备型号"
            class="ps-select w-350"
            popper-class="ps-popper-select"
            @change="changeSelectDeviceModel"
          >
            <el-option
              v-for="item in deviceModelList"
              :key="item.key"
              :label="item.key"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <div v-if="deviceForm.deviceModel === 'PS-C1050-1' || deviceForm.deviceModel === 'PS-C1501' || deviceForm.deviceModel === 'PS-C1051' || deviceForm.deviceModel === 'PS-D2D' || deviceForm.deviceModel === 'PS-D2' || deviceForm.deviceModel === 'PS-D2mini'">
          <div v-if="deviceForm.deviceModel === 'PS-C1050-1' || deviceForm.deviceModel === 'PS-C1050'">
            <el-form-item label="传感器版本-秤" prop="balanceSensorVersion">
              <el-radio-group class="ps-radio" v-model="deviceForm.balanceSensorVersion">
                <el-radio label="v1">v1版本</el-radio>
                <el-radio label="v2">v2版本</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="传感器版本-托盘" prop="traySensorVersion">
              <el-radio-group class="ps-radio" v-model="deviceForm.traySensorVersion">
                <el-radio label="v1">v1版本</el-radio>
                <el-radio label="v2">v2版本</el-radio>
                <el-radio label="v3">v3版本</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <el-form-item
            label="离线人脸激活码"
            prop="offlineFaceActivationCode"
            :rules="[
              { required: deviceForm.deviceModel === 'PS-C1051' ? true : false, message: '请输入离线人脸激活码', trigger: ['blur', 'change'] }
            ]"
          >
            <el-input
              v-model="deviceForm.offlineFaceActivationCode"
              placeholder="请输入离线人脸激活码"
              class="ps-input w-350"
              maxlength="16"
            ></el-input>
          </el-form-item>
        </div>

        <div v-if="deviceForm.deviceType === 'QCG'">
          <el-form-item label="格子" required>
            <el-form-item :class="[index>0?'m-t-25':'','cupboard-ceil-form']" v-for="(item,index) in deviceForm.cupboardCeil" :key="'option'+index" :prop="`cupboardCeil[${index}]`" :rules='deviceFormRules.cupboardCeil'>
              <el-input v-model.number="deviceForm.cupboardCeil[index]" placeholder="请输入格子数" class="ps-input"></el-input>
              <img src="@/assets/img/plus.png" alt="" @click="addCupboardCeil()">
              <img src="@/assets/img/reduce.png" alt="" v-if="deviceForm.cupboardCeil.length > 1" @click="delCupboardCeil(index)">
            </el-form-item>
          </el-form-item>
          <el-form-item label="appid：" prop="cupboardAppid">
            <el-input
              v-model="deviceForm.cupboardAppid"
              placeholder="请输入appid"
              class="ps-input w-350"
            ></el-input>
          </el-form-item>
          <el-form-item label="deviceName：" prop="cupboardDeviceName">
            <el-input
              v-model="deviceForm.cupboardDeviceName"
              placeholder="请输入deviceName"
              class="ps-input w-350"
            ></el-input>
          </el-form-item>
          <el-form-item label="deviceSecret：" prop="cupboardDeviceSecret">
            <el-input
              v-model="deviceForm.cupboardDeviceSecret"
              placeholder="请输入deviceSecret"
              class="ps-input w-350"
            ></el-input>
          </el-form-item>
          <el-form-item label="serialport：" prop="cupboardSerialport">
            <el-input
              v-model="deviceForm.cupboardSerialport"
              placeholder="请输入serialport（串口号）"
              class="ps-input w-350"
            ></el-input>
          </el-form-item>
          <el-form-item label="服务器地址：" prop="cupboardUrl">
            <el-input
              v-model="deviceForm.cupboardUrl"
              placeholder="请输入服务器地址"
              class="ps-input w-350"
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item label="SN码：" prop="SNCode">
          <el-input
            v-model="deviceForm.SNCode"
            placeholder="请输入SN码"
            class="ps-input w-350"
          ></el-input>
        </el-form-item>
        <el-form-item label="有效期：" prop="validityDate" v-if="!isAutoSellingCabinet">
          <el-date-picker
            v-model="deviceForm.validityDate"
            type="daterange"
            align="left"
            unlink-panels
            range-separator="至"
            start-placeholder="生效时间"
            end-placeholder="失效时间"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            clearable
            class="ps-poper-picker"
          ></el-date-picker>
        </el-form-item>
      </div>
      <div v-if="type==='detail'" class="detail">
        <el-form-item label="所属消费点："><span class="detail__span">{{deviceInfo.consumer_name}}</span></el-form-item>
        <el-form-item v-if="false" label="设备地址："><span class="detail__span">{{deviceInfo.consumer_name}}</span></el-form-item>
        <el-form-item label="设备名："><span class="detail__span">{{deviceInfo.device_name}}</span></el-form-item>
        <el-form-item label="设备类型："><span class="detail__span">{{deviceInfo.device_type_alias}}</span></el-form-item>
        <el-form-item label="设备型号："><span class="detail__span">{{deviceInfo.device_model_alias}}</span></el-form-item>
        <el-form-item label="SN码："  v-if="deviceInfo.device_type === 'ZDSHG'"><span class="detail__span">{{deviceInfo.serial_no}}</span></el-form-item>
        <div v-if="deviceInfo.device_type === 'QCG'">
          <el-form-item label="deviceName："><span class="detail__span">{{cupboardJson.deviceName}}</span></el-form-item>
          <el-form-item label="appid："><span class="detail__span">{{cupboardJson.appId}}</span></el-form-item>
          <el-form-item label="deviceSecret："><span class="detail__span">{{cupboardJson.deviceSecret}}</span></el-form-item>
          <el-form-item label="串口号："><span class="detail__span">{{cupboardJson.serialport}}</span></el-form-item>
          <el-form-item label="柜子数量："><span class="detail__span">{{cupboardJson.ceil_list}}</span></el-form-item>
        </div>
        <div v-if="deviceInfo.device_type==='RLZJ'">
          <el-form-item label="水控模式:">
            <el-switch v-model="zjJson.shuikong" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="考勤功能:">
            <el-switch v-model="zjJson.attendance" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="健康码识别功能:">
            <el-switch v-model="zjJson.healthyCode" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="体温检测:">
            <el-switch v-model="zjJson.temperature" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="门禁功能:">
            <el-switch v-model="zjJson.control" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="农行校园设备:">
            <el-switch v-model="zjJson.schoolDevice" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
        </div>
        <div v-if="deviceInfo.device_type === 'ZNC' && !deviceInfo.using && (deviceInfo.device_model === 'PS-C1050-1' || deviceInfo.device_model === 'PS-C1050')">
          <el-form-item label="传感器版本-秤" prop="balanceSensorVersion">
            <el-radio-group class="ps-radio" v-model="deviceInfo.balance_sensor_version">
              <el-radio label="v1">v1版本</el-radio>
              <el-radio label="v2">v2版本</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="传感器版本-托盘" prop="traySensorVersion">
            <el-radio-group class="ps-radio" v-model="deviceInfo.tray_sensor_version">
              <el-radio label="v1">v1版本</el-radio>
              <el-radio label="v2">v2版本</el-radio>
              <el-radio label="v3">v3版本</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <div v-if="type==='getlog'">
        <el-form-item label="拉取日期" prop="logDate">
          <el-date-picker
            v-model="deviceForm.logDate"
            type="daterange"
            align="left"
            unlink-panels
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :picker-options="logPickerOptions"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            class="ps-poper-picker"
          ></el-date-picker>
        </el-form-item>
      </div>
      <div v-if="type==='effective' || type==='mul_activate_time'">
        <el-form-item label="有效期：" prop="validityDate">
          <el-date-picker
            v-model="deviceForm.validityDate"
            type="daterange"
            align="left"
            unlink-panels
            range-separator="至"
            start-placeholder="生效时间"
            end-placeholder="失效时间"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            clearable
            class="ps-poper-picker"
          ></el-date-picker>
        </el-form-item>
      </div>
      <div v-if="type==='name'">
        <el-form-item label="设备名：" prop="deviceName">
          <el-input
            v-model="deviceForm.deviceName"
            placeholder="请输入设备名"
            class="ps-input w-250"
            maxlength="32"
          ></el-input>
        </el-form-item>
      </div>
      <div v-if="type==='code'" class="code-box">
        <div class="code">
          <qrcode :value="deviceInfo.device_type==='QCG'?deviceInfo.cupboard_json:deviceInfo.activation_code" :options="{ width: 280 }" :margin="10" alt class="face-img" />
          <div class="code-info-box">
            <p>设备名：{{ deviceInfo.device_name }}</p>
            <p>设备类型：{{ deviceInfo.device_type_alias }}</p>
            <p>所属组织：{{ deviceInfo.consumer_name }}</p>
          </div>
        </div>
      </div>
      <div v-if="type==='weigh'">
        <el-form-item label="请选择所属商户" prop="companyId">
          <company-select
            class="search-item-w ps-select"
            v-model="deviceForm.companyId"
            :clearable="true"
            :filterable="true"
            :options="companyOptions"
            @getselect="changeWeighHandle"
            ></company-select>
        </el-form-item>
        <div class="origin-text">该激活码支持该商户所有组织使用，仅适用激活双屏结算秤/点餐机“称重模块”</div>
      </div>
    </el-form>
    <template slot="tool">
      <div v-if="showFooter" slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          v-if="type==='weigh'"
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          生成并下载
        </el-button>
        <el-button
          v-else
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
    <!-- 下载称重二维码的模板 -->
    <div class="weigh-code-wrapper">
      <div ref="weighCodeRef" class="weigh-code p-t-20">
        <div v-if="selectWeighCompany.name">{{ selectWeighCompany.name }}-称重模块激活码</div>
        <qrcode v-if="weighCode" :value="weighCode" :options="weighOption" :margin="5" alt class="face-img" />
      </div>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import OrganizationSelect from '@/components/OrganizationSelect'
import qrcode from '@chenfengyuan/vue-qrcode'
import CompanySelect from '@/components/CompanySelect'
import html2canvas from 'html2canvas';
import FileSaver from 'file-saver'
import { to } from '@/utils';

export default {
  name: 'trayDialog',
  components: {
    OrganizationSelect,
    qrcode,
    CompanySelect
  },
  props: {
    loading: Boolean,
    isshow: Boolean,
    showFooter: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '600px'
    },
    deviceInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    deviceTypeList: {
      type: Array,
      default() {
        return []
      }
    },
    selectList: {
      type: Array,
      default() {
        return []
      }
    },
    minTime: {
      type: [Number, String],
      default: ''
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    let validatalogDate = (rule, value, callback) => {
      if (!value.length) {
        callback(new Error("请选择日期"));
      } else if (value[1].split('-')[2] - value[0].split('-')[2] > 7) {
        callback(new Error("您所选的日期超过7天，请重新选择"));
      } else {
        callback();
      }
    };
    return {
      isLoading: false,
      pickerOptions: {
        // disabledDate(time) {
        //   console.log(222, this.type)
        //   if (this.type === 'mul_activate_time') {
        //     console.log(333, this.minTime)
        //     return this.minTime < Date.now();
        //   }
        // },
        shortcuts: [{
          text: '一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '一年',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      logPickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      deviceForm: {
        activationCode: '',
        organization: '',
        deviceName: '',
        deviceType: '',
        deviceModel: '',
        SNCode: '',
        validityDate: [],
        logDate: [],
        // 取餐柜参数
        cupboardCeil: [32],
        cupboardAppid: '',
        cupboardDeviceName: '',
        cupboardDeviceSecret: '',
        cupboardSerialport: '',
        cupboardUrl: '',
        // 闸机参数
        shuikong: false,
        attendance: false,
        healthyCode: false,
        temperature: false,
        control: false,
        balanceSensorVersion: 'v1',
        traySensorVersion: 'v1',
        offlineFaceActivationCode: '',
        schoolDevice: false // 农行校园设备
      },
      deviceFormRules: {
        deviceName: [{ required: true, message: '请输入设备名', trigger: 'blur' }],
        organization: [{ required: true, message: '请选择组织', trigger: 'blur' }],
        deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
        deviceModel: [{ required: true, message: '请选择设备型号', trigger: 'change' }],
        logDate: [{ required: true, validator: validatalogDate, trigger: "blur" }],
        validityDate: [{ required: true, message: '请选择有效期', trigger: 'change' }],
        cupboardCeil: [
          { required: true, message: '格子数不能为空' },
          { type: 'number', message: '格子数必须为数字值' }
        ],
        cupboardAppid: [{ required: true, message: '请输入appid', trigger: 'blur' }],
        cupboardDeviceName: [{ required: true, message: '请输入deviceName', trigger: 'blur' }],
        cupboardDeviceSecret: [{ required: true, message: '请输入deviceSecret', trigger: 'blur' }],
        cupboardSerialport: [{ required: true, message: '请输入serialport（串口号）', trigger: 'blur' }],
        cupboardUrl: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
        SNCode: [{ required: false, message: '请输入SN码', trigger: 'blur' }]
      },
      deviceModelList: [],
      cupboardJson: {},
      zjJson: {
        shuikong: false,
        attendance: false,
        healthyCode: false,
        temperature: false,
        control: false,
        schoolDevice: false
      },
      companyOptions: { // 秤商户选择
        label: 'name',
        value: 'company'
      },
      selectWeighCompany: {},
      weighCode: '', // 生成的秤激活码
      weighOption: {
        errorCorrectionLevel: 'H',
        width: 280
      },
      isAutoSellingCabinet: false // 是否自动售卖机
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        if (this.type === 'add') {
          this.getActivationCode()
        }
        this.weighCode = ''
        this.selectWeighCompany = {}
        if (this.type === 'name') {
          this.deviceForm.deviceName = this.deviceInfo.device_name
        }
        if (this.deviceInfo.cupboard_json) {
          this.cupboardJson = JSON.parse(this.deviceInfo.cupboard_json)
        }
        if (this.deviceInfo.zj_json) {
          let json = JSON.parse(this.deviceInfo.zj_json)
          this.zjJson.shuikong = !!json.sk_on
          this.zjJson.attendance = !!json.kq_on
          this.zjJson.healthyCode = !!json.jkm_on
          this.zjJson.temperature = !!json.tw_on
          this.zjJson.control = !!json.mj_on
          this.zjJson.schoolDevice == !! json.abc_school
        }
        if (val) {
          this.deviceForm.validityDate = this.getNextYear()
        }
        if (this.type === 'mul_activate_time') {
          this.pickerOptions.disabledDate = (time) => {
            return this.minTime > time.getTime();
          }
        }
      }
    }
  },
  created() {
    // this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getActivationCode()
    },
    clickConfirmHandle() {
      this.$refs.deviceForm.validate(valid => {
        if (valid) {
          let params
          switch (this.type) {
            case 'add':
              params = {
                activation_code: this.deviceForm.activationCode,
                device_name: this.deviceForm.deviceName,
                organization_id: this.deviceForm.organization,
                device_type: this.deviceForm.deviceType,
                device_model: this.deviceForm.deviceModel,
                start_date: this.deviceForm.validityDate[0],
                end_date: this.deviceForm.validityDate[1]
              }
              if (this.deviceForm.deviceModel === 'PS-C1050-1' || this.deviceForm.deviceModel === 'PS-C1050') {
                params.balance_sensor_version = this.deviceForm.balanceSensorVersion
                params.tray_sensor_version = this.deviceForm.traySensorVersion
              }
              if (this.deviceForm.offlineFaceActivationCode) {
                params.offline_face_activation_code = this.deviceForm.offlineFaceActivationCode
              }
              if (this.deviceForm.deviceType === 'QCG') {
                params.cupboard_json = {
                  appId: this.deviceForm.cupboardAppid,
                  deviceName: this.deviceForm.cupboardDeviceName,
                  deviceSecret: this.deviceForm.cupboardDeviceSecret,
                  serialport: this.deviceForm.cupboardSerialport,
                  url: this.deviceForm.cupboardUrl,
                  ceil_list: this.deviceForm.cupboardCeil,
                  activation_code: this.deviceForm.activationCode
                }
              }
              if (this.deviceForm.deviceType === 'RLZJ') {
                params.zj_json = {
                  sk_on: this.deviceForm.shuikong ? 1 : 0,
                  kq_on: this.deviceForm.attendance ? 1 : 0,
                  jkm_on: this.deviceForm.healthyCode ? 1 : 0,
                  tw_on: this.deviceForm.temperature ? 1 : 0,
                  mj_on: this.deviceForm.control ? 1 : 0,
                  abc_school: this.deviceForm.schoolDevice ? 1 : 0
                }
              }
              if (this.deviceForm.SNCode) {
                params.serial_no = this.deviceForm.SNCode
              }
              this.addDevice(params)
              break;
            case 'detail':
              // if (this.deviceInfo.device_type === 'RLZJ') {
              //   params = {
              //     device_no: this.deviceInfo.device_no,
              //     zj_json: {
              //       sk_on: this.zjJson.shuikong ? 1 : 0,
              //       kq_on: this.zjJson.attendance ? 1 : 0,
              //       jkm_on: this.zjJson.healthyCode ? 1 : 0,
              //       tw_on: this.zjJson.temperature ? 1 : 0,
              //       mj_on: this.zjJson.control ? 1 : 0
              //     }
              //   }
              //   this.modifyDevice(params)
              // }
              switch (this.deviceInfo.device_type) {
                case 'RLZJ':
                  params = {
                    device_no: this.deviceInfo.device_no,
                    zj_json: {
                      sk_on: this.zjJson.shuikong ? 1 : 0,
                      kq_on: this.zjJson.attendance ? 1 : 0,
                      jkm_on: this.zjJson.healthyCode ? 1 : 0,
                      tw_on: this.zjJson.temperature ? 1 : 0,
                      mj_on: this.zjJson.control ? 1 : 0,
                      abc_school: this.zjJson.schoolDevice ? 1 : 0
                    }
                  }
                  this.modifyDevice(params)
                  break;
                case 'ZNC':
                  if (this.deviceInfo.device_model === 'PS-C1050-1' || this.deviceInfo.device_model === 'PS-C1050') {
                    params = {
                      device_no: this.deviceInfo.device_no,
                      balance_sensor_version: this.deviceInfo.balance_sensor_version,
                      tray_sensor_version: this.deviceInfo.tray_sensor_version
                    }
                    this.modifyDevice(params)
                  }
                  this.isshow = false
                  break;
              }
              break;
            case 'name':
              params = {
                device_no: this.deviceInfo.device_no,
                device_name: this.deviceForm.deviceName
              }
              this.modifyDevice(params)
              break;
            case 'effective':
              params = {
                device_no: this.deviceInfo.device_no,
                time_range: {
                  start_date: this.deviceForm.validityDate[0],
                  end_date: this.deviceForm.validityDate[1]
                }
              }
              this.modifyDevice(params)
              break;
            case 'mul_activate_time':
              params = {
                device_nos: this.selectList,
                choices: 6,
                valid_day: {
                  start_time: this.deviceForm.validityDate[0] + ' 00:00:00',
                  end_time: this.deviceForm.validityDate[1] + ' 23:59:59'
                }
              }
              this.modifyMulDevice(params)
              break;
            case 'getlog':
              params = {
                device_no: this.deviceInfo.device_no,
                start_date: this.deviceForm.logDate[0],
                end_date: this.deviceForm.logDate[1]
              }
              this.deviceLogPull(params)
              break;
            case 'weigh':
              params = {
                company_id: this.deviceForm.companyId
              }
              this.addWeighCode(params)
              break;
          }
        } else {
        }
      })
    },
    async addDevice(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceAddPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('新建成功')
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量修改激活码信息
    async modifyMulDevice(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceBatchModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改设备
    async modifyDevice(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 拉取日志
    async deviceLogPull(params) {
      const res = await this.$apis.apiBackgroundAdminDevicePullLogPost(params)
      if (res.code === 0) {
        this.$message.success('拉取日志成功')
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 创建称重模块激活码
    async addWeighCode(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceGenerateActiveCodePost(params)
      if (res.code === 0) {
        this.weighCode = res.data
        this.$message.success(res.msg)
        this.$nextTick(_ => {
          this.downWeighCode()
        })
      } else {
        this.isLoading = false
        this.$message.error(res.msg)
      }
    },
    // 获取选中的公司名称
    changeWeighHandle(e) {
      this.selectWeighCompany = e.item
    },
    // 下载二维码
    downWeighCode() {
      let weighCodeRef = this.$refs.weighCodeRef
      html2canvas(weighCodeRef).then((canvas) => {
        let url = canvas.toDataURL()
        FileSaver.saveAs(url, this.selectWeighCompany.name + '-称重模块激活码.png')
        this.$emit('confirm', 'search')
        // this.confirm()
      });
      this.isLoading = false
    },
    clickCancleHandle() {
      // this.$refs.deviceForm.resetFields()
      this.visible = false
      this.deviceForm.deviceName = ''
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.$refs.deviceForm.resetFields()
      this.isLoading = false
      this.visible = false
      this.deviceForm.deviceName = ''
      this.deviceForm.offlineFaceActivationCode = ''
      // this.$emit('close')
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    },
    // 生成激活码
    async getActivationCode() {
      const res = await this.$apis.apiBackgroundAdminDeviceGenerateActivationPost()
      if (res.code === 0) {
        this.deviceForm.activationCode = res.data.activation_code
      } else {
        this.$message.error(res.msg)
      }
    },
    deviceTypeChange(e) {
      console.log("deviceTypeChange", e);
      // 如果是自动饮料售卖机，SN码为必填
      this.deviceFormRules.SNCode[0].required = e && (e === 'ZDSHG' || e === 'CJY')
      if (this.$refs.deviceForm) {
        console.log("this.$refs.deviceForm", this.$refs.deviceForm);
        this.$refs.deviceForm.clearValidate()
      }
      this.isAutoSellingCabinet = e && e === 'ZDSHG'
      this.deviceForm.deviceModel = ''
      this.getDeviceModel()
    },
    // 获取设备型号
    async getDeviceModel() {
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceModelPost({
        device_type: this.deviceForm.deviceType
      })
      if (res.code === 0) {
        this.deviceModelList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    getNextYear() {
      let timeStamp, startDate
      if (this.type === 'mul_activate_time') {
        console.log(11112332323)
        timeStamp = this.minTime + 86400000 * (365)
        startDate = [
          new Date(this.minTime).getFullYear(),
          (new Date(this.minTime).getMonth() + 1).toString().padStart(2, '0'),
          new Date(this.minTime)
            .getDate()
            .toString()
            .padStart(2, '0')
        ].join('-')
      } else {
        timeStamp = new Date().getTime() + 86400000 * (365)
        startDate = [
          new Date().getFullYear(),
          (new Date().getMonth() + 1).toString().padStart(2, '0'),
          new Date()
            .getDate()
            .toString()
            .padStart(2, '0')
        ].join('-')
      }

      let endDate = [
        new Date(timeStamp).getFullYear(),
        (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
        new Date(timeStamp)
          .getDate()
          .toString()
          .padStart(2, '0')
      ].join('-')
      return [startDate, endDate]
    },
    addCupboardCeil() {
      this.deviceForm.cupboardCeil.push('')
    },
    delCupboardCeil(index) {
      this.deviceForm.cupboardCeil.splice(index, 1);
    },
    changeSelectDeviceModel() {
      this.$nextTick(() => {
        this.$refs.deviceForm.clearValidate(['offlineFaceActivationCode']);
      })
    },
    // 检测该设备是否激活
    ifActivate() {
      if (this.deviceInfo.activation_status) {
        return true
      } else {
        return false
      }
    },
    // 农行校园设备开启
    schoolControlChange(value) {
      console.log("schoolControlChange", value);
      // 开启
      if (value) {
        if (!this.deviceForm.organization) {
          this.$message.error('请先选择所属组织!')
          this.$set(this.deviceForm, 'schoolDevice', false)
        }
      }
    },
    // 组织修改监听
    organizationSelectChange(data) {
      var id = data ? data.id : ''
      console.log("organizationSelectChange", data, id);
      if (id) {
        this.getSchoolInfoBYOrgId(id)
      }
    },
    // 根据组织ID获取是否有农行校园配置
    async getSchoolInfoBYOrgId(id) {
      const [err, res] = await to(this.$apis.xxx({ id: id }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        console.log("getSchoolInfoBYOrgId", data);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.code-form-item-wrapper {
  display: flex;
  .code-btn-wrapper {
    margin-left: 20px;
  }
}
.m-t-25{
  margin-top: 25px!important;
}
.cupboard-ceil-form{
  display: block!important;
  .el-form-item__content{
    display: flex;
    width: 350px;
    img{
      margin: 5px 0 5px 10px;
    }
  }
}
.detail{
  &__span{
    width: 150px;
    display: inline-block;
  }
}
.code-box{
  text-align: center;
  .code{
    width: 280px;
    margin: 0 auto;
  }
  .code-info-box{
    padding: 0 40px;
    margin: 0;
    text-align: left;
    p{
      margin: 0 0 5px;
      line-height: 1.2;
      font-weight: bold;
    }
  }
}
.weigh-code-wrapper{
  position: absolute;
  z-index: -1;
  opacity: 0;
  .weigh-code{
    display: inline-block;
    text-align: center;
  }
}
</style>
