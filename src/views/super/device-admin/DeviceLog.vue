<template>
  <div class="ActiveCodeAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="del" @click="deleteHandler('multi')">批量禁用</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="create_time" label="操作时间" align="center" width="120"></el-table-column>
          <el-table-column prop="device_type" label="设备类型" align="center">
            <template slot-scope="scope">
              {{ shwoDeviceType(scope.row.device_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="device_number" label="设备号" align="center" width="90"></el-table-column>
          <el-table-column prop="device_name" label="设备名" align="center"></el-table-column>
          <el-table-column prop="consumer_name" label="所属组织" align="center" show-overflow-tooltip width="140"></el-table-column>
          <!-- <el-table-column prop="serial_no" label="所属食堂" align="center"></el-table-column> -->
          <!-- <el-table-column prop="consumer_name" label="所属档口" align="center"></el-table-column> -->
          <el-table-column prop="account_name" label="操作人" align="center" width="150"></el-table-column>
          <el-table-column prop="device_mac" label="设备地址" align="center" width="150"></el-table-column>
          <el-table-column prop="source_alias" label="日志来源" align="center" width="150"></el-table-column>

          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                :disabled="!scope.row.url"
                @click="downloadHandler(scope.row)"
                >下载日志</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="deleteHandler('single', scope.row)"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, getDateRang, to } from '@/utils'
import FileSaver from "file-saver";

export default {
  name: 'DeviceLog',
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        select_date: {
          type: 'daterange',
          label: '',
          value: getDateRang(7, { format: '{y}-{m}-{d}' }),
          clearable: false
        },
        org_no: {
          type: 'organizationSelect',
          multiple: false,
          checkStrictly: true,
          isLazy: true,
          label: '消费点',
          value: '',
          placeholder: '请选择消费点',
          role: 'super'
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        source: {
          type: 'select',
          label: '日志来源',
          value: '',
          placeholder: '请选择',
          dataList: [{
            label: '设备上传',
            value: 0
          }, {
            label: '后台拉取',
            value: 1
          }]
        },
        device_number: {
          type: 'input',
          label: '设备号',
          value: '',
          placeholder: '请输入'
        }
      },
      deviceInfo: {},
      selectListId: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDeviceList()
      this.getDeviceType()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.deviceDialogVisible = false
      this.getDeviceList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取日志列表
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceLogListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.tableData = [{}]
        this.totalCount = res.data.count
      } else {
        this.tableData = [{}]
        this.$message.error(res.msg)
      }
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    shwoDeviceType(type) {
      let name = ''
      let list = this.searchFormSetting.device_type.dataList
      for (let index = 0; index < list.length; index++) {
        if (list[index].key === type) {
          name = list[index].name
          break
        }
      }
      list = null
      return name
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      val.map(item => { this.selectListId.push(item.log_no) })
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDeviceList()
    },
    downloadHandler(row) {
      if (!row.url) return this.$message.error('获取日志文件失败！')
      const loading = this.$loading({
        lock: true,
        text: '正在获取数据中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.9)'
      });
      let splitList = row.url.split('/')
      let fileName = splitList[splitList.length - 1]
      FileSaver.saveAs(row.url, fileName)
      loading.close();
    },
    deleteHandler(type, row) {
      let deleteIds = []
      if (type === 'multi') {
        deleteIds = this.selectListId
      } else {
        deleteIds = [row.log_no]
      }
      if (!deleteIds.length) {
        this.$message.error('请选择要删除的日志！')
        return
      }
      this.$confirm(`确定删除当前选中的日志吗？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundAdminDeviceDelLogPost({
              log_nos: deleteIds
            }))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getDeviceList()
              done()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
