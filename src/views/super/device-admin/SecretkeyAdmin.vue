<template>
  <div class="ActiveCodeAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openSecretKeyDialog('add')">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="create_time" label="创建时间" align="center" width="120"></el-table-column>
          <el-table-column prop="secret" label="秘钥" align="center"></el-table-column>
          <el-table-column prop="org_name" label="所属组织" align="center" show-overflow-tooltip width="140"></el-table-column>
          <el-table-column prop="secret_status_alias" label="状态" align="center" width="150"></el-table-column>
          <el-table-column prop="disclaimer_status_alias" label="免责说明" align="center" width="150"></el-table-column>
          <el-table-column prop="consent_time" label="协议同意时间" align="center" width="150"></el-table-column>

          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-red"
                :disabled="scope.row.secret_status"
                @click="deleteHandler('single', scope.row)"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 添加弹窗 start -->
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="380px"
        top="20vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
        @closed="dialogHandleClose"
      >
        <el-form
          ref="dialogFormRef"
          v-loading="dialogLoading"
          :rules="dialogFormDataRuls"
          :model="dialogFormData"
          class="dialog-form"
          label-width="90px"
          size="small"
        >
          <el-form-item label="所属组织" prop="org_no">
            <organization-select
              class="search-item-w ps-input w-250"
              placeholder="请选择所属组织"
              :isLazy="false"
              :multiple="false"
              role="super"
              check-strictly
              v-model="dialogFormData.org_no"
              :append-to-body="true"
              >
            </organization-select>
          </el-form-item>
          <el-form-item label="生成数量" prop="count">
            <el-input class="ps-input" v-model="dialogFormData.count" placeholder="请输入数量"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button  size="small" class="ps-cancel-btn" :disabled="dialogLoading" @click="dialogVisible = false">
            {{ $t('dialog.cancel_btn') }}
          </el-button>
          <el-button
            class="ps-origin-btn"
            :disabled="dialogLoading"
            type="primary"
            size="small"
            @click="submitDialogHandler('dialogFormRef')"
          >
            {{ $t('dialog.add_btn') }}
          </el-button>
        </span>
      </el-dialog>
      <!-- 弹窗 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import OrganizationSelect from '@/components/OrganizationSelect'

export default {
  name: 'DeviceLog',
  props: {},
  // mixins: [activatedLoadData],
  components: {
    OrganizationSelect
  },
  data() {
    let validateCount = (rule, value, callback) => {
      let reg = /(^[0-9]{1,2}$)/
      // eslint-disable-next-line eqeqeq
      if (!reg.test(value) || (value == 0)) {
        callback(new Error('格式不正确'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        secret: {
          type: 'input',
          label: '秘钥',
          value: '',
          placeholder: '请输入秘钥',
          maxlength: 8
        },
        org_name: {
          type: 'input',
          label: '所属组织',
          value: '',
          placeholder: '请输入所属组织',
          maxlength: 20
        },
        // device_name: {
        //   type: 'input',
        //   label: '设备名',
        //   value: '',
        //   placeholder: '请输入设备名'
        // },
        secret_status: {
          type: 'select',
          label: '状态',
          value: '',
          placeholder: '请选择状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '已使用',
            value: true
          }, {
            label: '未使用',
            value: false
          }]
        },
        disclaimer_status: {
          type: 'select',
          label: '免责说明',
          value: '',
          placeholder: '请选择免责说明',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '已同意',
            value: true
          }, {
            label: '未同意',
            value: false
          }]
        }
      },
      dialogTitle: '创建密钥',
      dialogVisible: false,
      dialogLoading: false,
      dialogFormData: {
        org_no: '',
        count: 1
      },
      dialogFormDataRuls: {
        org_no: [
          { required: true, message: '所属组织不能为空', trigger: "change" }
        ],
        count: [
          { required: true, validator: validateCount, trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSecretKeyList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getSecretKeyList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表
    async getSecretKeyList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDisclaimerListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.result
        this.totalCount = res.data.count
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSecretKeyList()
    },
    openSecretKeyDialog() {
      this.dialogVisible = true
    },
    dialogHandleClose() {
      // this.$refs.dialogFormRef.clearValidate()
      this.$refs.dialogFormRef.resetFields()
    },
    submitDialogHandler(refType) {
      this.$refs[refType].validate(valid => {
        if (valid) {
          if (this.dialogLoading) return;
          this.addSecretKeyHandle()
        }
      })
    },
    async addSecretKeyHandle() {
      this.dialogLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminDisclaimerAddPost({
        org_no: this.dialogFormData.org_no,
        count: this.dialogFormData.count
      }))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.dialogVisible = false
        this.searchHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHandler(type, row) {
      let deleteIds = []
      // if (type === 'multi') {
      //   deleteIds = this.selectListId
      // } else {
      deleteIds = row.disclaimer_no
      // }
      if (!deleteIds) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm(`确定删除当前选中的数据吗？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundAdminDisclaimerDeletePost({
              disclaimer_no: deleteIds
            }))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.searchHandle()
              done()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
