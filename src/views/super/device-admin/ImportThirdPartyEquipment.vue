<template>
  <div>
    <refresh-tool :title="title" :show-refresh="false" />
    <import-page
      class="importPage"
      :initial="initial"
      :url="url"
      :header-len="headerLen"
      :template-url="templateUrl"
    />
  </div>
</template>

<script>
export default {
  name: 'ImportIngredients',
  data() {
    return {
      type: 'import',
      title: '批量第三方设备列表',
      headerLen: 1,
      initial: true,
      url: 'apiBackgroundDeviceAdminThirdDeviceThirdDeviceBatchAddPost',
      templateUrl: '/api/temporary/template_excel/third_device_import.xlsx'
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss">
.importPage {
  // max-width: 1160px;
}
</style>
