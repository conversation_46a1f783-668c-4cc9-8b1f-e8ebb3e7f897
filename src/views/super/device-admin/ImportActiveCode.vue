<template>
  <div>
    <refresh-tool :title="title" :show-refresh="false" />
    <import-page class="importPage" :initial="initial" :url="url" :header-len="headerLen" :template-url="templateUrl" params-key="url" :result-index="-1" :border="true" />
  </div>
</template>

<script>
export default {
  name: 'ImportIngredients',
  data() {
    return {
      type: 'import',
      title: '导入',
      headerLen: 1,
      initial: true,
      url: 'apiBackgroundAdminDeviceBulkCreateActivationCodePost',
      templateUrl: '/api/temporary/template_excel/bulk_create_activation_code.xlsx'
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
  },
  methods: {}
}
</script>

<style lang="scss">
.importPage {
  // max-width: 1160px;
}
</style>
