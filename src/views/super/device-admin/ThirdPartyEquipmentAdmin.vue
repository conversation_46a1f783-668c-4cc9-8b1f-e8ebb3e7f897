<template>
  <div class="ThirdPartyEquipmentAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('add')">新建</button-icon>
          <button-icon color="plain" type="Import" @click="importHandler('import')">
            批量导入
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column
            prop="organization_name"
            label="所属组织"
            align="center"
            show-overflow-tooltip
            width="140"
          ></el-table-column>
          <el-table-column prop="device_number" label="设备编号" align="center"></el-table-column>
          <el-table-column prop="device_type_alias" label="设备类型" align="center"></el-table-column>
          <el-table-column prop="device_model" label="设备型号" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialog('modify', scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="deleteHandler('single', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <third-party-equipment-dialog
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :equipment-info="equipmentInfo"
      @confirm="searchHandle"
    />
  </div>
</template>

<script>
import ThirdPartyEquipmentDialog from './components/ThirdPartyEquipmentDialog.vue'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
export default {
  name: 'ThirdPartyEquipmentAdmin',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        organization: {
          type: 'organizationSelect',
          multiple: false,
          checkStrictly: true,
          isLazy: true,
          label: '所属组织',
          value: '',
          placeholder: '请选择消费点',
          role: 'super'
        },
        device_number: {
          type: 'input',
          label: '设备编号',
          value: '',
          maxlength: '20',
          placeholder: '请输入'
        }
      },
      dialogTitle: '',
      dialogVisible: false,
      dialogType: '',
      equipmentInfo: {}
    }
  },
  created() {
    this.initLoad()
  },
  components: {
    ThirdPartyEquipmentDialog
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getThirdDeviceList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.dialogVisible = false
      this.getThirdDeviceList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 第三方设备相关信息列表
    async getThirdDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceAdminThirdDeviceListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getThirdDeviceList()
    },
    deleteHandler(type, row) {
      let deleteIds = [row.id]
      // if (type === 'multi') {
      //   deleteIds = this.selectListId
      // } else {
      //   deleteIds = [row.log_no]
      // }
      // if (!deleteIds.length) {
      //   this.$message.error('请选择要删除的日志！')
      //   return
      // }
      this.$confirm(`确定删除当前选中的设备吗？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundDeviceAdminThirdDeviceDeletePost({
                ids: deleteIds
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getThirdDeviceList()
              done()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    importHandler() {
      this.$router.push({
        name: 'SuperImportThirdPartyEquipment'
      })
    },
    openDialog(type, data) {
      this.equipmentInfo = data
      this.dialogType = type
      switch (type) {
        case 'add':
          this.dialogTitle = '新增第三方设备'
          break
        case 'modify':
          this.dialogTitle = '编辑第三方设备'
          break
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped></style>
