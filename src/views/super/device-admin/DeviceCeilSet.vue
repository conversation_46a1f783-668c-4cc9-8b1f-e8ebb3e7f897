<template>
  <div class="DeviceCeilSet container-wrapper">
    <div class="table-wrapper" style="margin-top: 0px;margin-bottom: 50px;">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div style="display: flex;align-items: center;">
          <div class="table-title">取餐柜设置</div>
        </div>
        <div style="padding-right:20px;">
          <div>当前设备：{{deviceName}}</div>
        </div>
      </div>
      <div class="content-wrapper" :key="contentKey" v-loading="isLoading">
        <div>餐格大小：
          <el-button type="primary" @click="mulOperation('big')">大餐格</el-button>
          <el-button type="warning" @click="mulOperation('little')">小餐格</el-button>
        </div>
        <div class="title">主柜：3，5号柜为操作屏</div>
        <div class="ceil-list">
          <div :class="['ceil-item', ceilColor(item.type)]" v-for="(item, index) in mainCupboard" :key="index" @click="selectCeil('main', index)">
            {{item.num}}
            <div :class="[item.isSelect?'select-mask':'un-select']">已选中: {{item.num}}</div>
          </div>
        </div>
        <div class="title">副柜：</div>
        <div>
          <div class="ceil-list" v-for="(assistantList, listIndex) in assistantCupboard" :key="listIndex">
            <div :class="['ceil-item', ceilColor(assistantItem.type)]" v-for="(assistantItem, itemIndex) in assistantList" :key="itemIndex" @click="selectCeil('assistant', listIndex, itemIndex)">
              {{assistantItem.num}}
              <div :class="[assistantItem.isSelect?'select-mask':'un-select']">已选中: {{assistantItem.num}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'DeviceCeilSet',
  data() {
    return {
      isLoading: false,
      deviceNo: '',
      deviceName: '',
      ceilList: [],
      mainCupboard: [],
      assistantCupboard: [],
      selectList: [],
      contentKey: 0
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.deviceNo = this.$route.query.device_no
      this.deviceName = this.$route.query.device_name
      this.ceilList = JSON.parse(this.$route.query.ceil_list)
      console.log('ceilList', this.ceilList)
      this.getCupboardCeil()
    },
    // 刷新页面
    refreshHandle() {
      this.initLoad()
    },
    // 获取取餐柜格子信息
    async getCupboardCeil() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceGetCupboardCeilTypePost({
        device_no: this.deviceNo
      })
      this.isLoading = false
      if (res.code === 0) {
        this.mainCupboard = []
        this.assistantCupboard = []
        let total = 0
        let item = 1
        for (let i = 0; i < this.ceilList.length; i++) {
          if (i) {
            this.assistantCupboard[i - 1] = []
          }
          total = total + Number(this.ceilList[i])
          for (item; item <= total; item++) {
            let type
            if (res.data.little_ceil_list) {
              res.data.little_ceil_list.map(little => {
                if (little === item) {
                  type = 'little'
                }
              })
            }
            if (res.data.big_ceil_list) {
              res.data.big_ceil_list.map(big => {
                if (big === item) {
                  type = 'big'
                }
              })
            }
            if (item === 3 || item === 5) {
              type = 'screen'
            }
            if (i === 0) {
              this.mainCupboard.push({
                num: item,
                type,
                isSelect: false
              })
            } else {
              this.assistantCupboard[i - 1].push({
                num: item,
                type,
                isSelect: false
              })
            }
          }
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格子颜色
    ceilColor(type) {
      if (type === 'little') {
        return 'little'
      } else if (type === 'big') {
        return 'big'
      } else if (type === 'screen') {
        return 'screen'
      }
    },
    // 格子选中与取消选中
    selectCeil(type, index, itemIndex) {
      if (type === 'main') {
        if (this.mainCupboard[index].type === 'screen') {
          return this.$message.error("3，5号柜为操作屏,不可进行设置")
        }
        this.mainCupboard[index].isSelect = !this.mainCupboard[index].isSelect
        if (this.mainCupboard[index].isSelect) {
          this.selectList.push(this.mainCupboard[index].num)
        } else {
          this.selectList.splice(this.selectList.indexOf(this.mainCupboard[index].num), 1)
        }
      } else if (type === 'assistant') {
        this.assistantCupboard[index][itemIndex].isSelect = !this.assistantCupboard[index][itemIndex].isSelect
        if (this.assistantCupboard[index][itemIndex].isSelect) {
          this.selectList.push(this.assistantCupboard[index][itemIndex].num)
        } else {
          this.selectList.splice(this.selectList.indexOf(this.assistantCupboard[index][itemIndex].num), 1)
        }
      }
      this.contentKey = Math.random()
    },
    mulOperation(type) {
      if (!this.selectList.length) {
        return this.$message.error('请先选择餐格！（点击餐格进行选择）')
      }
      let content = ''
      // 处理数据，所有餐格数据都要传过去
      let bigCeilList = []
      let littleCeilList = []
      switch (type) {
        case "big":
          content = '确定将选中餐格设置为大餐格吗？'
          bigCeilList = bigCeilList.concat(this.selectList)
          break;
        case "little":
          content = '确定将选中餐格设置为小餐格吗？'
          littleCeilList = littleCeilList.concat(this.selectList)
          break;
      }
      this.mainCupboard.map(item => {
        if (this.selectList.indexOf(item.num) === -1) {
          if (item.type === 'big') {
            bigCeilList.push(item.num)
          } else if (item.type === 'little') {
            littleCeilList.push(item.num)
          }
        }
      })
      this.assistantCupboard.map(assistantItem => {
        assistantItem.map(item => {
          if (this.selectList.indexOf(item.num) === -1) {
            if (item.type === 'big') {
              bigCeilList.push(item.num)
            } else if (item.type === 'little') {
              littleCeilList.push(item.num)
            }
          }
        })
      })
      this.$confirm(`${content}`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              device_no: this.deviceNo,
              big_ceil_list: bigCeilList,
              little_ceil_list: littleCeilList
            }
            this.setCupboardCeilType(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    async setCupboardCeilType(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceSetCupboardCeilTypePost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('设置成功')
        this.getCupboardCeil()
        this.selectList = []
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  .DeviceCeilSet{
    margin-top: 30px;
    .content-wrapper{
      padding: 0 20px 30px;
    }
    .title{
      padding: 20px 0;
    }
    .ceil-list{
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20px;
      .ceil-item{
        width: 80px;
        line-height: 35px;
        border-radius: 5px;
        text-align: center;
        margin: 5px 10px;
        cursor: pointer;
        position: relative;
        .select-mask{
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          font-size: 14px;
          border-radius: 5px;
          background-color: #7e7e7e7e;
          color: #fff;
        }
        .un-select{
          display: none;
        }
      }
      .little{
        background-color: #E6A23C;
        color: #ffffff;
      }
      .big{
        background-color: #409EFF;
        color: #ffffff;
      }
      .screen{
        background-color: #bfbfbf;
      }
    }
  }
</style>
