<template>
  <el-container class="ps-container">
    <!-- 菜单栏 -->
    <el-aside :width="collapse?'64px':'230px'" class="ps-aside sidebar-container">
      <sidebar :is-collapse="collapse" />
    </el-aside>
    <!-- 主体 -->
    <el-container class="main-container">
      <!-- 导航栏 -->
      <el-header class="nav-container" height="110px">
        <nav-tabs />
      </el-header>
      <!-- 正文 -->
      <el-main ref="mainRef">
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="cachedViews" :max="maxCachedViews">
            <!-- 添加key会导致热更新失效 -->
            <router-view v-if="env && isRouterAlive"/>
            <router-view v-else />
          </keep-alive>
        </transition>
        <el-backtop target=".el-main" :bottom="60"></el-backtop>
        <!-- 备案号 -->
        <!-- <filings :class="{'fixed-record': fixedRecord}" /> -->
      </el-main>
    </el-container>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogSync"
      width="350px"
      top="25vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="dialogType === 'identityChange' ? false : true"
      class="tips"
    >
      <div class="dialog-content" v-if="dialogType === 'wechatBind'">
        <div style="text-align: center;margin-bottom:20px;">
          <img src="@/assets/img/binding-wechat.png" alt="" />
        </div>
        <div>
          绑定微信后，可进行微信扫码登录，后期可在微信开放更多功能，请前往设置进行微信绑定。
        </div>
      </div>
      <div class="dialog-content" v-if="dialogType === 'identityChange'" v-loading="dialogLoading">
        <el-select
          v-model="org"
          placeholder="请下拉选择"
          class="ps-select"
          popper-class="ps-popper-select-theme"
          style="width:100%;"
        >
          <el-option
            v-for="item in orgsList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="dialogType === 'wechatBind'" class="goto-btn" @click="gotoSetting()">
          立即前往
        </el-button>
        <el-button v-if="dialogType === 'identityChange'" :disabled="dialogLoading" class="goto-btn" @click="confirmOrg()">
          确定
        </el-button>
      </span>
    </el-dialog>
    <double-factor v-if="userInfo.is_double_factor" />
    <jump-change-pwd v-if="showJumpDialog" />
    <!--阅读协议-->
    <read-agreement refs="readAgreemeet" @readAgreement="clickReadAgreement"></read-agreement>
    <!-- 服务到期弹窗 -->
    <dialog-message
      width="470px"
      title="提示"
      :show.sync="serviceDialogShow"
      customClass="expire-dialog"
      :showFooter="false"
      @close="handleClose"
    >
      <template class="expire-dialog-content">
        <span>{{ msg(0) }}：<span style="text-decoration: underline; color: blue;">{{ msg(1) }}</span>）</span>
      </template>
      <template #tool>
        <div class="dialog-footer" style="margin-top: 20px; text-align: right;" >
          <el-button class="ps-cancel-btn renew-btn" @click="goUpgrade">续费</el-button>
          <el-button class="ps-btn" @click="handleClose">暂不处理</el-button>
        </div>
      </template>
    </dialog-message>

    <dialog-message
      width="470px"
      title="问卷调查"
      :show.sync="surveyDialogVisible"
      :showFooter="false"
      @close="handleClose"
    >
      <template>
        <div>
          <div>
            <span>你有一份</span>
            <span class="f-w-700">{{ surveyName }}</span>
          </div>
          <div>如无法及时填写，可通过右上角通知（小喇叭）找到我~</div>
        </div>
      </template>
      <template #tool>
        <div class="dialog-footer" style="margin-top: 20px; text-align: right;" >
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="gotoPath">去填写</el-button>
        </div>
      </template>
    </dialog-message>
  </el-container>
</template>

<script>
import sidebar from '@/layout/components/Sidebar/index.vue'
import NavTabs from '@/layout/components/NavTabs/index.vue'
import Filings from "@/components/Filings"
import DoubleFactor from './components/DoubleFactor/index.vue'
import JumpChangePwd from './components/JumpChangePwd'
import { setSessionStorage, getSessionStorage, to } from '@/utils'
import { mapGetters } from "vuex"
import ReadAgreement from './components/ReadAgreement/index.vue'

export default {
  components: {
    sidebar,
    NavTabs,
    // eslint-disable-next-line vue/no-unused-components
    Filings,
    DoubleFactor,
    JumpChangePwd,
    ReadAgreement
  },
  provide () {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      collapse: false,
      dialogSync: false,
      organizationChange: false,
      dialogTitle: '',
      dialogType: '',
      orgsList: '',
      org: '',
      userInfo: '',
      fixedRecord: false,
      env: process.env.NODE_ENV === 'development',
      showJumpDialog: false,
      isRouterAlive: false, // 刷新当前页面
      serviceDialogShow: false,
      dialogLoading: false, // 弹窗loading
      surveyDialogVisible: false,
      surveyName: ''
    }
  },
  computed: {
    ...mapGetters([
      'stopServiceMsg'
    ]),
    cachedViews() {
      return this.$store.state.navTabs.cachedViews
    },
    key() {
      return this.$route.path
    },
    maxCachedViews() {
      return this.$route.maxCachedViews
    },
    msg() {
      return d => {
        let text = this.stopServiceMsg.msg ? this.stopServiceMsg.msg.split('：') : ''
        if (d === 0) {
          return text[0]
        } else {
          return text ? text[1].slice(0, -1) : ''
        }
      }
    }
  },
  watch: {},
  async created() {
    this.showJumpDialog = getSessionStorage('ISEXPIRECHANGEPWD') === '1'
    await this.$sleep(30) // 为了弹窗有先后，给个睡眠时间
    this.userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO')))
    // this.userInfo.is_double_factor = true
    this.isOpenDialog()
  },
  mounted() {
    if (this.stopServiceMsg.code && this.stopServiceMsg.code === 5 && !this.dialogSync) {
      this.serviceDialogShow = true
    }
  },
  methods: {
    // 刷新页面
    reload() {
      this.isRouterAlive = false
      this.$nextTick(_ => {
        this.isRouterAlive = true
      })
    },
    isOpenDialog() {
      this.orgsList = this.userInfo.organizationList
      // for (let key in this.userInfo.orgs) {
      //   this.orgsList.push({
      //     id: key,
      //     name: this.userInfo.orgs[key]
      //   })
      // }
      if (this.orgsList.length > 1 && !sessionStorage.getItem('isChoiceOrg')) {
        this.dialogSync = true
        this.dialogTitle = '选择登录组织'
        this.dialogType = 'identityChange'
      } else if (this.userInfo.has_first_login) {
        this.dialogSync = true
        this.dialogTitle = '温馨提示'
        this.dialogType = 'wechatBind'
      } else {
        var id = this.orgsList[0] ? this.orgsList[0].id : ''
        this.getAbcAgreement(id)
      }
    },
    async confirmOrg() {
      if (this.org === '') {
        return this.$message.error('请选择组织')
      }
      if (this.dialogLoading) return
      this.dialogLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundBindOrgPost({
        org_no: this.org
      }))
      this.dialogLoading = true
      if (err) {
        this.$message.error(err.message)
      }
      if (res.code === 0) {
        sessionStorage.setItem('isChoiceOrg', 1)
        this.$store.dispatch('user/setOrganization', Number(this.org))
        let obj = {
          ...res.data,
          orgs_level: this.userInfo.orgs_level
        }
        this.$store.dispatch('user/setUserInfo', obj)
        // 更新路由
        const permissions = await this.$store.dispatch('user/getPermissionList', { key: '' })
        await this.$store.dispatch('permission/changeRoutes', permissions)

        // 刷新当前页面的数据
        this.reload()

        if (this.userInfo.has_first_login) {
          this.dialogSync = true
          this.dialogTitle = '温馨提示'
          this.dialogType = 'wechatBind'
        } else {
          this.dialogSync = false
        }
        this.getSurveyMessage(this.org)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取问卷消息
    async getSurveyMessage(id) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundMessagesMessagesGetMsgListPost({
        page: 1,
        page_size: 9999,
        message_type: 1
      }))
      if (err) return
      if (res.code === 0) {
        let obj = res.data.results.find(item => {
          return item.read_flag !== '已读'
        })
        console.log('obj', obj)
        if (Object.keys(obj).length) {
          this.surveyDialogVisible = true
          this.surveyName = obj.title
        }
        this.getAbcAgreement(id)
      }
    },
    gotoSetting() {
      this.$router.push({
        name: 'AccountSetting'
      })
      this.dialogSync = false
    },
    //  同意阅读协议
    clickReadAgreement() {
      this.isOpenDialog()
    },
    // 获取协议
    async getAbcAgreement(id) {
      console.log("getAbcAgreement", id);
      var params = {
        organization_id: id
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationAccountGetUserAbcAgreementPost(params))
      console.log("getAbcAgreement", err, res);
      if (err) {
        return
      }
      if (res && res.code === 0) {
        console.log("getAbcAgreement", res);
        var data = res.data || {}
        if (data && Reflect.has(data, "is_show")) {
          var isShow = data.is_show || false
          // 显示弹窗
          setSessionStorage("showAgreement", isShow)
          this.$store.dispatch('user/setAgreementInfo', data)
        }
      }
    },
    handleClose() {
      this.serviceDialogShow = false
      this.surveyDialogVisible = false
    },
    goUpgrade() {
      this.handleClose()
      this.$router.push({
        path: '/upgrade/service'
      })
    },
    gotoPath() {
      this.surveyDialogVisible = false
      this.$router.push({
        name: 'MerchantNoticeList'
      })
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/index.scss';
@import '~@/styles/mixin.scss';
.ps-container {
  height: 100%;
  // border: 1px solid #eee;
  .el-header {
    padding: 0;
    background-color: $headerBg;
    color: #333;
  }
  .el-aside {
    color: #333;
  }
  .ps-aside {
    background-color: rgb(238, 241, 246);
  }
}
.tips {
  .goto-btn {
    width: 50%;
    display: block;
    margin: auto;
    color: #ffffff;
    background-blend-mode: normal, normal;
    box-shadow: 0px 5px 7px 0px rgba(255, 155, 69, 0.5);
    border-radius: 4px;
    border: none;
    @include btn_color_linear($btn-origin-linear);
    &:hover {
      opacity: 0.8;
      color: #ffffff !important;
    }
    &:active {
      opacity: 0.9;
      color: #ffffff !important;
    }
  }
}
</style>
