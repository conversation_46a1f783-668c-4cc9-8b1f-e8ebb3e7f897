<template>
  <div id="nav-wrapper">
    <tab-header />
    <tabs-view />
  </div>
</template>

<script>
import tabHeader from './tabHeader.vue'
import tabsView from './tabsView.vue'
export default {
  name: 'NavTabs',
  components: {
    tabHeader,
    tabsView
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {},
  methods: {
  }
}
</script>

<style lang="scss">
#nav-wrapper{
}
</style>
