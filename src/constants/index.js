export const FOOD_TYPE = [
  {
    name: '菜品 / 商品价格',
    value: 1
  },
  {
    name: '称重价格',
    value: 2
  },
  {
    name: '菜品 / 商品价格 + 称重价格',
    value: 3
  }
]

export const NUTRITION_KEY = [
  { name: '能量', key: 'energy_kcal', unit: 'Kcal' },
  { name: '脂肪', key: 'axunge', unit: 'g' },
  { name: '蛋白质', key: 'protein', unit: 'g' },
  { name: '碳水化合物', key: 'carbohydrate', unit: 'g' },
  { name: '胆固醇', key: 'cholesterol', unit: 'mg' },
  { name: '膳食纤维', key: 'dietary_fiber', unit: 'g' },
  { name: '钙', key: 'ca', unit: 'mg' },
  { name: '磷', key: 'p', unit: 'mg' },
  { name: '钾', key: 'k', unit: 'mg' },
  { name: '钠', key: 'na', unit: 'mg' },
  { name: '镁', key: 'mg', unit: 'mg' },
  { name: '铁', key: 'fe', unit: 'mg' },
  { name: '碘', key: 'i', unit: 'μg' },
  { name: '硒', key: 'zn', unit: 'μg' },
  { name: '锌', key: 'se', unit: 'mg' },
  { name: '铜', key: 'cu', unit: 'mg' },
  { name: '氟', key: 'f', unit: 'mg' },
  { name: '铬', key: 'cr', unit: 'μg' },
  { name: '钼', key: 'mo', unit: 'μg' },
  { name: '锰', key: 'mn', unit: 'mg' },
  { name: '维生素A', key: 'va', unit: 'μg' },
  { name: '维生素D', key: 'vd', unit: 'μg' },
  { name: '维生素E', key: 've', unit: 'mg' },
  { name: '维生素K', key: 'vk', unit: 'μg' },
  { name: '维生素B1', key: 'vb1', unit: 'mg' },
  { name: '维生素B2', key: 'vb2', unit: 'mg' },
  { name: '维生素B6', key: 'vb6', unit: 'mg' },
  { name: '维生素B12', key: 'vb12', unit: 'μg' },
  { name: '维生素C', key: 'vc', unit: 'mg' },
  { name: '泛酸', key: 'vb5', unit: 'mg' },
  { name: '叶酸', key: 'vm', unit: 'μg' },
  { name: '烟酸', key: 'vb3', unit: 'mg' },
  { name: '胆碱', key: 'choline', unit: 'mg' },
  { name: '烟酰胺', key: 'nicotinamide', unit: 'mg' },
  { name: '生物素', key: 'vh', unit: 'mg' }
]

export const localPermissionsTree = [
  {
    index: '1',
    key: 'home',
    verbose_name: '首页',
    is_menu: true,
    parent: null,
    level: 0,
    perm_type: 3,
    children: [
      {
        index: '1.1',
        key: 'application_center',
        verbose_name: '应用中心',
        is_menu: true,
        parent: '1',
        level: 1,
        perm_type: 3,
        children: []
      },
      {
        index: '1.2',
        key: 'home_page',
        verbose_name: '首页',
        is_menu: true,
        parent: '1',
        level: 1,
        perm_type: 3,
        children: []
      },
      {
        index: '1.3',
        key: 'jiaofei_data',
        verbose_name: '缴费数据',
        is_menu: true,
        parent: '1',
        level: 1,
        perm_type: 3,
        children: []
      }
    ]
  },
  {
    index: '2',
    key: 'order',
    verbose_name: '订单管理',
    is_menu: true,
    parent: null,
    level: 0,
    perm_type: 3,
    children: [
      {
        index: '2.1',
        key: 'order_management',
        verbose_name: '订单管理',
        is_menu: true,
        parent: '2',
        level: 1,
        perm_type: 3,
        children: [
          {
            index: '2.1.1',
            key: 'consumption_order',
            verbose_name: '消费订单',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.1.2',
            key: 'selling_container_order',
            verbose_name: '售货柜订单',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.1.3',
            key: 'refund_order',
            verbose_name: '退款订单',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.1.4',
            key: 'top_up_order',
            verbose_name: '充值订单',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.1.5',
            key: 'offline_debit_failed_order',
            verbose_name: '脱机扣款失败订单',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.1.6',
            key: 'withdrawal_order',
            verbose_name: '提现订单',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.1.7',
            key: 'order_evaluation',
            verbose_name: '订单评价',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          }
        ]
      },
      {
        index: '2.2',
        key: 'order_approval',
        verbose_name: '订单审批',
        is_menu: true,
        parent: '2',
        level: 1,
        perm_type: 3,
        children: [
          {
            index: '2.2.1',
            key: 'examination_and_approval_of_representations',
            verbose_name: '申述审批',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.2.2',
            key: 'cancel_order_approval',
            verbose_name: '取消订单审批',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.2.3',
            key: 'recharge_and_refund_approval',
            verbose_name: '充值退款审批',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.2.4',
            key: 'withdrawal_approval',
            verbose_name: '提现审批',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.2.5',
            key: 'visitor_meal_approval',
            verbose_name: '访客就餐审批',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '2.2.6',
            key: 'visitor_access_approval',
            verbose_name: '访客通行审批',
            is_menu: true,
            parent: '2.1',
            level: 2,
            perm_type: 3,
            children: []
          }
        ]
      }
    ]
  },
  {
    index: '3',
    key: 'dining_management',
    verbose_name: '用餐管理',
    is_menu: true,
    parent: null,
    level: 0,
    perm_type: 3,
    children: [
      {
        index: '3.1',
        key: 'food_product_management',
        verbose_name: '菜品/商品管理',
        is_menu: true,
        parent: '3',
        level: 1,
        perm_type: 3,
        children: [
          {
            index: '3.1.1',
            key: 'menu_management',
            verbose_name: '菜谱管理',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.2',
            key: 'food_store',
            verbose_name: '食材库',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.3',
            key: 'food_product_categories',
            verbose_name: '菜品/商品分类',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.4',
            key: 'dishes_merchandise_library',
            verbose_name: '菜品/商品库',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.5',
            key: 'package_management',
            verbose_name: '套餐管理',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.6',
            key: 'list_of_intended_recipes',
            verbose_name: '意向菜谱排行',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.7',
            key: 'food_sample_record',
            verbose_name: '菜品留样记录',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.8',
            key: 'Collective management',
            verbose_name: '集体管理',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          },
          {
            index: '3.1.9',
            key: 'Recipe rule setting',
            verbose_name: '菜谱规则设置',
            is_menu: true,
            parent: '3.1',
            level: 2,
            perm_type: 3,
            children: []
          }
        ]
      }
    ]
  }
]
